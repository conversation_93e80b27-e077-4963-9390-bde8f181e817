/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, { useState, useEffect } from "react";
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
} from "react-native";
import QRCodeScanner from "react-native-qrcode-scanner";
import { find, isEmpty, isObject, map } from "lodash";
import BleManager from "react-native-ble-manager";
import BluetoothStateManager from "react-native-bluetooth-state-manager";
import { useDispatch, useSelector } from "react-redux";
import Toast from "react-native-simple-toast";
import styles from "./styles";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { translate } from "../../lang/Translate";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import { sendErrorReport } from "../../utils/commonFunction";
import {
  check,
  openSettings,
  PERMISSIONS,
  request,
} from "react-native-permissions";

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const QRScanner = ({ navigation }) => {
  const dispatch = useDispatch();
  const token = useSelector((state) => state.auth.accessToken);
  const [isBleInitialized, setIsBleInitialized] = useState(false);
  let peripherals = new Map();
  const [list, setList] = useState([]);
  const { isBleConnected } = useSelector((state) => state.bluetooth);

  // const [connnectedID, setconnnectedID] = useState("");
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  // const [bluetoothStatus, setbluetoothStatus] = useState(false);

  const startScan = () => {
    if (!isScanning && !isBleConnected) {
      BleManager.scan([], 3, true)
        .then(() => {
          console.log("Scanning...");
          setIsScanning(true);
        })
        .catch((err) => {
          console.error(err);
        });
    }
  };

  useEffect(() => {
    // Initialize BLE Manager
    BleManager.start({ showAlert: false })
      .then(() => {
        console.log("BLE Manager initialized");
        setIsBleInitialized(true);
      })
      .catch((error) => {
        console.error("BLE Manager initialization failed:", error);
        sendErrorReport(error, "ble_init_error");
      });
  }, []);

  useEffect(() => {
    if (isBleInitialized) {
      console.log("BLE Manager initialized, starting scan in 2 seconds...");
      setTimeout(() => {
        startScan();
      }, 2000);
    }
  }, []);

  const handleStopScan = () => {
    console.log("Scan is stopped");
    setIsScanning(false);
    setisRefreshing(false);
  };

  const handleDiscoverPeripheral = (peripheral) => {
    if (peripheral.name && !isBleConnected) {
      console.log("Got ble peripheral", peripheral);
      peripherals.set(peripheral.id, peripheral);

      setList(Array.from(peripherals.values()));
    }
  };

  useEffect(() => {
    if (!isBleConnected) {
      BluetoothStateManager.getState().then((bluetoothState) => {
        switch (bluetoothState) {
          case "Unknown":
          case "Resetting":
          case "Unsupported":
          case "Unauthorized":
          case "PoweredOff":
            Toast.show(translate("turnOnBle"));
            if (Platform.OS === "android") {
              sendErrorReport(true, "requestToEnableHom");
              check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then((res) => {
                console.log("BLUETOOTH_CONNECT---", res);
                if (res === "granted") {
                  BluetoothStateManager.requestToEnable().then((result) => {
                    console.log(
                      "BluetoothStateManager.requestToEnable -> result",
                      result
                    );
                  });
                }
              });
              request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
                .then((result) => {
                  console.log("BLUETOOTH_CONNECT----1", result);
                })
                .then((statuses) => {
                  console.log(
                    "BLUETOOTH_CONNECT--2",
                    statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT]
                  );
                });
            } else {
              console.log("open settings====");
              BluetoothStateManager.openSettings();
            }
            break;
          case "PoweredOn":
            !isBleConnected ? startScan() : null;
            break;
          default:
            break;
        }
      });
    }
  }, []);

  useEffect(() => {
    if (!isBleConnected) {
      bleManagerEmitter.addListener(
        "BleManagerCentralManagerWillRestoreState",
        (data) => {
          console.log(
            "BLE ==> BleManagerCentralManagerWillRestoreState ===> ",
            data
          );
        }
      );

      bleManagerEmitter.addListener(
        "BleManagerDiscoverPeripheral",
        handleDiscoverPeripheral
      );
      bleManagerEmitter.addListener("BleManagerStopScan", handleStopScan);
      // bleManagerEmitter.addListener(
      //   "BleManagerDisconnectPeripheral",
      //   handleDisconnectedPeripheral
      // );

      if (Platform.OS === "android" && Platform.Version >= 23) {
        console.log("called---5");
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
        ).then((result) => {
          if (result) {
            console.log("Permission is OK");
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
              PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
              PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
            ).then((res) => {
              if (res) {
                console.log("User accept");
              } else {
                console.log("User refuse");
              }
            });
          }
        });
      }
    }

    return () => {
      console.log("unmount");
    };
  }, [isBleConnected]);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  const readData = async (item) => {
    // console.log("ddddddddd=========", item);
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getDevice,
        "POST",
        {
          device_bluetooth_name: item.name, //advertising.localName, // manndjdjfjdjfnfmansi chanfe name-------
          product_id: item?.id,
        },
        headers
      );
      console.log("get device=====", response);
      if (response.success && !isEmpty(response.data)) {
        console.log("DISPATCH 5");
        setDeviceId(response?.data?.id);
        dispatch(BluetoothAction.setConnectedDeviceDetail(response?.data));

        // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
        // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
        if (isObject(item) && !isEmpty(item)) {
          console.log("DISPATCH 5-2");
          dispatch(BluetoothAction.setDeviceID(""));
          setTimeout(() => {
            console.log("DISPATCH 5-3");

            dispatch(BluetoothAction.setLastDeviceId(item.id));
            dispatch(BluetoothAction.setDeviceID(item.id));
            navigation.navigate("Connect", {
              product_id: item?.id,
              device_id: response.data.id || deviceId,
              product_id: item?.id,
            });
          }, 500);
        } else {
          setIsScanning(false);

          Toast.show("Can't find any device. Please try again");
          sendErrorReport(
            JSON.stringify({
              item,
              responseFromApi: response.data,
            }),
            "on_read_qr_cant_find_2"
          );

          startScan();
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("error device detail ===", error);
      sendErrorReport(error, "on_read_qr");
    }
  };

  const renderItem = ({ item }) => (
    // console.log("renderItem -> item", list);
    <View
      style={{
        padding: 12,
        borderRadius: 8,
        backgroundColor: "#fff",
        shadowColor: "#000",
        margin: 8,
        sshadowColor: "#000",
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 8,
        }}
      >
        {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
        <Text
          style={{
            fontSize: 18,
            marginStart: 8,
            flex: 1,
            fontWeight: "700",
          }}
        >
          {item?.name == "NO NAME" ? "N/A" : item?.name}
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor:
              item?.advertising?.isConnectable == 1 ? "green" : "green",
            padding: 8,
            borderRadius: 8,
          }}
          activeOpacity={0.7}
          onPress={() => {
            readData(item);
            // navigation.navigate('Dashboard');
          }}
        >
          <Text style={{ color: "#fff" }}>CONNECT</Text>
        </TouchableOpacity>
      </View>
      <View style={{ flexDirection: "row" }}>
        {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>Sevice ID: </Text> */}
        <Text style={{ fontSize: 15, marginStart: 8, flex: 1 }}>
          {item?.id}
        </Text>
      </View>
    </View>
  );

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const onRefresh = () => {
    setisRefreshing(true);
    BleManager.stopScan().then(() => {
      // Success code
      console.log("Scan stopped");
      peripherals = new Map();
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  return (
    <View style={styles.root}>
      <GradientBack />
      <CHeader
        title={translate("addDeviceTitle")}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <View style={{ flex: 1 }}>
        <FlatList
          data={list}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ flexGrow: 1 }}
          onRefresh={onRefresh}
          refreshing={isRefreshing}
          ListEmptyComponent={() => (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ fontWeight: "bold", color: "#fff" }}>
                No Device Available
              </Text>
            </View>
          )}
        />
      </View>
      <View style={styles.bottomViewStyle}>
        <View style={styles.linkViewStyle}>
          <Text style={styles.linkTextStyle}>{translate("qrLinkText")}</Text>
        </View>
      </View>
    </View>
  );
};

export default QRScanner;
