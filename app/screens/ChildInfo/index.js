/* eslint-disable global-require */
/* eslint-disable operator-linebreak */
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Modal,
  ActivityIndicator,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Dimensions,
  Keyboard,
  Alert,
} from "react-native";
// import ImageCropPicker, {openPicker} from "react-native-image-crop-picker";
import Toast from "react-native-simple-toast";
import { useSelector, useDispatch } from "react-redux";
import RNFetchBlob from "rn-fetch-blob";
import Swiper from "react-native-deck-swiper";
import { SliderBox } from "react-native-image-slider-box";
import { findIndex, isEmpty, isObject, isUndefined } from "lodash";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  event,
} from "react-native-reanimated";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import CountryPicker from "react-native-country-picker-modal";
import CInput from "../../components/CInput";
import GradientBack from "../../components/gradientBack";
import styles from "./styles";
import { CustomIcon } from "../../config/LoadIcons";
import CHeader from "../../components/CHeader";
import CButton from "../../components/CButton";
import DropDown from "../../components/DropDown";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import SettingsHeader from "../../components/CHeader/SettingsHeader";
import { FontFamily } from "../../config/typography";
import DateTimePicker from "@react-native-community/datetimepicker";
import moment from "moment";
import { compose } from "redux";
import { usePermissions } from "../../utils/hooks";
import { RESULTS } from "react-native-permissions";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";

const { height: dHeight, width: dWidth } = Dimensions.get("window");
const ChildInfo = ({ navigation, route }) => {
  const { requestCameraAccess, requestGalleryAccess } = usePermissions();

  const childID = route?.params?.item?.id;
  const dispatch = useDispatch();
  const { characteristicID, serviceID } = useSelector(
    (state) => state.bluetooth
  );
  const [inViewPort, setInViewPort] = useState(0);
  const deviceList = route?.params;
  const [index, setIndex] = useState(0);
  const [open, setOpen] = useState(false);
  const [date, setDate] = useState(new Date());
  const [dateSelected, setDateSelected] = useState(false);

  const [mode, setmode] = useState("date");
  const [show, setshow] = useState(false);
  const [text, setText] = useState("Empty");

  const onChange = (event, selectedDate) => {
    setshow(false);
    const currentDate = selectedDate || new Date();
    const update_date = moment(currentDate).format("DD/MM/YYYY");
    setstate({ ...state, dob: update_date });
    setDateSelected(true);
  };
  const ShowDatePickerModal = () => {
    setshow(true);
    setstate({
      ...state,
      dob: state.dob ? moment(state.dob, "DD/MM/YYYY").toDate() : new Date(),
    });
    // setDateSelected(false);
  };

  const colors = useTheme();
  const BaseColor = colors.colors;

  const imageAnim = useSharedValue(0);
  const infoAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const imageAnimationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withTiming(imageAnim.value, {
          duration: 1000,
        }),
      },
    ],
  }));

  const infoStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(infoAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    imageAnim.value = 1;
    opacityAnim.value = 1;
    infoAnim.value = -300;
  }, []);

  // const userData = useSelector((state) => state.auth.userData);
  const token = useSelector((state) => state.auth.accessToken);

  const type = route?.params?.type;
  // console.log("ChildInfo -> type", type);

  const [state, setstate] = useState({
    isImage: "",
    nickName: "",
    height: "",
    weight: "",
    conatactname: "",
    conatactnumber: "",
    selectedCountry: "US",
    countryCode: "1",
    imageBase64: "",
    about: "",
    dob: "",
    updateImage: "",
    updateImageBase64: "",
    id: null,
  });

  const stateRef = useRef(null);
  stateRef.current = state;

  const [id, setId] = useState(null);

  const [cardData, setCardData] = useState([
    {
      type: "add",
    },
  ]);

  const [nickNameError, setNickNameError] = useState(false);
  const [dobError, setDobError] = useState(false);
  const [heightError, setHeightError] = useState(false);
  const [weightError, setWeightError] = useState(false);
  const [photoLoading, setphotoLoading] = useState(false);
  const [conatactnameError, setconatactnameError] = useState(false);
  const [conatactnumberError, setconatactnumberError] = useState(false);

  const [nickNameErrorTxt, setNickNameErrorTxt] = useState("");
  const [dobErrorTxt, setDobErrorTxt] = useState("");
  const [conatactnameErrorTxt, setconatactnameErrorTxt] = useState("");
  const [conatactnumberErrorTxt, setconatactnumberErrorTxt] = useState("");
  const [loader, setloader] = useState(false);
  const [selectChild, setSelectChild] = useState({
    type: "add",
  });

  const nameRef = useRef();
  const aboutRef = useRef();
  const contactnameRef = useRef();
  const contactnumberRef = useRef();

  const heightData = [];
  const weigthData = [];

  for (let i = 70; i <= 150; i++) {
    heightData.push({
      value: i,
    });
  }

  for (let i = 8; i <= 30; i++) {
    weigthData.push({
      value: i,
    });
  }

  const gender = [
    {
      value: "MALE",
      id: 1,
      icon: "avatar",
    },
    {
      value: "FEMALE",
      id: 2,
      icon: "woman",
    },
  ];

  const [selectedGender, setselectedGender] = useState({
    value: "MALE",
    id: 1,
    icon: "avatar",
  });
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date) => {
    console.warn("A date has been picked: ", date);
    hideDatePicker();
  };
  const Validation = () => {
    enableAnimateInEaseOut();
    const numVal = /^[0-9]+$/;
    if (isEmpty(state.nickName)) {
      allErrorFalse();
      setNickNameError(true);
      setNickNameErrorTxt(translate("enternickname"));
      console.log("1st");
    } else if (isEmpty(state.dob.toString())) {
      allErrorFalse();
      setDobError(true);
      setDobErrorTxt(translate("selectdob"));
      console.log("2nd");
    }
    // else if (isEmpty(state.height)) {
    //   allErrorFalse();
    //   Toast.show(translate("selectheight"), Toast.SHORT);
    // } else if (isEmpty(state.weight)) {
    //   allErrorFalse();
    //   Toast.show(translate("selectweight"), Toast.SHORT);
    // }
    else if (isEmpty(state.conatactname)) {
      allErrorFalse();
      setconatactnameError(true);
      setconatactnameErrorTxt("Please enter contact name");
      console.log("3rd");
    } else if (isEmpty(state.conatactnumber)) {
      allErrorFalse();
      setconatactnumberError(true);
      setconatactnumberErrorTxt("Please enter contact number");
      console.log("4th");
    } else if (
      !numVal.test(String(state.conatactnumber)) ||
      state.conatactnumber.length < 6 ||
      state.conatactnumber.length > 12
    ) {
      allErrorFalse();
      setconatactnumberError(true);
      setconatactnumberErrorTxt("Please enter valid contact number");
      console.log("5th");
    } else if (isEmpty(state.imageBase64) && isEmpty(state.updateImage)) {
      allErrorFalse();
      Toast.show(translate("addprofilepicture"), Toast.SHORT);
      console.log("6th");
    } else {
      console.log("CHILD PROFILE");
      allErrorFalse();
      childProfile();
    }
    // alert("done");
  };

  const allErrorFalse = () => {
    // setIsImage(false);
    setNickNameError(false);
    setHeightError(false);
    setWeightError(false);
    setDobError(false);
    setconatactnameError(false);
    setconatactnumberError(false);
  };

  // this function for add use product
  async function addUserProduct(data) {
    const obj = {
      app_name: "cbtsmartcar",
      child_id: data?.id,
      device_id: route?.params?.device_id,
      product_id: route?.params?.product_id,
      service_id: serviceID,
      characteristic_id: characteristicID,
      type: "type 1",
      platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
    };
    sendErrorReport(obj, "add_product_obj");
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.addUserProduct,
        "POST",
        obj,
        headers
      );
      sendErrorReport(response, "add_product_response");
      if (response.success) {
        setstate({ id: null });
        navigation.navigate("DrawerNav");
      }
    } catch (error) {
      console.log("error ===", error);
      sendErrorReport(error, "add_user_prod");
    }
  }

  const childProfile = (sId) => {
    setloader(true);
    const data = {
      app_name: "cbtsmartcar",
      nick_name: state.nickName,
      date_of_birth: state.dob,
      height: "83",
      weight: "16",
      gender: "male",
      about: state.about || "",
      emergency_name: state.conatactname,
      emergency_phone: state.conatactnumber,
      emergency_phone_code: state.countryCode.includes("+")
        ? state.countryCode
        : `+${state.countryCode}`,
      country: state.selectedCountry,
      child_profile:
        state.updateImage !== "" && state?.id
          ? state.updateImageBase64 || state.updateImage
          : state.imageBase64,
      platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
    };
    if (state?.id) {
      data.child_id = state?.id;
    }
    if (route?.params?.device_id) {
      data.device_id = route?.params?.device_id;
    }
    if (route?.params?.product_id) {
      data.product_id = route?.params?.product_id;
    }

    sendErrorReport(data, "child_profile_data");

    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    let url;
    if (state?.id) {
      url = BaseSetting.endpoints.updateChild;
    } else {
      url = BaseSetting.endpoints.childProfile;
    }
    console.log("data=====", data);
    getApiData(url, "POST", data, headers)
      .then((response) => {
        sendErrorReport(response, "child_profile_response");
        sendErrorReport(route, "child_profile_route_data");
        if (response.success) {
          setstate({
            isImage: "",
            nickName: "",
            dob: "",
            about: "",
            height: "",
            weight: "",
            imageBase64: "",
            updateImage: "",
            conatactname: "",
            conatactnumber: "",
            selectedCountry: "US",
            countryCode: "1",
          });

          if (isObject(response.data) && !isEmpty(response.data)) {
            dispatch(BluetoothAction.setActiveChildDetail(response.data));
          }

          if (
            route?.params?.product_id &&
            // state?.id === null && //! FIX: REMOVED THIS FOR DEVICE LIST NOT UPDATING ISSUE
            type === "connect"
          ) {
            if (isObject(response.data) && !isEmpty(response.data)) {
              addUserProduct(response.data);
              sendErrorReport(response.data, "child_profile_response_data");
            }
            setloader(false);
          } else {
            setloader(false);
            sendErrorReport("true", "navigation_child_profile");
            navigation.navigate("DrawerNav");
          }
        } else {
          sendErrorReport(response, "child_profile_response_false");
          console.log("profile insert response:", response.message);
          Toast.show(response.message);
        }

        setloader(false);
      })
      .catch((err) => {
        console.log("ERRR", err);
        Toast.show("Something went wrong! Unable to save child profile");
        sendErrorReport(err, "add_update_child_profile_1");
        setloader(false);
      });
  };

  useFocusEffect(
    useCallback(() => {
      setCardData([
        {
          type: "add",
        },
      ]);
      getChildInfo();
    }, [])
  );

  const getChildInfo = () => {
    setphotoLoading(true);
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    setCardData([
      {
        type: "add",
      },
    ]);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      "POST",
      {
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          const tempArr = [
            {
              type: "add",
            },
          ];
          const childArr = response.data;

          childArr.map((item) => {
            tempArr.splice(1, 0, item);
          });

          setCardData(tempArr);
        } else {
          Toast.show(response.message);
        }
        setphotoLoading(false);
      })
      .catch((err) => {
        console.log("error user child", err);
        Toast.show("Something went wrong while getting child information");
        sendErrorReport(err, "get_child_profile");
        setphotoLoading(false);
      });
  };

  const openCamera = async () => {
    try {
      const result = await requestGalleryAccess();
      if (result && result === RESULTS?.GRANTED) {
        const data = await launchImageLibrary({ mediaType: "photo" });
        console.log("data", data);
      } else {
        console.log("DENIED");
        return null;
      }
    } catch (error) {
      console.log("error ", error);
    }
  };

  const image = async (item) => {
    try {
      const result = await requestGalleryAccess();
      if (result && result === RESULTS?.GRANTED) {
        const img = await launchImageLibrary({ mediaType: "photo" });
        console.log("img", img);
        let filePath = img.assets[0].uri;

        // filePath = filePath.slice(5);

        RNFetchBlob.fs
          .readFile(filePath, "base64")
          .then((data) => {
            item.type === "add"
              ? setstate((prevState) => ({
                  ...prevState,
                  ...stateRef?.current,
                  isImage: filePath,
                  imageBase64: `data:image/png;base64,${data}`,
                }))
              : setstate((prevState) => ({
                  ...prevState,
                  ...stateRef?.current,
                  updateImage: filePath,
                  updateImageBase64: `data:image/png;base64,${data}`,
                }));

            setTimeout(() => {
              setphotoLoading(false);
            }, 2000);
          })
          .catch((err) => {
            sendErrorReport(err, "image_fs");
            setphotoLoading(false);
          });
      } else {
        setphotoLoading(false);
      }
    } catch (error) {
      setphotoLoading(false);
    }
    // setphotoLoading(true);
    // ImagePicker.openPicker({
    //   width: 250,
    //   height: 250,
    //   cropping: true,
    //   mediaType: 'photo'
    // })
    //   .then((img) => {
    //     console.log('img ', img)
    //     RNFetchBlob.fs
    //       .readFile(img.path, "base64")
    //       .then((data) => {
    //         item.type === "add"
    //           ? setstate({
    //               ...stateRef?.current,
    //               isImage: img.path,
    //               imageBase64: `data:image/png;base64,${data}`,
    //             })
    //           : setstate({
    //               ...stateRef?.current,
    //               updateImage: img.path,
    //               updateImageBase64: `data:image/png;base64,${data}`,
    //             });
    //         setTimeout(() => {
    //           setphotoLoading(false);
    //         }, 2000);
    //       })
    //       .catch((err) => {
    //         sendErrorReport(err, "image_fs");
    //         setphotoLoading(false);
    //       });
    //   })
    //   .catch((err) => {
    //     console.log('err ', err)
    //     setphotoLoading(false);
    //   });
  };

  function handleBackButtonClick() {
    if (type !== "Otp") {
      navigation.navigate("DrawerNav");
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  const renderDevice = ({ item, index }) => {
    return (
      <View
        style={{
          flex: 1,
          alignSelf: "center",
          justifyContent: "center",
          alignItems: "center",
          width: dWidth * 0.7,
        }}
      >
        {item?.type === "add" ? (
          <TouchableOpacity
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 20,
              zIndex: 11,
              alignSelf: "center",
              borderWidth: selectChild?.type === "add" ? 2 : 0,
              borderColor: BaseColor.textGrey,
            }}
            activeOpacity={0.7}
            onPress={() => {
              setSelectChild(item);
              setstate({
                isImage: "",
                nickName: "",
                height: "",
                weight: "",
                conatactname: "",
                conatactnumber: "",
                selectedCountry: "US",
                countryCode: "1",
                imageBase64: "",
                about: "",
                dob: "",
                updateImage: "",
                updateImageBase64: "",
                id: null,
              });
            }}
          >
            {!isEmpty(state?.isImage) ? (
              <View
                style={{
                  width: dWidth / 1.7,
                  height: 250,
                  borderRadius: 20,
                }}
              >
                <Image
                  style={{
                    width: "100%",
                    height: "100%",
                    borderRadius: 20,
                  }}
                  source={{ uri: state?.isImage }}
                />
              </View>
            ) : (
              <View
                style={{
                  width: dWidth / 1.7,
                  height: 250,
                  justifyContent: "center",
                }}
              >
                <Text
                  style={[styles.imageText1, { color: BaseColor.blackColor }]}
                >
                  {translate("uploadPic")}
                </Text>
              </View>
            )}
            <TouchableOpacity
              activeOpacity={0.7}
              style={[
                styles.editIcon,
                {
                  borderColor:
                    selectChild?.id === item?.id
                      ? BaseColor.blue
                      : BaseColor.textGrey,
                },
              ]}
              onPress={() => image({ type: "add" })}
              disabled={selectChild?.type === "add" ? false : true}
            >
              <CustomIcon
                name="edit"
                size={22}
                color={
                  selectChild?.type === "add"
                    ? BaseColor.blue
                    : BaseColor.textGrey
                }
              />
            </TouchableOpacity>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            activeOpacity={0.7}
            style={{
              borderWidth: selectChild?.id === item?.id ? 2 : 0,
              borderColor: BaseColor.textGrey,
              borderRadius: 20,
              justifyContent: "center",
              alignItems: "center",
            }}
            onPress={() => {
              console.log("item--------", item);
              setstate((prevState) => ({
                ...prevState,
                id: item?.id,
                nickName: item?.nick_name,
                dob: item?.date_of_birth,
                about: item?.about,
                conatactname: item?.emergency_name,
                conatactnumber: item?.emergency_phone,
                countryCode: item?.emergency_phone_code,
                selectedCountry: item?.country,
                updateImage: item?.child_profile,
              }));
              setId(item?.id);
              setSelectChild(item);
            }}
          >
            <Image
              source={{
                uri:
                  id == cardData[index]?.id && state?.updateImage !== ""
                    ? state?.updateImage
                    : item?.child_profile,
              }}
              style={{
                width: Dimensions.get("screen").width / 1.7,
                height: 250,
                borderRadius: 20,
              }}
            />
            <TouchableOpacity
              activeOpacity={0.7}
              style={[
                styles.editIcon,
                {
                  borderColor:
                    selectChild?.id === item?.id
                      ? BaseColor.blue
                      : BaseColor.textGrey,
                },
              ]}
              onPress={() => image({ type: "update" })}
              disabled={selectChild?.id === item?.id ? false : true}
            >
              <CustomIcon
                name="edit"
                size={22}
                color={
                  selectChild?.id === item?.id
                    ? BaseColor.blue
                    : BaseColor.textGrey
                }
              />
            </TouchableOpacity>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
    waitForInteraction: true,
    minimumViewTime: 5,
  });

  let onViewableItemsChanged = React.useRef((cardIndex) => {
    const tempIndex = cardIndex?.viewableItems;
    console.log("tempindex======", tempIndex);
    if (tempIndex > -1) {
      tempIndex.map((item) => {
        setIndex(item?.index);
      });
    }
  });

  const scrollToIndex = (e) => {
    if (flatlist && flatlist?.current) {
      flatlist?.current?.scrollToIndex({
        index: arrayIndex,
        animated: true,
      });
    }
  };

  const arrayIndex = findIndex(cardData, (lt) => lt?.id === childID);
  useEffect(() => {
    if (arrayIndex > -1 && !isUndefined(childID)) {
      setTimeout(() => {
        setIndex(arrayIndex);
        scrollToIndex();
      }, 1000);
    }
  }, [arrayIndex]);

  useEffect(() => {
    // alert(`Child Profile`);
    if (index != 0) {
      setstate((prevState) => ({
        ...prevState,
        nickName: cardData[index]?.nick_name,
        dob: cardData[index]?.date_of_birth.toString(),
        weight: { value: cardData[index]?.weight },
        height: {
          value: cardData[index]?.height,
        },
        about: cardData[index]?.about,
        conatactname: cardData[index]?.emergency_name,
        conatactnumber: cardData[index]?.emergency_phone,
        selectedCountry: cardData[index]?.country,
        countryCode: cardData[index].emergency_phone_code,
        id: index == 0 ? null : cardData[index]?.id,
        imageBase64: cardData[index]?.child_profile,
      }));
      setSelectChild(cardData[index]);
      if (cardData[index]?.gender === "male") {
        setselectedGender({
          value: "MALE",
          id: 1,
          icon: "avatar",
        });
      } else {
        setselectedGender({
          value: "FEMALE",
          id: 2,
          icon: "woman",
        });
      }
    } else {
      setstate((prevState) => ({
        ...prevState,
        nickName: "",
        dob: "",
        weight: "",
        height: "",
        about: "",
        id: null,
        imageBase64: "",
        conatactname: "",
        conatactnumber: "",
        countryCode: "1",
        selectedCountry: "US",
      }));
      setselectedGender({
        value: "MALE",
        id: 1,
        icon: "avatar",
      });
    }
  }, [index]);

  const flatlist = useRef();

  return (
    <>
      <GradientBack />
      <KeyboardAvoidingView
        style={{
          flex: 1,
          // backgroundColor: BaseColor.whiteColor
        }}
        behavior={Platform.OS === "ios" ? "padding" : null}
      >
        <SettingsHeader
          image
          titleTextColor={
            route?.params?.backTitle
              ? BaseColor.blackColor
              : BaseColor.whiteColor
          }
          title2={type !== "edit" && translate("child")}
          title={
            type === "edit"
              ? route?.params?.title
                ? route?.params?.title
                : translate("editProfile")
              : translate("profile")
          }
          tit
          leftIconName="left-arrow"
          onLeftPress={() => {
            type === "edit"
              ? navigation.goBack()
              : navigation.navigate("DrawerNav");
          }}
          onRightPress={() => {
            if (state.id) {
              childProfile();
            } else {
              Validation();
            }
          }}
          backTitle={
            route?.params?.backTitle ? route?.params?.backTitle : "Back"
          }
          isViolateLogo={!!route?.params?.title}
          childInfo
          headerBackgroundColor={
            route?.params?.title
              ? BaseColor.inputBackGroundColor
              : BaseColor.primaryViolate
          }
        />
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <SafeAreaView style={[styles.root]}>
            <StatusBar backgroundColor="transparent" barStyle="light-content" />
            <View style={{ marginTop: 30 }} />
            <Animated.View
              style={{
                paddingVertical: 24,
                ...imageAnimationStyle,
              }}
            >
              {type === "edit" ? null : (
                <>
                  <Text
                    style={{
                      fontSize: 24,
                      fontFamily: FontFamily.regular,
                      marginLeft: 20,
                      color: BaseColor.blackColor,
                    }}
                  >
                    {translate("createNewProfile")}
                  </Text>
                  <Text
                    style={{
                      fontSize: 16,
                      fontFamily: FontFamily.regular,
                      marginLeft: 20,
                      marginTop: 8,
                      marginBottom: 16,
                      color: BaseColor.blackColor,
                    }}
                  >
                    {translate("whyNecessary")}
                  </Text>
                </>
              )}
              {/* <Text
                style={[styles.chooseProfile, { color: BaseColor.blackColor }]}
              >
                {translate("chooseProfileText")}
              </Text> */}
              {route?.params?.type === "Otp" ? (
                <TouchableOpacity
                  activeOpacity={1}
                  style={{
                    backgroundColor: BaseColor.whiteColor,
                    justifyContent: "center",
                    alignItems: "center",
                    alignSelf: "center",
                    borderRadius: 20,
                    zIndex: 11,
                    width: Dimensions.get("screen").width / 1.5,
                    height: 250,
                    marginBottom: 20,
                  }}
                  onPress={() => image({ type: "add" })}
                >
                  {state.isImage != "" ? (
                    <View>
                      <Image
                        style={{
                          width: Dimensions.get("screen").width / 1.5,
                          height: 250,
                          borderRadius: 20,
                        }}
                        source={{ uri: state.isImage }}
                      />
                    </View>
                  ) : (
                    <View>
                      <CustomIcon
                        name="insert-picture-icon"
                        size={36}
                        color={BaseColor.blueLight}
                        style={{ textAlign: "center" }}
                      />
                      <Text
                        style={[
                          styles.imageText,
                          { color: BaseColor.blueDark },
                        ]}
                      >
                        {translate("addNew")}
                      </Text>
                      <Text
                        style={[
                          styles.imageText1,
                          { color: BaseColor.textGrey },
                        ]}
                      >
                        {translate("tapToAddText")}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ) : (
                <FlatList
                  ref={flatlist}
                  data={cardData}
                  renderItem={renderDevice}
                  horizontal
                  contentContainerStyle={{
                    marginBottom: 20,
                    paddingHorizontal: 35,
                  }}
                  showsHorizontalScrollIndicator={false}
                  pagingEnabled={true}
                  keyExtractor={(item) => item?.id}
                  snapToAlignment="center"
                  snapToInterval={Dimensions.get("screen").width / 1.5}
                  viewabilityConfig={viewabilityConfig?.current}
                  onViewableItemsChanged={onViewableItemsChanged?.current}
                />
              )}
            </Animated.View>
            <View
              style={{
                backgroundColor: BaseColor.whiteColor,
                height: 5,
                width: dWidth,
              }}
            />
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "flex-end",
              }}
            >
              <Animated.View
                style={[
                  styles.infoView,
                  { top: 300, backgroundColor: BaseColor.inputBackGroundColor },
                  infoStyleAnim,
                ]}
              >
                {/* <View
                  style={[
                    styles.horizontalLine,
                    { backgroundColor: BaseColor.black30 },
                  ]}
                /> */}
                <Text
                  style={[styles.infoText, { color: BaseColor.blackColor }]}
                >
                  {translate("infoTitle")}
                </Text>
                {/* <View style={styles.textInputView}> */}
                <View style={{ flex: 1, marginBottom: 10 }}>
                  <CInput
                    ref={nameRef}
                    placeholder={translate("nickName")}
                    hideLeftIcon
                    placeholderTextColor="#000000"
                    inputStyle={{
                      color: BaseColor.bColor,
                      //backgroundColor: BaseColor.whiteColor,
                    }}
                    textInputWrapper={{
                      borderRadius: 12,
                      // marginEnd: 4,
                      backgroundColor: "#fff",
                    }}
                    value={state.nickName}
                    onChangeText={(val) => {
                      setstate({ ...state, nickName: val });
                    }}
                    onSubmitEditing={() => {
                      Keyboard.dismiss();
                    }}
                    showError={nickNameError}
                    errorMsg={nickNameErrorTxt}
                  />
                </View>
                {/* <TouchableOpacity onPress={() => showDatePicker()}>
                  <Text>BirthDay</Text>
                </TouchableOpacity> */}
                {/* <DateTimePickerModal
                  isVisible={isDatePickerVisible}
                  mode="date"
                  onConfirm={handleConfirm}
                  onCancel={hideDatePicker}
                /> */}
                <View
                  style={{
                    flex: 1,
                    paddingBottom: nickNameError ? 22 : 0,
                    marginBottom: 10,
                  }}
                >
                  <TouchableOpacity
                    style={styles.date_picker}
                    onPress={() => ShowDatePickerModal()}
                  >
                    <Text style={styles.date_input}>
                      {state.dob !== "" || dateSelected
                        ? `${state.dob}`
                        : translate("dob")}
                    </Text>
                    {show && (
                      <DateTimePicker
                        testID="dateTimePicker"
                        value={state.dob ? new Date(state.dob) : new Date()}
                        mode={mode}
                        display={
                          Platform.OS == "android" ? "calendar" : "compact"
                        }
                        onChange={onChange}
                      />
                    )}
                    {/* <CInput
                      placeholder={translate("dob")}
                      // floatingPlaceholder={state.dob ? true : false}
                      inputBackGroundColor={BaseColor.whiteColor}
                      hideLeftIcon
                      placeholderTextColor="#000000"
                      textInputWrapper={{
                        backgroundColor: "#fff",
                        borderRadius: 12,
                      }}
                      value={state.dob}
                      DateTimePicker
                      onDateChange={(val) => {
                        // console.log("DOB???", val, typeof val);
                        setstate({ ...state, dob: val });
                      }}
                      showError={dobError}
                      errorMsg={dobErrorTxt}
                      iconName="pencil"
                      hideRightIcon={type === "edit" ? isEdit : true}
                    /> */}
                  </TouchableOpacity>
                </View>
                {/* <View
                  style={{
                    flex: 1,
                    paddingBottom: nickNameError ? 22 : 0,
                    marginBottom: 10,
                  }}
                >
                  <CInput
                    placeholder={translate("dob")}
                    hideLeftIcon
                    placeholderTextColor="#000000"
                    selectColor="#000"
                    textInputWrapper={{
                      borderRadius: 12,
                      // marginStart: 4,
                      backgroundColor: "#fff",
                    }}
                    value={state.dob}
                    // datePicker
                    onDateChange={(val) => {
                      // console.log("DOB???", val, typeof val);
                      setstate({ ...state, dob: val });
                    }}
                    showError={dobError}
                    errorMsg={dobErrorTxt}
                  />
                </View> */}
                <View style={{ marginBottom: 8 }}>
                  <CInput
                    ref={aboutRef}
                    placeholder="FEW WORDS ABOUT ME"
                    hideLeftIcon
                    multiline
                    maxLength={150}
                    placeholderTextColor="#000000"
                    inputheight={"100%"}
                    inputStyle={{
                      color: BaseColor.bColor,
                      lineHeight: 18,
                      textAlignVertical: "top",
                    }}
                    textInputWrapper={{
                      borderRadius: 12,
                      backgroundColor: "#fff",
                      // overflow: "hidden",
                      height: 80,
                    }}
                    value={state.about}
                    onChangeText={(val) => {
                      setstate({ ...state, about: val });
                    }}
                  />
                </View>
                <View style={styles.textInputView}>
                  {/* <CInput
                placeholder="HEIGHT"
                hideLeftIcon
                placeholderTextColor={BaseColor.placeHolderColor}
                textInputWrapper={{
                  borderRadius: 12,
                  flex: 1,
                  marginEnd: 4,
                }}
                /> */}
                  {/* <DropDown
                    placeholder={translate("height")}
                    data={heightData}
                    backGround
                    backColor="#fff"
                    textColor="#bdbdbd"
                    selectColor="#000"
                    style={{ borderRadius: 12, flex: 1, marginEnd: 4 }}
                    valueProp="value"
                    onSelect={(val) => {
                      // setHeight(val);
                      setstate({ ...state, height: val });
                    }}
                    selectedObject={state.height}
                    showError={heightError}
                  /> */}
                  {/* <DropDown
                    placeholder={translate("weight")}
                    backGround
                    backColor="#fff"
                    textColor="#bdbdbd"
                    selectColor="#000"
                    data={weigthData}
                    style={{ borderRadius: 12, flex: 1, marginStart: 4 }}
                    valueProp="value"
                    onSelect={(val) => {
                      // setWeight(val);
                      setstate({ ...state, weight: val });
                    }}
                    selectedObject={state.weight}
                    showError={weightError}
                  /> */}
                  {/* <CInput
                placeholder="c"
                hideLeftIcon
                placeholderTextColor={BaseColor.placeHolderColor}
                textInputWrapper={{
                  borderRadius: 12,
                  flex: 1,
                  marginStart: 4,
                }}
                /> */}
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                  }}
                >
                  {gender.map((item) => (
                    <View
                      style={{
                        flex: 1,
                        marginHorizontal: 8,
                      }}
                    >
                      <TouchableOpacity
                        style={[styles.iconView]}
                        activeOpacity={0.7}
                        onPress={() => {
                          setselectedGender(item);
                        }}
                      >
                        {/* <View
                          style={{
                            ...styles.genderIcon,
                            backgroundColor:
                              item.id === selectedGender.id
                                ? colors.colors.blue
                                : colors.colors.blue,
                            borderColor: colors.colors.whiteColor,
                          }}
                        >
                          <CustomIcon
                            name={item.icon}
                            size={48}
                            color={
                              item.id === selectedGender.id
                                ? colors.colors.whiteColor
                                : colors.colors.whiteColor
                            }
                          />
                        </View> */}

                        {/* {item.id === selectedGender.id ? (
                          <View
                            style={{
                              ...styles.selectedCheck,
                              backgroundColor: colors.colors.blue,
                              // borderColor: colors.colors.whiteColor,
                            }}
                          >
                            <CustomIcon
                              name="check"
                              size={12}
                              color={colors.colors.whiteColor}
                            />
                          </View>
                        ) : null} */}

                        {/* <Text
                          style={[
                            styles.genderName,
                            { color: BaseColor.whiteColor },
                          ]}
                        >
                          {item.value}
                        </Text> */}
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
                <Text
                  style={[
                    styles.infoText,
                    { color: BaseColor.blackColor, paddingTop: 24 },
                  ]}
                >
                  {translate("emergencycontact")}
                </Text>
                <View style={{ ...styles.textInputView, marginTop: 5 }}>
                  <View style={{ flex: 1 }}>
                    <CInput
                      ref={contactnameRef}
                      placeholder={translate("contactname")}
                      hideLeftIcon
                      placeholderTextColor="#000000"
                      inputStyle={{ color: BaseColor.bColor }}
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        backgroundColor: "#fff",
                      }}
                      value={state.conatactname}
                      onChangeText={(val) => {
                        setstate({
                          ...state,
                          conatactname: val.replace(/[0-9]/g, ""),
                        });
                      }}
                      onSubmitEditing={() => {
                        contactnumberRef.current.focus();
                      }}
                      showError={conatactnameError}
                      errorMsg={conatactnameErrorTxt}
                    />
                  </View>
                </View>
                <View style={{ ...styles.textInputView, marginBottom: 20 }}>
                  <View
                    style={{
                      position: "absolute",
                      flexDirection: "row",
                      alignItems: "center",
                      zIndex: 1,
                      top: 12,
                      left: 10,
                    }}
                  >
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <CountryPicker
                        {...{
                          countryCode: state.selectedCountry || "US",
                          withFilter: true,
                          withFlag: true,
                          withAlphaFilter: true,
                          withCallingCode: true,
                          withEmoji: true,
                          disabled: true,
                          onSelect: (val) => {
                            setstate({
                              ...state,
                              selectedCountry: val.cca2,
                              countryCode: val.callingCode[0],
                            });
                          },
                          theme: {
                            fontSize: 16,
                            onBackgroundTextColor: BaseColor.whiteColor,
                            primaryColor: BaseColor.alertRed,
                            backgroundColor: BaseColor.blueDark,
                            filterPlaceholderTextColor: BaseColor.white80,
                          },
                          // onOpen: null,
                          // onClose: () => {
                          //   // onClose();
                          // },
                        }}
                        visible={state.countryCode}
                      />
                      <CustomIcon
                        name="expand-button"
                        size={14}
                        color="#bdbdbd"
                      />
                    </View>
                  </View>
                  <View style={{ flex: 1 }}>
                    <CInput
                      ref={contactnumberRef}
                      placeholder={translate("contactnumber")}
                      hideLeftIcon
                      keyboardType="number-pad"
                      // placeholderTextColor={BaseColor.placeHolderColor}
                      // textInputWrapper={{
                      //   borderRadius: 12,
                      //   marginEnd: 4,
                      //   paddingLeft: 50,
                      // }}
                      placeholderTextColor="#000000"
                      inputStyle={{ color: BaseColor.bColor }}
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        backgroundColor: "#fff",
                        paddingLeft: 50,
                        marginBottom: 20,
                      }}
                      value={state.conatactnumber}
                      onChangeText={(val) => {
                        setstate({ ...state, conatactnumber: val });
                      }}
                      onSubmitEditing={() => {
                        Keyboard.dismiss();
                      }}
                      showError={conatactnumberError}
                      errorMsg={conatactnumberErrorTxt}
                    />
                    <CButton
                      style={styles.loginBtn}
                      titleStyle={{ color: BaseColor.whiteColor }}
                      onPress={Validation}
                      title={translate("complete")}
                      loader={loader}
                      // done={done}
                    />
                  </View>
                </View>
              </Animated.View>
            </Animated.View>
          </SafeAreaView>
        </ScrollView>
      </KeyboardAvoidingView>
      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: BaseColor.black30,
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: "center",
              alignItems: "center",
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}
          >
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: "bold",
                marginTop: 8,
              }}
            >
              Loading
            </Text>
          </View>
        </View>
      </Modal>
    </>
  );
};
export default ChildInfo;
