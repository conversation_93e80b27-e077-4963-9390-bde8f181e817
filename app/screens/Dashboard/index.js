/* eslint-disable implicit-arrow-linebreak */
/* eslint-disable no-console */
/* eslint-disable global-require */
/* eslint-disable quotes */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  ActivityIndicator,
  BackHandler,
  Image,
  Modal,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Animated as NativeAnimated,
  Dimensions,
  AppState,
  ImageBackground,
} from "react-native";
import Toast from "react-native-simple-toast";
import { VERSION, findIndex, has, isEmpty, isObject } from "lodash";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import MCIcon from "react-native-vector-icons/MaterialCommunityIcons";
import { useDispatch, useSelector } from "react-redux";
import SVGBatteryOut from "../../assets/images/battrayOutline.svg";
import SVGBatteryRed from "../../assets/images/battrayOutlineRed.svg";
import SVGBatteryW from "../../assets/images/SVGBatteryW.svg";
import SVGbabyWhite from "../../assets/images/babyWhite.svg";
import BluetoothStateManager from "react-native-bluetooth-state-manager";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import styles from "./styles";
import BluetoothAction from "../../redux/reducers/bluetooth/actions";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import BaseColorMain from "../../config/colors";
import PushNotification from "react-native-push-notification";
import {
  sendErrorReport,
  sensorColor,
  sensorColorfnc,
} from "../../utils/commonFunction";
import AuthActions from "../../redux/reducers/auth/actions";
import moment from "moment";
import { CustomIcon } from "../../config/LoadIcons";
import GestureRecognizer, {
  swipeDirections,
} from "react-native-swipe-gestures";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
  interpolate,
} from "react-native-reanimated";
import CButton from "../../components/CButton";
import BackgroundTimer from "react-native-background-timer";
import { SafeAreaView } from "react-native-safe-area-context";
import { BlurView } from "@react-native-community/blur";
import LinearGradient from "react-native-linear-gradient";
import CAlert from "../../components/CAlert";
import DashboardNav from "./dashboardNav";
import { check, PERMISSIONS, request } from "react-native-permissions";

let backPressed = 0;
const nWidth = Dimensions.get("window").width;
const Dashboard = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const peripherals = new Map();

  const dispatch = useDispatch();
  const {
    bleData,
    connectedDeviceDetail,
    activeChildDetail,
    connectedDeviceId,
    emergencyAlert,
    isBleConnected,
    alertTime,
    lowAlert,
    highAlert,
    tempModal,
    tempAvg,
    humidityAvg,
    isCurrentActiveDevice,
    activeDeviceId,
    isAlert,
    alertData,
    lastDeviceId,
    connectedDeviceList,
  } = useSelector((state) => state.bluetooth);

  const {
    setDeviceID,
    setConnectedDeviceDetail,
    setLastDeviceId,
    setActiveChildDetail,
  } = BluetoothAction;

  const { accessToken, isFarenheit, isCRASH } = useSelector(
    (state) => state.auth
  );

  const { setIsCRASH } = AuthActions;
  const animatedValue = useRef(new NativeAnimated.Value(0)).current;
  const animatedValueRing = useRef(new NativeAnimated.Value(0)).current;
  const animatedValue1 = useRef(new NativeAnimated.Value(0)).current;

  const bleNotConnected = require("../../assets/images/bleNotConnected.png");
  const warning = require("../../assets/images/warning.png");

  const show3NumWithDots = require("../../assets/images/three_front_correct_dots.png");
  const show3Num = require("../../assets/images/three_front_correct.png");
  const show4num = require("../../assets/images/four_rear_correct.png");
  const show5num = require("../../assets/images/five.png");
  const show6 = require("../../assets/images/six_rotate_rear.png");
  const show11num = require("../../assets/images/eleven.png");
  const show13num = require("../../assets/images/thirteen.png");
  const show14num = require("../../assets/images/fourteen.png");
  const show15num = require("../../assets/images/show15num.png");
  const show16num = require("../../assets/images/sixteen.png");
  const twoHoriArrow = require("../../assets/images/twoHorizontalArrow.png");
  const upSwing = require("../../assets/images/up_swing.png");
  const show9DownSwing = require("../../assets/images/nine_down_swing.png");
  const show9HeadrestUp = require("../../assets/images/nine_headrest_up.png");
  const show12LeftISO = require("../../assets/images/twelve_left_ISOFix.png");

  const [showAlert, setShowAlert] = useState(false);
  const [bgImage, setBgImage] = useState(false);
  const [hideHWT, setHideHWT] = useState(false);
  // let parseBleData =
  //   bleData && !isEmpty(bleData) && !isObject(bleData)
  //     ? JSON.parse(bleData)
  //     : {};

  const parseBleData = useMemo(
    () =>
      bleData && !isEmpty(bleData) && !isObject(bleData)
        ? JSON.parse(bleData)
        : {},
    [bleData]
  );
  // console.log("parseble====", parseBleData);
  const batteryVoltage =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.BV
      ? parseBleData.BV
      : parseBleData.bv;
  let SW =
    isObject(parseBleData) && !isEmpty(parseBleData) && has(parseBleData, "SW")
      ? parseBleData.SW
      : parseBleData.s1;
  const temp =
    isObject(parseBleData) &&
    !isEmpty(parseBleData) &&
    parseBleData?.Temperature
      ? parseBleData.Temperature
      : parseBleData.TEMP;
  const nowTime = moment();
  const durationTemp = moment.duration(nowTime.diff(alertTime.temp));

  const [powerDetail, setPowerDetail] = useState({
    powerTime: null,
    value: null,
  });

  // console.log("parseBleData******", parseBleData);
  useEffect(() => {
    if (alertData?.message) {
      setTimeout(() => {
        setShowAlert(isAlert);
      }, 200);
    }
  }, [isAlert, alertData]);

  const [address, setAddress] = useState("");
  const [isConnecting, setisConnecting] = useState(false);
  const [productConnect, setProductConnect] = useState(false);

  const [appState, setAppState] = useState(true);
  const [connectionLoader, setConnectionLoader] = useState(false);
  const [sensorColors, setSensorsColors] = useState(
    sensorColorfnc(parseBleData)
  );
  const [tempAlert, setTempAlert] = useState({
    type: "",
    bool: false,
    title: "",
    message: "",
    icon: false,
    images: "",
  });
  const getConvertedTemp = (realTemp) => {
    if (isFarenheit) {
      return ((Number(realTemp) * 9) / 5 + 32).toFixed(0);
    }
    return realTemp;
  };
  const cel = parseBleData?.Temperature
    ? parseInt(parseBleData?.Temperature - 2.5)
    : parseBleData?.TEMP
    ? parseInt(parseBleData.TEMP - 2.5)
    : 0;
  const displayTemperature = getConvertedTemp(cel);
  // const [bluePulse, setBluePulse] = useState(false);

  // const xml = `
  // <svg xmlns="http://www.w3.org/2000/svg" width="279" height="120" viewBox="0 0 279 120">
  // <g id="Group_6954" data-name="Group 6954" transform="translate(-83.034 -601.37)">
  //   <ellipse id="Ellipse_201" data-name="Ellipse 201" cx="60.5" cy="60" rx="60.5" ry="60" transform="translate(83.034 601.37)" fill=${BaseColor.blueDark}/>
  //   <ellipse id="Ellipse_202" data-name="Ellipse 202" cx="60.5" cy="60" rx="60.5" ry="60" transform="translate(241.034 601.37)" fill=${BaseColor.blueDark}/>
  //   <path id="Path_1561" data-name="Path 1561" d="M-1564,6677.491s15.631,13.9,33.446,13.9,37.812-13.9,37.812-13.9v85.244s-20-12.524-37.812-12.524S-1564,6762.735-1564,6762.735Z" transform="translate(1751.112 -6059.361)" fill=${BaseColor.blueDark}/>
  // </g>
  // </svg>
  // `;

  // const [temp, settemp] = useState(10);
  const userLocation = useSelector((state) => state.auth.userLocation);
  useEffect(() => {
    if (userLocation?.results) {
      const index = userLocation?.results?.length - 2;
      setAddress(
        userLocation?.results[index > 0 ? index : 0]?.formatted_address
      );
    }
  }, [userLocation]);

  useEffect(() => {
    if (!isBleConnected) {
      setShowAlert(true);
    }
  }, [isBleConnected]);

  BluetoothStateManager.onStateChange((bluetoothState) => {
    switch (bluetoothState) {
      case "Unknown":
      case "Resetting":
      case "Unsupported":
      case "Unauthorized":
      case "PoweredOff":
      case "PoweredOn":
        // console.log("ON ==== ...... ******");
        // startScan();
        break;
      default:
        break;
    }
  }, true /*= emitCurrentState */);

  useEffect(() => {
    PushNotification.createChannel(
      {
        channelId: "default-channel-id", // (required)
        channelName: "Default channel", // (required)
        channelDescription: "A default channel", // (optional) default: undefined.
        soundName: "default", // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      (created) =>
        console.log(`createChannel 'default-channel-id' returned '${created}'`) // (optional) callback returns whether the channel was created, false means it already existed.
    );
  }, []);

  useEffect(() => {
    setTimeout(() => {
      NativeAnimated.loop(
        NativeAnimated.timing(animatedValue, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        })
      ).start();
    }, 100);
    setTimeout(() => {
      NativeAnimated.loop(
        NativeAnimated.timing(animatedValue1, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        })
      ).start();
    }, 500);
  }, [sensorColor]);

  useEffect(() => {
    setTimeout(() => {
      NativeAnimated.loop(
        NativeAnimated.timing(animatedValueRing, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        })
      ).start();
    }, 100);
  }, []);

  useEffect(() => {
    BluetoothStateManager.getState().then((bluetoothState) => {
      switch (bluetoothState) {
        case "Unknown":
        case "Resetting":
        case "Unsupported":
        case "Unauthorized":
        case "PoweredOff":
          Toast.show(translate("turnOnBle"));
          if (Platform.OS === "android") {
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then((res) => {
              console.log("BLUETOOTH_CONNECT---", res);
              if (res === "granted") {
                BluetoothStateManager.requestToEnable().then((result) => {
                  console.log(
                    "BluetoothStateManager.requestToEnable -> result",
                    result
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then((result) => {
                console.log("BLUETOOTH_CONNECT----1", result);
              })
              .then((statuses) => {
                console.log(
                  "BLUETOOTH_CONNECT--2",
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT]
                );
              });
          } else {
            BluetoothStateManager.openSettings();
          }
          break;
        case "PoweredOn":
          break;
        default:
          break;
      }
    });
  }, []);

  const resetCrash = () => {
    // if (parseBleData.s9 >= 15) {
    dispatch(setIsCRASH(false));
    console.log("-----> true");
    // }
  };

  const handleStopScan = () => {
    console.log("Scan is stopped");
  };

  // BACK BUTTON HANDLER LOGIC - BUT NOT REQUIRED NOW
  // function handleBackButtonClick() {
  //   if (backPressed > 0) {
  //     BackHandler.exitApp();
  //     backPressed = 0;
  //   } else {
  //     backPressed++;
  //     Toast.show("Press Again To Exit");
  //     setTimeout(() => {
  //       backPressed = 0;
  //     }, 2000);
  //     return true;
  //   }
  //   return true;
  // }

  // useEffect(() => {
  //   BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
  //   return () => {
  //     BackHandler.removeEventListener(
  //       "hardwareBackPress",
  //       handleBackButtonClick
  //     );
  //   };
  // }, []);

  useEffect(() => {
    AppState.addEventListener("change", handleAppStateChange);
    return () => {
      AppState.removeEventListener("change", handleAppStateChange);
    };
  }, []);

  const handleAppStateChange = (nextAppState) => {
    console.log(`App State: ${nextAppState}`);
    if (nextAppState === "active") {
      console.log("App State======", true);
      setAppState(true);
    } else {
      console.log("App State======", false);
      setAppState(false);
    }
  };

  const batteryPercent =
    batteryVoltage > 2.75 ? 100 : ((batteryVoltage - 1.8) * 100) / 0.69 || 0;

  function pushNotification(title, message) {
    console.log("PUSH NOTIFICATION");
    PushNotification.localNotification({
      channelId: "default-channel-id",
      title, // (optional)
      message, // (required)
      ignoreInForeground: false,
    });
  }

  useEffect(() => {
    setTimeout(() => {
      pushNotification("Test", "Hello");
    }, 5000);
  }, []);

  // this function for handle blue pulse

  useEffect(() => {
    if (
      parseBleData &&
      !isEmpty(connectedDeviceDetail) &&
      (connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
        "babyauto-csa" ||
        connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
          "babyauto-csb" ||
        connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
          "reebaby-csa" ||
        connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
          "dorel-csa")
    ) {
      const arr = [
        parseBleData.s2,
        parseBleData.s3,
        parseBleData.s4,
        parseBleData.s5,
      ];

      if (arr.length > 0) {
        let count = 0;
        arr.map((val) => {
          if (val >= 1) {
            count += 1;
          }
        });
      }

      if (powerDetail.value !== parseBleData?.s1) {
        setTimeout(() => {
          setPowerDetail({ powerTime: moment(), value: SW });
        }, 500);
      }

      const powerAlert = moment.duration(nowTime.diff(powerDetail.powerTime));
      if (powerAlert && powerAlert.seconds() >= 60) {
        setTimeout(() => {
          setTempAlert({
            bool: true,
            title: translate("powerDownTitle"),
            message: translate("powerDownMessage"),
            type: "",
          });
          if (!appState) {
            pushNotification("Power Down", "Powering down");
          }
        }, 500);
      }

      if (
        isBleConnected &&
        parseBleData?.s1 >= 1 &&
        accessToken !== "" &&
        !productConnect &&
        !tempModal
      ) {
        setTempAlert({
          bool: true,
          title: "",
          message: `${translate("productConnected", {
            child_name: activeChildDetail?.nick_name || "Child",
          })}`,
          type: "",
        });
        setProductConnect(true);

        if (!appState) {
          pushNotification(
            "Child Seat",
            `${translate("productConnected", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 1 &&
        parseBleData?.s1 < 6 &&
        parseBleData?.s2 === 0 &&
        parseBleData?.s3 === 0
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("notInstall", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("notInstall", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (parseBleData?.s1 > 6 && parseBleData?.s2 === 1) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("rearFacing", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });

        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("rearFacing", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 7 &&
        parseBleData?.s1 < 19 &&
        parseBleData?.s3 === 0 &&
        parseBleData?.s2 === 0
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("notInstall", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("notInstall", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 1 &&
        parseBleData?.s1 < 6 &&
        parseBleData?.s3 === 1
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("notEnough", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("notEnough", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 7 &&
        parseBleData?.s1 < 19 &&
        parseBleData?.s3 === 1 &&
        parseBleData?.s6 === 0
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("recommended", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });

        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("recommended", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 19 &&
        parseBleData?.s6 === 0 &&
        (parseBleData?.s4 === 1 || parseBleData?.s5 === 1)
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("recommended", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });

        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("recommended", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 19 &&
        parseBleData?.s3 === 0 &&
        parseBleData?.s4 === 0 &&
        parseBleData?.s5 === 0
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("notInstall", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("notInstall", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 19 &&
        parseBleData?.s6 === 1 &&
        parseBleData?.s7 > 90 &&
        (parseBleData?.s4 === 1 || parseBleData?.s5 === 1)
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("moveHead", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("moveHead", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 19 &&
        parseBleData?.s6 === 1 &&
        parseBleData?.s7 < 90 &&
        (parseBleData?.s4 === 1 || parseBleData?.s5 === 1) &&
        parseBleData?.s8 < 100
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("boosterUprightPosition", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("boosterUprightPosition", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 1 &&
        parseBleData?.s1 < 7 &&
        parseBleData?.s2 === 1 &&
        parseBleData?.s7 > 90 &&
        parseBleData?.s8 > 100
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("reclineAlert", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("reclineAlert", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 7 &&
        parseBleData?.s1 < 19 &&
        parseBleData?.s3 === 1 &&
        parseBleData?.s6 === 1 &&
        parseBleData?.s7 < 90 &&
        parseBleData?.s8 < 100
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("frontUprightPosition", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("frontUprightPosition", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 7 &&
        parseBleData?.s1 < 19 &&
        parseBleData?.s3 === 1 &&
        parseBleData?.s7 > 160
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("moveHead", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("moveHead", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }
      if (
        parseBleData?.s1 >= 1 &&
        parseBleData?.s1 < 7 &&
        parseBleData?.s2 === 1 &&
        parseBleData?.s7 < 90
      ) {
        setTempAlert({
          bool: true,
          title: `${translate("homeAlertTitle")}`,
          message: `${translate("downHead", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            `${translate("homeAlertTitle")}`,
            `${translate("downHead", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 1 &&
        parseBleData?.s1 < 7 &&
        parseBleData?.s2 === 1 &&
        parseBleData?.s7 > 90 &&
        parseBleData?.s8 < 100
      ) {
        setTempAlert({
          bool: true,
          title: "",
          icon: true,
          images: require("../../assets/images/rearFace.png"),
          message: `${translate("correctRearFacing", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            "",
            `${translate("correctRearFacing", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 7 &&
        parseBleData?.s1 < 19 &&
        parseBleData?.s3 === 1 &&
        parseBleData?.s6 === 1 &&
        parseBleData?.s7 < 90 &&
        parseBleData?.s8 > 100
      ) {
        setTempAlert({
          bool: true,
          title: "",
          icon: true,
          images: require("../../assets/images/frontFace.png"),
          message: `${translate("correctFrontFacing", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            "",
            `${translate("correctFrontFacing", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (
        parseBleData?.s1 >= 19 &&
        (parseBleData?.s4 === 1 || parseBleData?.s5 === 1) &&
        parseBleData?.s6 === 1 &&
        parseBleData?.s7 < 90 &&
        parseBleData?.s8 > 100
      ) {
        setTempAlert({
          bool: true,
          title: "",
          icon: true,
          images: require("../../assets/images/boosterMode.png"),
          message: `${translate("boosterMode", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`,
          type: "tempAlert",
        });
        if (!appState) {
          pushNotification(
            "",
            `${translate("boosterMode", {
              child_name: activeChildDetail?.nick_name || "child",
            })}`
          );
        }
      }

      if (parseBleData?.s9 >= 20 && parseBleData.s1 >= 1) {
        dispatch(setIsCRASH(true));
      }

      if (!isBleConnected && emergencyAlert && accessToken !== "") {
        setTempAlert({
          bool: true,
          title: translate("childLeftInSeatTitle"),
          message: translate("childLeftInSeatMessage"),
          type: "leftChild",
        });
        if (!appState) {
          pushNotification(
            "Child Seat",
            "Your child is still in their car seat. Please ensure they are with a responsible adult."
          );
        }
      }

      setTimeout(() => {
        if (parseBleData?.s1 === 0 && accessToken !== "" && tempAlert?.bool) {
          setTempAlert({
            bool: true,
            title: translate("leftSeatTitle"),
            message: translate("leftSeatMessage"),
            type: "",
          });
          setProductConnect(false);
          if (!appState) {
            pushNotification(
              "Child has left seat",
              "The child is out of seat and the system will power down in 30 seconds."
            );
          }
        }
      }, 300);

      if (
        displayTemperature > Number(highAlert) ||
        (displayTemperature < Number(lowAlert) && isBleConnected)
      ) {
        const message =
          displayTemperature > Number(highAlert)
            ? "The temperature is high around your child, please ensure they are safe and comfortable."
            : "The temperature is low around your child, please ensure they are safe and comfortable.";
        // if (alertTime.temp === 0 || durationTemp.minutes() >= 1) {
        setTempAlert({
          bool: true,
          title: translate("tempAlertTitle"),
          message,
          type: "",
        });
        dispatch(
          BluetoothAction.setAlertTime({ ...alertTime, temp: moment() })
        );
        if (!appState) {
          pushNotification(`${translate("tempAlertTitle")}`, `${message}`);
        }
        // }
      }
      if (batteryPercent < 25) {
        // if ((alertTime.voc === 0 || batteryAlert.minutes() >= 30) && !visible) {
        setTempAlert({
          bool: true,
          title: translate("batteryAlertTitle"),
          message: translate("batteryLowMsg"),
          type: "",
        });
        dispatch(BluetoothAction.setAlertTime({ ...alertTime, voc: moment() }));
        // }
      }
      // front facing setup
    }
  }, [
    parseBleData,
    bleData,
    emergencyAlert,
    isBleConnected,
    displayTemperature,
    batteryPercent,
  ]);

  useEffect(() => {
    if (isBleConnected) {
      if (parseBleData?.s9 === 1) {
        // set front facing image for initial display
        // show 3 number image ppt 120823
        setBgImage(show3Num);
        setHideHWT(false);
        // return;
      }
      if (parseBleData?.s18 > 80 && parseBleData?.s10 === 1) {
        //show 5 number
        // hide height weight temp - done
        //show hori arrow with 180 - done
        // show height left red - done
        setHideHWT(true);
        setBgImage(show5num);
        // return;
      } else if (parseBleData?.s18 < 80 && parseBleData?.s9 === 1) {
        //show 6 number
        //show height right red - done
        //show 180 with horizontal arrows - done
        setHideHWT(true); // do it false in other
        setBgImage(show6);
        // return;
      }
      // else if (parseBleData?.s18 < 80 && parseBleData?.s10 === 1) {
      //   // set rear facing image for initial display
      //   // show 4 number image - setup is done without dot
      //   setHideHWT(false);
      //   setBgImage(show4num);
      //   // return;
      // }
      else if (
        parseBleData?.s18 > 100 &&
        parseBleData?.s9 === 1 &&
        (parseBleData.s7 === 1 || parseBleData.s8 === 1)
      ) {
        // show 7 number
        // show vertical arrow - done
        // hide height weight temp - done
        setHideHWT(true);
        setBgImage(show6);
        // return;
      } else if (
        parseBleData.s1 > 2 &&
        parseBleData?.s9 === 0 &&
        parseBleData?.s10 === 0
      ) {
        //show 8 number
        //show horizontal arrows - done
        // not temp height width - done
        setHideHWT(true);
        setBgImage(show6);
        // return;
      } else if (parseBleData.s12 < 80) {
        // show 9 nunber image
        // harness anngle red - done
        //please move headrest up untill value on screen turn white so we need to set harness value color according this
        // show 2 round arrow up - done
        setHideHWT(true);
        setBgImage(show9HeadrestUp);
        // return;
      } else if (parseBleData.s12 > 120) {
        // show 10 nunber image
        // harness anngle red - done
        //please move headrest down untill value on screen turn white so we need to set harness value color according this
        // show 2 round arrow down - done
        setHideHWT(true);
        setBgImage(show9HeadrestUp);
        // return;
      } else if (parseBleData.s6 === 0 && parseBleData?.s18 < 100) {
        //show 11 number image
        // temp height weight set at their positions - done
        setBgImage(show11num);
        // return;
      } else if (parseBleData?.s18 < 100 && parseBleData.s7 === 1) {
        // show 12 number
        setHideHWT(true);
        setBgImage(show12LeftISO);
        // return;
      } else if (parseBleData?.s18 < 100 && parseBleData.s8 === 1) {
        console.log("show 13 num");
        // show 13 number
        setHideHWT(true);
        setBgImage(show13num);
        // return;
      } else if (
        parseBleData?.s18 > 100 &&
        parseBleData.s4 === 0 &&
        parseBleData.s5 === 0
      ) {
        // show 14 number
        setHideHWT(true);
        setBgImage(show14num);
        // return;
      } else if (parseBleData?.s18 > 100 && parseBleData.s2 === 0) {
        // show 15 number
        setHideHWT(true);
        setBgImage(show15num);
        // return;
      } else if (parseBleData?.s18 > 100 && parseBleData.s3 === 0) {
        // show 16 number
        setHideHWT(true);
        setBgImage(show16num);
        // return;
      } else if (parseBleData.s1 > 2) {
        //show emergenvy alert
        // show 17 number
        setHideHWT(false);
        setBgImage(bleNotConnected);
        // return;
      } else {
        setBgImage(show3Num);
        setHideHWT(false);
      }

      setSensorsColors(sensorColorfnc(parseBleData));
    } else {
      setHideHWT(false);
      setBgImage(bleNotConnected);

      // setBgImage(show11num);//demo
    }
    console.log(
      "ccccble dta=------------------------------",
      JSON.stringify(parseBleData)
    );
  }, [parseBleData]);

  //count down timer
  const initialSeconds = 60;
  const [seconds, setSeconds] = useState(initialSeconds);
  const [isRunning, setIsRunning] = useState(false);

  const handleTimeOver = () => {
    setIsRunning(false);
    // Do something when time is over
    // alert("Time is over!");
    if (seconds === 0) {
      setSeconds(initialSeconds);
    }
    dispatch(BluetoothAction.setIsAlert(false));
  };

  useEffect(() => {
    let interval;
    if (isRunning && seconds > 0) {
      interval = BackgroundTimer.setInterval(() => {
        setSeconds((prevSeconds) => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      handleTimeOver();
    }

    return () => {
      BackgroundTimer.clearInterval(interval);
    };
  }, [isRunning, seconds]);

  const handleStartPress = () => {
    setIsRunning(true);
  };
  const handleStopPress = () => {
    setIsRunning(false);
    setSeconds(initialSeconds);
  };

  useEffect(() => {
    console.log("alertdata type====", alertData.message);
    if (alertData.type === "leftChild") {
      handleStartPress();
    }
  }, [alertData.type]);

  // const Sensor = ({ style, status, id, sensorColor }) => (
  //   <View style={[styles.sensorWrapper, style]}>
  //     <View
  //       style={[
  //         styles.sensorDotIndicator,
  //         {
  //           backgroundColor: sensorColor
  //             ? sensorColor.backgroundColor
  //             : BaseColorMain.alertRed,
  //         },
  //       ]}
  //     >
  //       {/* <Text>{id}</Text> */}
  //     </View>
  //     {status ? (
  //       <>
  //         <Animated.View
  //           style={[
  //             styles.ripleStyle,
  //             {
  //               backgroundColor: sensorColor
  //                 ? sensorColor.backgroundColor
  //                 : BaseColorMain.darkBlue + "99",
  //               // backgroundColor: BaseColorMain.orange,
  //               transform: [
  //                 {
  //                   scale: animatedValue,
  //                 },
  //               ],
  //             },
  //           ]}
  //         />
  //         <Animated.View
  //           style={[
  //             styles.ripleStyle,
  //             {
  //               backgroundColor: sensorColor
  //                 ? sensorColor.backgroundColor
  //                 : BaseColorMain.darkBlue + "4D",
  //               // backgroundColor:  BaseColorMain.orange,
  //               transform: [
  //                 {
  //                   scale: animatedValue1,
  //                 },
  //               ],
  //             },
  //           ]}
  //         />
  //       </>
  //     ) : null}
  //   </View>
  // );
  const Sensor = ({ id, style, status }) => (
    <View style={[styles.sensorWrapper, style]}>
      <View
        style={[
          styles.sensorDotIndicator,
          {
            backgroundColor: status
              ? id === "s1"
                ? sensorColors.s1
                : id === "s2"
                ? sensorColors.s2
                : id === "s3"
                ? sensorColors.s3
                : id === "s4"
                ? sensorColors.s4
                : id === "s5"
                ? sensorColors.s5
                : id === "s6"
                ? sensorColors.s6
                : id === "s7"
                ? sensorColors.s7
                : id === "s8"
                ? sensorColors.s8
                : sensorColors.s8
              : "transparent",
          },
        ]}
      >
        {/* <Text>{id}</Text> */}
      </View>
      {status ? (
        <>
          <NativeAnimated.View
            style={[
              styles.ripleStyle,
              {
                backgroundColor:
                  id === "s1"
                    ? sensorColors.s1 + "99"
                    : id === "s2"
                    ? sensorColors.s2 + "99"
                    : id === "s3"
                    ? sensorColors.s3 + "99"
                    : id === "s4"
                    ? sensorColors.s4 + "99"
                    : id === "s5"
                    ? sensorColors.s5 + "99"
                    : id === "s6"
                    ? sensorColors.s6 + "99"
                    : id === "s7"
                    ? sensorColors.s7 + "99"
                    : id === "s8"
                    ? sensorColors.s8 + "99"
                    : BaseColorMain.turquise + "99",
                transform: [
                  {
                    scale: animatedValue,
                  },
                ],
              },
            ]}
          />
          <NativeAnimated.View
            style={[
              styles.ripleStyle,
              {
                backgroundColor:
                  id === "s1"
                    ? sensorColors.s1 + "4D"
                    : id === "s2"
                    ? sensorColors.s2 + "4D"
                    : id === "s3"
                    ? sensorColors.s3 + "4D"
                    : id === "s4"
                    ? sensorColors.s4 + "4D"
                    : id === "s5"
                    ? sensorColors.s5 + "4D"
                    : id === "s6"
                    ? sensorColors.s6 + "4D"
                    : id === "s7"
                    ? sensorColors.s7 + "4D"
                    : id === "s8"
                    ? sensorColors.s8 + "4D"
                    : sensorColors.s8 + "4D",
                //BaseColorMain.turquise + "4D",
                transform: [
                  {
                    scale: animatedValue1,
                  },
                ],
              },
            ]}
          />
        </>
      ) : null}
    </View>
  );
  const WeightSensor = ({ status }) => {
    return (
      <View style={styles.pulseViewStyle}>
        {status ? (
          <>
            <NativeAnimated.View
              style={[
                styles.ripleStyle,
                {
                  backgroundColor: sensorColors.s1,

                  transform: [
                    {
                      scale: animatedValue,
                    },
                  ],
                },
              ]}
            />
            <NativeAnimated.View
              style={[
                styles.ripleStyle,
                {
                  backgroundColor: sensorColors.s1,

                  transform: [
                    {
                      scale: animatedValue1,
                    },
                  ],
                },
              ]}
            />
          </>
        ) : null}
        <View
          style={[
            {
              position: "absolute",
              height: 20,
              width: 20,
              borderRadius: 18,
              opacity: 1,
              justifyContent: "center",
              alignItems: "center",

              backgroundColor: sensorColors.s1,
            },
            multipleSensor ? { top: "60%" } : {},
          ]}
        />
      </View>
    );
  };
  const translateX = useSharedValue(-150); // Initialize the animation value
  const translateX1 = useSharedValue(1450); // Initialize the animation value
  const translateX2 = useSharedValue(1450);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });
  const animatedStyle1 = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX1.value }],
    };
  });
  const animatedStyle2 = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX2.value }],
    };
  });

  const startAnimation = () => {
    translateX.value = withTiming(14, { duration: 1000 });
    translateX1.value = withTiming(0, { duration: 1200 });
    translateX2.value = withTiming(0, { duration: 2000 });
  };

  useFocusEffect(
    useCallback(() => {
      startAnimation();
    }, [])
  );

  const multipleSensor =
    isObject(connectedDeviceDetail) &&
    !isEmpty(connectedDeviceDetail) &&
    (connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
      "babyauto-csa" ||
      connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
        "babyauto-csb" ||
      connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
        "reebaby-csa" ||
      connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
        "dorel-csa");

  const displayHumidity = parseBleData?.Humidity
    ? Math.round(parseBleData?.Humidity)
    : parseBleData?.HUMIDITY
    ? Math.round(parseBleData?.HUMIDITY)
    : 0;
  const displayWeight = SW || 0;
  const displayCrash =
    parseBleData.s9 > 0 && parseBleData.s9 <= 7
      ? "Loose"
      : parseBleData.s9 >= 8 && parseBleData.s9 <= 16
      ? "Correct"
      : parseBleData.s9 >= 17 && parseBleData.s9 <= 19
      ? "Too Tight"
      : parseBleData.s9 >= 20
      ? "ALERT"
      : "--";

  const seatAngle1 = parseBleData.s7 || 0;
  const seatAngle2 =
    parseBleData.s8 < 50
      ? "Recline 2"
      : parseBleData.s8 >= 51 && parseBleData.s8 <= 166
      ? "Recline 1"
      : "Upright";

  const normalImage = require("../../assets/images/normalSeat.png");
  const rearImage = require("../../assets/images/rearFace.png");
  const frontImage = require("../../assets/images/frontFace.png");
  const boosterImage = require("../../assets/images/boosterMode.png");

  const [changeSeat, setChangeSeat] = useState(false);
  // console.log("alert data type===", alertData.type);
  const onSwipe = (direction) => {
    const { SWIPE_LEFT, SWIPE_RIGHT } = swipeDirections;

    if (direction === SWIPE_LEFT || direction === SWIPE_RIGHT) {
      setChangeSeat(!changeSeat);
    }
    console.log("direction======", direction);
  };

  // blutooth animation
  const Ring = ({ delay }) => {
    const ring = useSharedValue(0);

    const ringStyle = useAnimatedStyle(() => ({
      opacity: 1 - ring.value,
      transform: [
        {
          scale: interpolate(ring.value, [0, 1], [0, 1]),
        },
      ],
    }));
    useEffect(() => {
      ring.value = withDelay(
        delay,
        withRepeat(
          withTiming(1, {
            duration: 4000,
          }),
          -1,
          false
        )
      );
    }, []);
    return <Animated.View style={[styles.ring, ringStyle]} />;
  };

  const BatteryView = () => (
    <View
      style={{
        // backgroundColor: isBleConnected ? BaseColor.whiteColor : null,
        height: 32,
        width: 32,
        borderRadius: 10,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {isBleConnected ? (
        batteryVoltage <= 25 && isBleConnected && !isConnecting ? (
          <SVGBatteryRed width={16} height={16} />
        ) : (
          <SVGBatteryOut width={16} height={16} />
        )
      ) : (
        <SVGBatteryW width={16} height={16} />
      )}

      <View
        style={{
          height: 8,
          width: 2,
          backgroundColor: isBleConnected
            ? batteryVoltage <= 25 && isBleConnected && !isConnecting
              ? "red"
              : BaseColor.whiteColor
            : "#656565",
          position: "absolute",
          top: "38%",
          left: "36%",
        }}
      />
      <View
        style={{
          height: 8,
          width: 2,
          backgroundColor: isBleConnected
            ? batteryVoltage <= 25 && isBleConnected && !isConnecting
              ? BaseColor.transparentColor
              : BaseColor.whiteColor
            : "#656565",
          position: "absolute",
          top: "38%",
          left: "46%",
        }}
      />
      <View
        style={{
          height: 8,
          width: 2,
          backgroundColor: isBleConnected
            ? batteryVoltage <= 25 && isBleConnected && !isConnecting
              ? BaseColor.transparentColor
              : BaseColor.whiteColor
            : "#656565",
          position: "absolute",
          top: "38%",
          left: "54%",
        }}
      />
    </View>
  );

  const MyBlurView = () => {
    return (
      <BlurView
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
        }}
        borderRadius={25}
        blurRadius={1}
        blurAmount={1}
        reducedTransparencyFallbackColor="white"
      />
    );
  };

  // Connection Device..
  async function connectDevice() {
    console.log("DISPATCH 3");
    dispatch(setDeviceID(""));
    if (isEmpty(connectedDeviceList) || isEmpty(lastDeviceId)) {
      navigation.navigate("QRScanner");
    } else {
      setConnectionLoader(true);
      const index = findIndex(
        connectedDeviceList,
        (i) => i.product_id === lastDeviceId
      );
      setTimeout(() => {
        console.log("DISPATCH 4");
        dispatch(setLastDeviceId(lastDeviceId));
        dispatch(setDeviceID(lastDeviceId));
        dispatch(setConnectedDeviceDetail(connectedDeviceList[index]));
        dispatch(setActiveChildDetail(connectedDeviceList[index]));
        setConnectionLoader(false);
      }, 1000);
    }
  }

  return (
    <View style={{ flex: 1, backgroundColor: "#fff", paddingBottom: 0 }}>
      <View style={{ backgroundColor: colors.colors.blue }}>
        {/* Not available */}
        {/* <CHeader
          image={require("../../assets/images/logo-1.png")}
          leftIconName="settings-2"
          rightIconName="notifications-bell-button"
          onLeftPress={() => {
            navigation.openDrawer();
          }}
          onRightPress={() => {
            navigation.navigate("Alerts");
          }}
        /> */}
      </View>
      <View style={{ paddingBottom: 0 }}>
        {/* //top row */}
        <View
          style={{
            position: "absolute",
            left: 10,
            top: 50,
            zIndex: 99,
            width: "95%",
          }}
        >
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Image
                source={{
                  uri:
                    activeChildDetail?.child_profile ||
                    connectedDeviceDetail?.child_profile,
                }}
                style={{
                  height: 30,
                  width: 30,
                  // marginLeft: 30,
                  marginRight: 10,
                  borderRadius: 15,
                  backgroundColor: "grey",
                }}
              />
              <Text style={{ color: BaseColor.whiteColor }}>
                {activeChildDetail?.nick_name ||
                  connectedDeviceDetail?.nick_name ||
                  "Child"}
              </Text>
            </View>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <BatteryView />
              <Text style={{ fontWeight: "bold", color: BaseColor.whiteColor }}>
                SMART 360 IQ
              </Text>
            </View>
          </View>
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",

                width: "100%",
                alignItems: "center",
              }}
            >
              <View style={styles.timeView}>
                <View>
                  <Text
                    style={{ color: "#fff", fontSize: 10, fontWeight: "600" }}
                  >
                    {moment().format("DD MMM, YYYY")}
                  </Text>
                  <Text
                    style={{ color: "#fff", fontSize: 10, fontWeight: "600" }}
                  >
                    {address}
                  </Text>
                </View>

                <Image
                  style={{ width: 16, height: 20 }}
                  resizeMode="contain"
                  source={require("../../assets/images/locationM.png")}
                />
              </View>
              <View>
                <View style={styles.ringView}>
                  {isBleConnected && <Ring delay={0} />}
                  {isBleConnected && <Ring delay={300} />}
                  {isBleConnected && <Ring delay={600} />}
                  {/* <Ring delay={900} /> */}
                  <View>
                    <View style={styles.blueIconView}>
                      {isBleConnected ? (
                        <MCIcon
                          name="bluetooth"
                          color={"#39FFF3"}
                          size={20}
                          style={{
                            borderRadius: 20,
                            justifyContent: "center",
                            fontWeight: "bold",
                          }}
                        />
                      ) : (
                        <ImageBackground
                          source={require("../../assets/images/disconnectedBluetooth.png")}
                          style={{
                            width: 60,
                            height: 60,
                            borderRadius: 30,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Image
                            style={{ width: 10, height: 10, marginBottom: 5 }}
                            resizeMode="contain"
                            source={require("../../assets/images/bluetoothOff.png")}
                          />
                        </ImageBackground>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>

        {hideHWT ? null : (
          <>
            <Animated.View
              style={[
                animatedStyle,
                styles.hwT,

                {
                  right:
                    bgImage === show4num
                      ? null
                      : bgImage === show11num
                      ? null
                      : "10%",
                  left:
                    bgImage === show4num
                      ? "-65%"
                      : bgImage === show11num
                      ? "10%"
                      : 0,
                  top:
                    bgImage === show4num
                      ? "22%"
                      : bgImage === show11num
                      ? "18%"
                      : "25%",
                  flexDirection: "column",
                },
              ]}
            >
              {/* <MyBlurView /> */}

              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  // justifyContent: "space-between",
                  // backgroundColor: "red",
                  width: "100%",
                  opacity: !isBleConnected ? 0.5 : 1,
                }}
              >
                <ImageBackground
                  source={
                    isBleConnected
                      ? require("../../assets/images/blueBlure.png")
                      : require("../../assets/images/greyBlur.png")
                  }
                  style={{
                    width: 50,
                    height: 50,
                    borderRadius: 25,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    source={require("../../assets/images/thermometer.png")}
                    style={{
                      height: 25,
                      width: 25,
                    }}
                    resizeMode="contain"
                  />
                </ImageBackground>
                <Text
                  style={{
                    color: BaseColor.whiteColor,
                    fontWeight: "700",
                    fontSize: 20,
                    fontFamily: FontFamily.semibold,
                  }}
                >
                  Temp{" "}
                  <Text style={{ fontSize: 25, fontWeight: "300" }}>
                    {displayTemperature}°{" "}
                  </Text>
                </Text>
              </View>
            </Animated.View>
            <Animated.View
              style={[
                animatedStyle1,
                styles.right_buttons,
                {
                  right: 5,
                  top: bgImage === show11num ? "50%" : "56%",
                  // flexDirection: "column",
                  width: "50%",
                  height: 53,
                  borderRadius: 30,
                  backgroundColor: "rgba(54, 54, 54, 0.52)",
                  opacity: !isBleConnected ? 0.5 : 1,
                },
              ]}
            >
              {/* <MyBlurView /> */}
              <ImageBackground
                source={
                  isBleConnected
                    ? require("../../assets/images/blueBlure.png")
                    : require("../../assets/images/greyBlur.png")
                }
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 25,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  source={require("../../assets/images/weight.png")}
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  resizeMode="contain"
                />
              </ImageBackground>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontWeight: "700",
                  fontSize: 20,
                  marginRight: 5,
                }}
              >
                Weight
              </Text>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontWeight: "300",
                  fontSize: 25,
                }}
              >
                {isBleConnected ? parseBleData?.s1 : 0}Kgs
              </Text>
            </Animated.View>
            <Animated.View
              style={[
                animatedStyle2,
                styles.right_buttons,
                {
                  right: 5,
                  top: "30%",
                  flexDirection: "row",
                  paddingRight: 20,
                  backgroundColor: alertData?.message
                    ? "#7D0000"
                    : "rgba(54, 54, 54, 0.52)",
                  borderRadius: 25,
                  opacity: !isBleConnected
                    ? 0.5
                    : alertData?.message
                    ? undefined
                    : 0.9,
                },
              ]}
            >
              {/* {isBleConnected && alertData?.message ? null : <MyBlurView />} */}
              <ImageBackground
                source={
                  isBleConnected
                    ? alertData?.message
                      ? require("../../assets/images/redBlur.png")
                      : require("../../assets/images/blueBlure.png")
                    : require("../../assets/images/greyBlur.png")
                }
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 25,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  source={require("../../assets/images/height.png")}
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  resizeMode="contain"
                />
              </ImageBackground>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontWeight: "700",
                  fontSize: 20,
                  marginRight: 5,
                }}
              >
                Height
              </Text>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontWeight: "300",
                  fontSize: 25,
                }}
              >
                {isBleConnected ? parseBleData?.s18 : 0}cms
              </Text>
            </Animated.View>
          </>
        )}

        {/* NOT AVAILABLE */}
        {/* <GradientBack borderRadius={60} /> */}

        <ScrollView
          contentContainerStyle={{
            paddingBottom: 100,
            // marginTop: 16,
            backgroundColor: "#17181a", // "#f8fafb",
          }}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <ImageBackground
            style={styles.background__image}
            resizeMode="cover"
            source={bgImage}
          >
            {parseBleData.s18 > 100 &&
              parseBleData.s9 === 1 &&
              (parseBleData?.s7 === 1 || parseBleData?.s8 === 1) && (
                <Image
                  source={require("../../assets/images/arrowVerticle.png")}
                  style={{
                    height: "40%",
                    width: "40%",
                    marginTop: "50%",
                    marginLeft: "15%",
                  }}
                  resizeMode="contain"
                />
              )}

            {parseBleData.s1 > 2 &&
              parseBleData?.s9 === 0 &&
              parseBleData?.s10 === 0 && (
                <View>
                  <Image
                    source={twoHoriArrow}
                    style={{
                      height: "90%",
                      width: "80%",
                      marginTop: "7%",
                      alignSelf: "center",
                    }}
                    resizeMode="contain"
                  />
                </View>
              )}

            {parseBleData.s18 < 80 && parseBleData.s9 === 1 && (
              <View>
                <Image
                  source={twoHoriArrow}
                  style={{
                    height: "90%",
                    width: "80%",
                    marginTop: "7%",
                    alignSelf: "center",
                  }}
                  resizeMode="contain"
                />
                <Text
                  style={{
                    alignSelf: "center",
                    fontSize: 140,
                    position: "absolute",
                    top: "38%",
                    color: BaseColor.whiteColor,
                  }}
                >
                  180°
                </Text>
                <Text
                  style={{
                    alignSelf: "center",
                    fontSize: 36,
                    position: "absolute",
                    top: "25%",
                    right: "6%",
                    color: BaseColor.whiteColor,
                    fontWeight: "bold",
                    lineHeight: 35,
                  }}
                >
                  Height{`\n`}
                  <Text
                    style={{
                      alignSelf: "center",
                      fontSize: 26,
                      color: BaseColor.alertRed,
                      fontWeight: "normal",
                      lineHeight: 20,
                    }}
                  >
                    {parseBleData?.s18}cms
                  </Text>
                </Text>
              </View>
            )}

            {parseBleData.s18 > 80 && parseBleData.s10 === 1 && (
              <View>
                <Image
                  source={twoHoriArrow}
                  style={{
                    height: "90%",
                    width: "80%",
                    marginTop: "7%",
                    alignSelf: "center",
                  }}
                  resizeMode="contain"
                />
                <Text
                  style={{
                    alignSelf: "center",
                    fontSize: 140,
                    position: "absolute",
                    top: "38%",
                    color: BaseColor.whiteColor,
                  }}
                >
                  180°
                </Text>
                <Text
                  style={{
                    alignSelf: "center",
                    fontSize: 36,
                    position: "absolute",
                    top: "25%",
                    left: "6%",
                    color: BaseColor.whiteColor,
                    fontWeight: "bold",
                    lineHeight: 35,
                  }}
                >
                  Height{`\n`}
                  <Text
                    style={{
                      alignSelf: "center",
                      fontSize: 26,
                      color: BaseColor.alertRed,
                      fontWeight: "normal",
                      lineHeight: 20,
                    }}
                  >
                    {parseBleData?.s18}cms
                  </Text>
                </Text>
              </View>
            )}

            {parseBleData.s12 < 80 && (
              <Image
                source={upSwing}
                style={{
                  height: "50%",
                  width: "50%",
                  marginTop: "20%",
                  marginLeft: "25%",
                }}
                resizeMode="contain"
              />
            )}

            {parseBleData.s12 < 80 && (
              <View
                style={{
                  position: "absolute",
                  bottom: "32%",
                  left: "20%",
                }}
              >
                <Text
                  style={{
                    color: BaseColor.whiteColor,
                    fontSize: 30,
                    fontWeight: "bold",
                  }}
                >
                  Harness
                </Text>
                <Text
                  style={{
                    color: BaseColor.alertRed,
                    fontSize: 20,
                  }}
                >
                  {parseBleData.s12}°
                </Text>
              </View>
            )}

            {parseBleData.s12 > 120 && (
              <Image
                source={show9DownSwing}
                style={{
                  height: "50%",
                  width: "50%",
                  marginTop: "20%",
                  marginLeft: "25%",
                }}
                resizeMode="contain"
              />
            )}

            {parseBleData.s12 > 120 && (
              <View
                style={{
                  position: "absolute",
                  bottom: "32%",
                  left: "20%",
                }}
              >
                <Text
                  style={{
                    color: BaseColor.whiteColor,
                    fontSize: 30,
                    fontWeight: "bold",
                  }}
                >
                  Harness
                </Text>
                <Text
                  style={{
                    color: BaseColor.alertRed,
                    fontSize: 20,
                  }}
                >
                  {parseBleData.s12}°
                </Text>
              </View>
            )}

            {!isBleConnected && (
              <Image
                source={warning}
                style={{
                  height: "20%",
                  width: "20%",

                  alignSelf: "center",

                  marginTop: "60%",
                  // marginLeft: "%",
                }}
                resizeMode="contain"
              />
            )}

            {/* NOT AVAILABLE - frontSeat */}
            {/* <GestureRecognizer onSwipe={onSwipe} style={styles.containerImg}>
              <Image
                // resizeMode="contain"
                style={[
                  styles.seat_image,
                  { height: changeSeat ? "90%" : "120%" },
                ]}
                source={changeSeat ? rearSeat : frontSeat}
              />
            </GestureRecognizer> */}

            <Sensor
              id="s4"
              status={parseBleData.s4}
              style={[{ top: 240, left: 120 }]}
            />
            <Sensor
              id="s5"
              status={parseBleData.s5}
              style={[{ top: 380, left: 70 }]}
            />
            <Sensor
              id="s3"
              status={parseBleData.s3}
              style={[{ top: 240, right: 120 }]}
            />
            <Sensor
              id="s2"
              status={parseBleData.s2}
              style={[{ top: 380, right: 70 }]}
            />
            <Sensor
              id="s7"
              status={parseBleData.s7}
              style={[{ top: 500, left: 75 }]}
            />
            <Sensor
              id="s8"
              status={parseBleData.s8}
              style={[{ top: 500, right: 70 }]}
            />

            <Sensor
              id="s1"
              status={parseBleData.s1}
              style={[{ top: 390, right: 160 }]}
            />

            {/* NOT REQUIRED NOW */}
            {/* <View style={styles.buttom_btns_row}>
              <LinearGradient
                colors={
                  isBleConnected
                    ? ["rgba(20, 135, 255, 12)", "rgba(41, 239, 196, 90)"]
                    : ["rgba(255, 255, 255, 12)", "rgba(255, 255, 255, 90)"]
                }
                style={styles.buttom_btn}
              >
                <Image
                  style={styles.baby_image}
                  source={require("../../assets/images/baby.png")}
                />
                <Text style={styles.title__text}>Height</Text>
                <View style={styles.values_row}>
                  <Text style={styles.num__text}>
                    {isBleConnected ? parseBleData?.s18 : 0}
                  </Text>
                  <Text style={styles.value__text}>cms</Text>
                </View>
              </LinearGradient>
              <LinearGradient
                colors={
                  isBleConnected
                    ? ["rgba(20, 135, 255, 12)", "rgba(41, 239, 196, 90)"]
                    : ["rgba(255, 255, 255, 12)", "rgba(255, 255, 255, 90)"]
                }
                style={styles.buttom_btn}
              >
                <Image
                  style={styles.baby_image}
                  source={require("../../assets/images/baby-weight.png")}
                />
                <Text style={styles.title__text}>Weight</Text>
                <View style={styles.values_row}>
                  <Text style={styles.num__text}>
                    {isBleConnected ? parseBleData?.s1 : 0}
                  </Text>
                  <Text style={styles.value__text}>Kg's</Text>
                </View>
              </LinearGradient>
              <LinearGradient
                colors={
                  isBleConnected
                    ? ["rgba(20, 135, 255, 12)", "rgba(41, 239, 196, 90)"]
                    : ["rgba(255, 255, 255, 12)", "rgba(255, 255, 255, 90)"]
                }
                style={styles.buttom_btn}
              >
                {isAlert ? (
                  <CustomIcon
                    name='warning'
                    size={22}
                    color={BaseColor.alertRed}
                    // style={{ marginEnd: 24 }}
                  />
                ) : (
                  <Image
                    style={styles.baby_image}
                    source={require("../../assets/images/seat.png")}
                  />
                )}
                <Text
                  style={[
                    styles.title__text,
                    {
                      color: isBleConnected
                        ? BaseColor.whiteColor
                        : BaseColor.blackColor,
                    },
                  ]}
                >
                  Safe for Travel
                </Text>
                <Text
                  style={[
                    styles.num__text,
                    {
                      color: isBleConnected
                        ? BaseColor.whiteColor
                        : BaseColor.blackColor,
                      fontSize: 8,
                    },
                  ]}
                >
                  Everything is{" "}
                </Text>
                <Text
                  style={[
                    styles.value__text,
                    {
                      color: isBleConnected
                        ? BaseColor.whiteColor
                        : BaseColor.blackColor,
                      fontSize: 12,
                    },
                  ]}
                >
                  Good
                </Text>
              </LinearGradient>
            </View> */}
          </ImageBackground>
          {/* BAD IMPLEMENTATION */}
          {/* <DashboardNav /> */}
        </ScrollView>

        {showAlert ? (
          <View
            style={{
              alignSelf: "center",
              // padding: 20,
              borderRadius: 8,
              position: "absolute",
              bottom: "13%",
              right: 20,
              left: 20,
              alignItems: "center",
              justifyContent: "center",
              alignContent: "center",
            }}
          >
            {/* Opacity container */}
            <View
              style={{
                backgroundColor: alertData?.message ? "#7D0000" : "#6F6F6F",
                opacity: 0.6,
                width: "100%",
                height: "100%",
                borderRadius: 8,
                position: "absolute",
              }}
            />
            {alertData.type === "leftChild" ? (
              <View
                style={{
                  height: 20,
                  width: 20,
                  position: "absolute",
                  right: 20,
                  top: 15,
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 100,
                  alignContent: "center",
                }}
              >
                <Text>{seconds}</Text>
              </View>
            ) : (
              <TouchableOpacity
                style={{
                  height: 20,
                  width: 20,
                  borderRadius: 10,
                  // backgroundColor: BaseColor.blueDark,
                  position: "absolute",
                  right: -5,
                  top: -5,
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 100,
                  borderColor: BaseColor.whiteColor,
                  borderWidth: 2,
                }}
                onPress={() => {
                  dispatch(BluetoothAction.setIsAlert(false));
                }}
              >
                <Icon
                  name="close"
                  style={{
                    color: BaseColor.whiteColor,
                    fontSize: 15,
                    fontWeight: "bold",
                  }}
                />
              </TouchableOpacity>
            )}
            <View
              style={{
                flexDirection: "row",
                alignContent: "center",
                alignItems: "center",
                paddingHorizontal: 10,
                alignSelf: "center",
                marginTop: 20,
                marginBottom: 10,
              }}
            >
              {isBleConnected ? (
                <>
                  <Image
                    style={{
                      height: 50,
                      width: 50,
                    }}
                    resizeMode="contain"
                    source={
                      alertData?.message
                        ? require("../../assets/images/alert-circle.png")
                        : require("../../assets/images/checkmark.png")
                    }
                  />
                  <Text
                    style={{
                      fontWeight: "bold",
                      color: BaseColor.whiteColor,
                      fontSize: 14,
                      opacity: 1,
                      paddingHorizontal: 20,
                      flexShrink: 1,
                    }}
                  >
                    {alertData?.message ||
                      `Perfect! ${activeChildDetail?.nick_name}'s seat is perfectly calibrated. Time to hit the road`}
                  </Text>
                </>
              ) : (
                <>
                  <Text
                    style={{
                      fontWeight: "bold",
                      color: BaseColor.whiteColor,
                      fontSize: 14,
                      opacity: 1,
                      paddingHorizontal: 20,
                      flexShrink: 1,
                    }}
                  >
                    Device not connected
                  </Text>
                  <TouchableOpacity
                    style={{
                      borderRadius: 5,
                      backgroundColor: BaseColor.green,
                      padding: 10,
                    }}
                    activeOpacity={0.8}
                    onPress={() => {
                      connectDevice();
                    }}
                  >
                    {connectionLoader ? (
                      <ActivityIndicator
                        color={BaseColor.whiteColor}
                        size="small"
                      />
                    ) : (
                      <Text
                        style={{
                          fontWeight: "bold",
                          color: BaseColor.whiteColor,
                          fontSize: 14,
                        }}
                      >
                        Connect
                      </Text>
                    )}
                  </TouchableOpacity>
                </>
              )}
            </View>
            {alertData?.type === "leftChild" ? (
              <View
                style={{
                  flexDirection: "row",

                  paddingHorizontal: 20,
                  marginBottom: 10,
                }}
              >
                <TouchableOpacity
                  style={{
                    flex: 1,
                    marginEnd: 8,
                    borderWidth: 1,
                    borderRadius: 30,
                    borderColor: BaseColor.blueDark,
                    elevation: 0,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    shadowOpacity: 0.0,
                    alignItems: "center",
                    justifyContent: "center",
                    height: 40,
                  }}
                  onPress={() => {
                    dispatch(BluetoothAction.setIsAlert(false));
                    dispatch(BluetoothAction.setIsCancelAlert(true));
                    alertData.type === "leftChild" ? handleStopPress() : null;
                  }}
                >
                  <Text
                    style={{ color: BaseColor.whiteColor, fontWeight: "bold" }}
                  >
                    With Adult
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    marginStart: 8,
                    borderWidth: 1,
                    borderColor: BaseColor.whiteColor,
                    elevation: 0,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    borderRadius: 30,
                    borderColor: BaseColor.blueDark,
                    shadowOpacity: 0.0,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  onPress={() => {
                    dispatch(BluetoothAction.setIsAlert(false));
                    alertData.type === "leftChild" ? handleStopPress() : null;
                  }}
                >
                  <Text
                    style={{ color: BaseColor.whiteColor, fontWeight: "bold" }}
                  >
                    Send sms
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <Text>{alertData.type}</Text>
            )}
          </View>
        ) : null}
      </View>

      {isCRASH ? (
        <TouchableOpacity
          TouchableOpacity={1}
          onPress={() => resetCrash()}
          style={styles.resetButton}
        >
          <Text style={styles.resetTxt}>{translate("reset")}</Text>
        </TouchableOpacity>
      ) : null}

      <CAlert
        visible={tempAlert.bool && !isEmpty(accessToken)} //Only visible if logged in
        type={tempAlert.type}
        onCancelPress={() =>
          setTempAlert({ type: "", bool: false, title: "", message: "" })
        }
        icon={tempAlert.icon}
        images={tempAlert.images}
        alertTitle={translate(tempAlert.title)}
        alertMessage={translate(tempAlert.message)}
        onOkPress={() =>
          setTempAlert({ type: "", bool: false, title: "", message: "" })
        }
      />

      {tempAlert.bool && !isEmpty(accessToken) ? (
        <View style={styles.alertMsgView}>
          <Text style={styles.alertTitle}>{translate(tempAlert.title)}</Text>
          {tempAlert?.icon ? (
            <View style={styles.iconMainView}>
              <Image
                style={{
                  height: 50,
                  width: 50,
                  borderRadius: 40,
                  backgroundColor: BaseColor.blueDark,
                }}
                source={require("../../assets/images/thumbs.png")}
              />
            </View>
          ) : (
            <View style={styles.iconMainView}>
              <View
                style={{
                  backgroundColor: BaseColor.alertRed,
                  width: 50,
                  height: 50,
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: 40,
                }}
              >
                <CustomIcon
                  name="warning"
                  size={20}
                  color={BaseColor.whiteColor}
                />
              </View>
            </View>
          )}
          <Text
            style={{
              ...styles.alertMsg,
              color: tempAlert?.icon ? "#000" : "red",
            }}
          >
            {translate(tempAlert.message)}
            {/* Congratulations, you have set up the car seat correctly for Rear
              facing! you are now ready for travel */}
          </Text>
        </View>
      ) : null}

      {/* THIS IS JUST A MODAL TO SHOW LOADER */}
      <Modal
        visible={isConnecting}
        transparent
        style={{
          flex: 1,
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: BaseColor.black30,
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: "center",
              alignItems: "center",
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}
          >
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: "bold",
                marginTop: 8,
              }}
            >
              Loading
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default Dashboard;
