/* eslint-disable quotes */
import React, { useCallback, useEffect, useState } from "react";
import {
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Modal,
  Share,
  Text,
  TouchableOpacity,
  View,
  Platform,
  ActivityIndicator,
  ScrollView,
  Alert,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
// import RNFetchBlob from "rn-fetch-blob";
import Toast from "react-native-simple-toast";
import Clipboard from "@react-native-community/clipboard";
import BluetoothStateManager from "react-native-bluetooth-state-manager";
import { useFocusEffect, useTheme } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { findIndex, isArray, isEmpty } from "lodash";
import moment from "moment";
import LinearGradient from "react-native-linear-gradient";
import CHeader from "../../components/CHeader";
import GradientBack from "../../components/gradientBack";
import { CustomIcon } from "../../config/LoadIcons";
import { FontFamily, FontWeight } from "../../config/typography";
import styles from "./styles";
import CVideoPlayer from "../../components/CVideoPlayer";
import CAlert from "../../components/CAlert";
import { translate } from "../../lang/Translate";
import CButton from "../../components/CButton";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import {
  addAction,
  openInAppBrowser,
  sendErrorReport,
} from "../../utils/commonFunction";
import InAppModal from "../../components/InAppModal";
import bluetoothActions from "../../redux/reducers/bluetooth/actions";
import { check, PERMISSIONS, request, RESULTS } from "react-native-permissions";

let backPressed = 0;

const Home = ({ navigation }) => {
  const dispatch = useDispatch();
  const {
    setDeviceID,
    setConnectedDeviceDetail,
    setLastDeviceId,
    setActiveChildDetail,
  } = bluetoothActions;
  const userData = useSelector((state) => state.auth.userData);
  const [videoPlayModal, setvideoPlayModal] = useState(false);
  const [alertModal, setAlertModal] = useState(false);
  const token = useSelector((state) => state.auth.accessToken);
  const { connectedDeviceList, isBleConnected, emergencyAlert } = useSelector(
    (state) => state.bluetooth
  );
  const [postArr, setpostArr] = useState([]);
  const [inAppModal, setInAppModal] = useState(false);
  const [inAppMsgData, setinAppMsgData] = useState({});
  const [deviceList, setDeviceList] = useState([]);
  const [postLoad, setPostLoad] = useState(true);
  const [devices, setDevices] = useState([
    {
      type: "add",
    },
  ]);

  const colors = useTheme();
  const BaseColor = colors.colors;

  useFocusEffect(
    useCallback(() => {
      if (token !== "") {
        getPost("feed_post");
        getFeedPost();
        getDeviceList();
        getChildInfo();
      }
    }, [])
  );

  // this function for get device list
  async function getDeviceList() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    const newObj = {
      type: "add",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        "POST",
        {
          platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
        },
        headers
      );
      if (response.success && isArray(response.data)) {
        const arr = response.data;
        // arr.unshift();
        setDeviceList([newObj, ...arr]);
        dispatch(bluetoothActions.setConnectedDeviceList([newObj, ...arr]));
      } else {
        setDeviceList([newObj]);
        Toast.show(response.message);
      }
    } catch (error) {
      setDeviceList([newObj]);
      sendErrorReport(error, "get_device_list");
      console.log("error for device list ===", error);
    }
  }

  const getChildInfo = () => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    console.log("getChildInfo -> headers", headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      "POST",
      {
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          const tempArr = [
            {
              type: "add",
            },
          ];
          const childArr = response.data;
          childArr.map((item) => {
            tempArr.unshift(item);
          });

          setDevices(tempArr);
        } else {
          Toast.show(response.message);
        }
      })
      .catch((err) => {
        // console.log("ERRR", err);
        Toast.show("Something went wrong while getting child detail");
        sendErrorReport(err, "get_child_in_device4");
      });
  };

  const getPost = (type) => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    getApiData(
      BaseSetting.endpoints.getPost,
      "POST",
      {
        // campaign_type: type,
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          setinAppMsgData(response?.data);
          setTimeout(() => {
            setInAppModal(true);
          }, 2000);
        } else {
          Toast.show("No Posts");
        }
      })
      .catch((err) => {
        console.log("ERRR", err);
        Toast.show("Something went wrong while getting posts");
        sendErrorReport(err, "get_post");
      });
  };

  // this function for get feed post data
  async function getFeedPost() {
    setPostLoad(true);
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getFeedPost,
        "POST",
        {
          platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
        },
        headers
      );

      if (response.success) {
        setpostArr(response.data);
      } else {
        Toast.show(response.message);
      }
      setPostLoad(false);
    } catch (error) {
      setPostLoad(false);
      sendErrorReport(error, "get_feed_post");
      console.log("feed post error ===", error);
    }
  }

  const getFeed = (type) => {
    setpostArr([]);
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    getApiData(
      BaseSetting.endpoints.getFeed,
      "POST",
      {
        campaign_type: type,
      },
      headers
    )
      .then((response) => {
        if (response.success) {
          setpostArr(response?.data);
        } else {
          Toast.show("No Posts");
        }
      })
      .catch((err) => {
        console.log("ERRR", err);
        Toast.show("Something went wrong while getting feed");
        sendErrorReport(err, "get_feed");
      });
  };

  const permissionCheck = () => {
    BluetoothStateManager.getState().then((bluetoothState) => {
      switch (bluetoothState) {
        case "Unknown":
        case "Resetting":
        case "Unsupported":
        case "Unauthorized":
        case "PoweredOff":
          Toast.show(translate("turnOnBle"));
          if (Platform.OS === "android") {
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then((res) => {
              console.log("BLUETOOTH_CONNECT---", res);
              if (res === "granted") {
                BluetoothStateManager.requestToEnable().then((result) => {
                  console.log(
                    "BluetoothStateManager.requestToEnable -> result",
                    result
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then((result) => {
                console.log("BLUETOOTH_CONNECT--1", result);
              })
              .then((statuses) => {
                console.log(
                  "BLUETOOTH_CONNECT--2",
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT]
                );
              });
          } else {
            BluetoothStateManager.openSettings();
          }
          break;
        case "PoweredOn":
          break;
        default:
          break;
      }
    });
  };

  // this function for disconnect device if connected
  async function disconnectDevice(item) {
    console.log("DISPATCH 3");
    if (Platform.OS === "android") {
      await check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then((res) => {
        console.log("BLUETOOTH_CONNECT---", res);
        if (res === "granted") {
          BluetoothStateManager.requestToEnable().then((result) => {
            console.log(
              "BluetoothStateManager.requestToEnable -> result",
              result
            );
          });
        } else if (res === "denied") {
          Alert.alert(
            "Bluetooth Permission Required",
            "This app needs Bluetooth permission. Please go to settings and grant Bluetooth access.",
            [
              {
                text: "Cancel",
                style: "cancel",
              },
              {
                text: "Open Settings",
                onPress: () => BluetoothStateManager.openSettings(),
              },
            ]
          );
          return;
        }
      });
    }
    const index = findIndex(devices, (i) => i.product_id === item.product_id);
    dispatch(setDeviceID(""));
    setTimeout(() => {
      console.log("DISPATCH 4");
      dispatch(setLastDeviceId(item.product_id));
      dispatch(setDeviceID(item.product_id));
      dispatch(setConnectedDeviceDetail(item));
      dispatch(setActiveChildDetail(devices[index]));
      // navigation.navigate("Connect", {
      //   product_id: item.product_id,
      //   device_id: item.device_id,
      // });
      navigation.navigate(translate("dashboard"));
    }, 1000);
  }

  // this function for handle media url
  function handleUrl(item) {
    if (
      item?.launch_url &&
      (item.launch_url.includes("http://") ||
        item.launch_url.includes("https://"))
    ) {
      openInAppBrowser(item?.launch_url);
      addAction(item, "clicked", token);
    } else {
      Toast.show(translate("urlError"));
    }
  }

  const renderDevice = ({ item, index }) => {
    return (
      <View
        style={[
          styles.deviceCard,
          {
            backgroundColor: BaseColor.blue,
            borderWidth: item.type == "add" ? 1 : 0,
          },
        ]}
        activeOpacity={0.7}
        onPress={() => {
          setAlertModal(true);
        }}
      >
        {item.type == "add" ? (
          <TouchableOpacity
            style={{ justifyContent: "center", alignItems: "center" }}
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate("QRScanner");
            }}
          >
            <CustomIcon name="plus" size={24} color={BaseColor.whiteColor} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{ ...styles.deviceCard, marginEnd: 0, borderWidth: 1 }}
            activeOpacity={0.7}
            onPress={() => {
              // setAlertModal(true);
              disconnectDevice(item);
            }}
          >
            {item?.child_profile ? (
              <Image
                source={{ uri: item.child_profile }}
                style={{
                  height: "100%",
                  width: "100%",
                  borderRadius: styles.deviceCard.borderRadius,
                }}
                resizeMode="cover"
              />
            ) : (
              <Image
                source={require("../../assets/images/9.jpg")}
                style={{
                  height: "100%",
                  width: "100%",
                  borderRadius: styles.deviceCard.borderRadius,
                }}
                resizeMode="cover"
              />
            )}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  useEffect(() => {
    if (!isBleConnected) {
      permissionCheck();
    }
  }, []);

  const renderGuide = ({ item, index }) => (
    <View
      style={[
        styles.guideRootView,
        {
          // backgroundColor: BaseColor.whiteColor,
          marginHorizontal: 16,
          marginVertical: 24,
          height: 250,
          width: 300,
          borderWidth: 4,
          borderColor: BaseColor.whiteColor,
        },
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.7}
        style={{ justifyContent: "center", alignItems: "center" }}
        onPress={() => {
          // setvideoPlayModal(true);
          handleUrl(item);
        }}
      >
        {item?.post_file ? (
          <View style={{ width: "100%", height: "100%" }}>
            <Image
              source={{ uri: item?.post_file }}
              style={{ height: "100%", width: "100%" }}
            />
          </View>
        ) : (
          <View
            style={{
              height: 250,
              width: 300,
              backgroundColor: "#ccc",
            }}
          />
        )}
        {item?.attachment_type === "Video link" ? (
          <View
            style={{
              position: "absolute",
              // alignSelf: 'center',
              justifyContent: "center",
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 40,
              alignItems: "center",
              height: 54,
              width: 54,
              borderRadius: 54,
            }}
          >
            <FAIcon name="play" size={24} color={BaseColor.blue} />
          </View>
        ) : null}
      </TouchableOpacity>
      <View
        style={[
          styles.row,
          {
            position: "absolute",
            bottom: 16,
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
            alignSelf: "center",
            fontFamily: FontFamily.default,
          },
        ]}
      >
        <Text
          style={{
            color: BaseColor.whiteColor,
            textAlign: "center",
            paddingHorizontal: 20,
          }}
          numberOfLines={2}
        >
          {item.message}
        </Text>
        <Text
          style={{
            color: BaseColor.whiteColor,
            textAlign: "center",
            paddingHorizontal: 20,
          }}
        >
          {`- ${item.post_title}`}
        </Text>
        {/* <TouchableOpacity
          style={{ padding: 8 }}
          activeOpacity={0.7}
          onPress={() => {
            Clipboard.setString(item.media_link);
          }}
          >
          <CustomIcon name="save" color={BaseColor.black70} size={16} />
        </TouchableOpacity> */}
        {/* {item.launch_url ? (
          <TouchableOpacity
            style={{ padding: 8 }}
            activeOpacity={0.7}
            onPress={() => {
              Share.share({
                message: `Share Feed ${item.launch_url} `,
                url: item.launch_url,
              });
              addAction(item, "shared", token);
            }}
          >
            <CustomIcon name="send-2" color={BaseColor.black70} size={16} />
          </TouchableOpacity>
        ) : null} */}
      </View>
    </View>
  );

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show("Press Again To Exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick
      );
    };
  }, []);

  function generateGreetings() {
    var currentHour = moment().format("HH");

    if (currentHour >= 5 && currentHour < 12) {
      return translate("goodmorning");
    } else if (currentHour >= 12 && currentHour < 18) {
      // return "Good Afternoon";
      return translate("goodafternoon");
    } else if (currentHour >= 18 && currentHour < 24) {
      // return "Good Evening";
      return translate("goodevening");
    } else {
      return "Hello";
    }
  }

  /**
   * @function requestNotificationPermission to ask for notifiction permissions
   */
  const requestNotificationPermission = async () => {
    const result = await request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
    return result;
  };

  /**
   * @function checkNotificationPermission to check for notifiction permissions
   */
  const checkNotificationPermission = async () => {
    const result = await check(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);
    return result;
  };

  /**
   * @function askNotifictionPermission to ask for notifiction permissions
   */
  const askNotifictionPermission = async () => {
    if (Platform.OS === "android") {
      try {
        const checkPermission = await checkNotificationPermission();
        if (checkPermission !== RESULTS.GRANTED) {
          const request = await requestNotificationPermission();
          console.log(
            "🚀 ~ file: Entrypoint.tsx:254 ~ askNotifictionPermission ~ request:",
            request
          );
          if (request !== RESULTS.GRANTED) {
            // Toast.show(
            //   'Notifiction permission revoked by user. Please allow from settings.',
            // );
          }
        }
      } catch (error) {
        console.log("askNotifictionPermission ~ error:", error);
      }
    }
  };

  useEffect(() => {
    askNotifictionPermission();
  }, []);

  return (
    <>
      <View style={{ flex: 1 }}>
        <GradientBack />
        <CHeader
          image={require("../../assets/images/logo-1.png")}
          leftIconName="settings-2"
          rightIconName="notifications-bell-button"
          onLeftPress={() => {
            navigation.openDrawer();
          }}
          onRightPress={() => {
            navigation.navigate("Alerts");
          }}
        />
        {/* <ScrollView> */}
        <View>
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: BaseColor.whiteColor,
              fontSize: 18,
              paddingHorizontal: 24,
              marginTop: 16,
            }}
          >
            {generateGreetings()}
          </Text>
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: BaseColor.whiteColor,
              fontSize: 28,
              marginTop: 10,
              paddingHorizontal: 24,
            }}
          >
            {userData.full_name}
          </Text>
        </View>
        <View style={styles.smartDeviceView}>
          <Text
            style={{
              fontFamily: FontFamily.default,
              color: BaseColor.whiteColor,
              fontSize: 13,
              marginBottom: 24,
              paddingHorizontal: 24,
            }}
          >
            {translate("mydevice")}
          </Text>
          <FlatList
            data={connectedDeviceList}
            renderItem={renderDevice}
            horizontal
            contentContainerStyle={{ paddingStart: 24 }}
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item.type}
          />
        </View>
        <View
          style={{
            ...styles.videoView,
            paddingLeft: 10,
            backgroundColor: colors.colors.darkBlue,
          }}
        >
          <FlatList
            data={postArr}
            renderItem={renderGuide}
            keyExtractor={(item) => item.id}
            scrollEnabled={isEmpty(postArr) ? false : true}
            horizontal={isEmpty(postArr) ? false : true}
            contentContainerStyle={{
              paddingTop: 16,
              paddingBottom: 100,
            }}
            showsHorizontalScrollIndicator={false}
            ListHeaderComponent={() => {
              if (isEmpty(renderGuide)) {
                return null;
              }
              return (
                <View>
                  <Text
                    style={{
                      color: BaseColor.whiteColor,
                      fontFamily: FontFamily.default,
                    }}
                  >
                    {translate("whatsNew")}
                  </Text>
                </View>
              );
            }}
            ListEmptyComponent={() => (
              <View style={styles.emptyComponent}>
                {postLoad ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text
                    style={{
                      width: "100%",
                      fontSize: 16,
                      color: "#fff",
                      textAlign: "center",
                    }}
                  >
                    {translate("noPosts")}
                  </Text>
                )}
              </View>
            )}
          />
        </View>
        {/* </ScrollView> */}
        <Modal
          visible={videoPlayModal}
          onRequestClose={() => setvideoPlayModal(false)}
          style={{ flex: 1 }}
        >
          <View style={{ flex: 1 }}>
            <CVideoPlayer
              source="http://techslides.com/demos/sample-videos/small.mp4"
              repeat
              onError={(err) => {
                console.log("err", err);
              }}
              MainViewStyle={{
                height: Dimensions.get("window").height,
                width: Dimensions.get("window").width,
              }}
            />
          </View>
          <CButton
            iconname="cancel"
            iconsize={10}
            iconColor={BaseColor.blackColor}
            style={styles.closeBtn}
            onPress={() => {
              setvideoPlayModal(false);
            }}
          />
        </Modal>
        <CAlert
          visible={alertModal}
          onRequestClose={() => setAlertModal(false)}
          alertMessage={translate("homeAlertMsg")}
          alertTitle={translate("homeAlertTitle")}
        />
        {/* {!isEmpty(inAppMsgData) && isArray(inAppMsgData)
          ? inAppMsgData.map((item, index) => {
            console.log('item**', item);
            return (
              <View key={index}> */}
        {!isEmpty(inAppMsgData) ? (
          <InAppModal
            visible={inAppModal}
            button={inAppMsgData?.button_info || []}
            title={!isEmpty(inAppMsgData) ? inAppMsgData?.text_info : {}}
            image={!isEmpty(inAppMsgData) ? inAppMsgData?.post_file : ""}
            position={
              !isEmpty(inAppMsgData) ? inAppMsgData?.message_position : ""
            }
            onClose={() => {
              setinAppMsgData({});
              setInAppModal(false);
              addAction(inAppMsgData, "clicked", token);
            }}
          />
        ) : null}

        {/* </View>
            );
          })
          : null} */}
      </View>
    </>
  );
};

export default Home;
