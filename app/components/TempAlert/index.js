import React, { useEffect, useState, useRef } from "react";
import {
  AppState,
  NativeEventEmitter,
  NativeModules,
  PermissionsAndroid,
  Platform,
  Geolocation,
  Alert,
} from "react-native";
import { findIndex, isEmpty, isObject, isString, map } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import PushNotification, {
  PushNotificationIOS,
} from "react-native-push-notification";
import BackgroundTimer from "react-native-background-timer";
import GetLocation from "react-native-get-location";
import Toast from "react-native-simple-toast";
import BleManager from "react-native-ble-manager";
import BluetoothStateManager from "react-native-bluetooth-state-manager";
import { bytesToString } from "convert-string";
import CAlert from "../CAlert";
import NetInfo from "@react-native-community/netinfo";
import BluetoothActions from "../../redux/reducers/bluetooth/actions";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import BaseSetting from "../../config/setting";
import { check, PERMISSIONS, RESULTS } from "react-native-permissions";
import { request } from "react-native-permissions";
import { sendErrorReport } from "../../utils/commonFunction";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Video from "react-native-video";

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);
console.log("🚀 ~ bleManagerEmitter:", bleManagerEmitter);

let parseBleData = {};
const TempAlert = (props) => {
  const peripherals = new Map();
  const navRef = props.navigation?.current;
  const dispatch = useDispatch();
  const {
    setAlertTime,
    setEmergencyAlert,
    setTempModal,
    setIsAlert,
    setAlertDataRedux,
    setIsCancelAlert,
  } = BluetoothActions;
  const [visible, setVisible] = useState(false);
  const visibleRef = React.useRef(null);
  visibleRef.current = visible;
  const [sound, setSound] = useState(false);
  const playerRef = useRef();
  const [alertData, setAlertData] = useState({
    title: "",
    message: "",
    type: "",
  });
  const {
    lowAlert,
    highAlert,
    bleData,
    alertTime,
    emergencyAlert,
    isBleConnected,
    connectedDeviceDetail,
    activeChildDetail,
    deviceID,
    lastDeviceId,
    tempModal,
    serviceID,
    characteristicID,
    isAlert,
    isCancelAlert,
    connectedDeviceList,
  } = useSelector((state) => state.bluetooth);

  const bleDataRef = useRef();
  bleDataRef.current = bleData;

  const [appState, setAppState] = useState(true);
  const nowTime = moment();
  const durationTemp = moment.duration(nowTime.diff(alertTime.temp));
  const batteryAlert = moment.duration(nowTime.diff(alertTime.voc));
  const accessToken = useSelector((state) => state.auth.accessToken);
  const [fanStatus, setFanStatus] = useState(false);
  const [startRead, setstartRead] = useState(false);
  const [productConnect, setProductConnect] = useState(false);
  const [powerDetail, setPowerDetail] = useState({
    powerTime: null,
    value: null,
  });
  const languageData = useSelector((state) => state.language.languageData);
  //For Checking if data submited to API
  const [isBleDataSubmited, setIsBleDataSubmited] = useState(0);
  // console.log("CHECK CD ----->", connectedDeviceDetail);
  // console.log("CHECK deviceID ----->", deviceID);
  // console.log("CHECK lastDeviceId ----->", lastDeviceId);
  // console.log("CHECK isBleConnected ----->", isBleConnected);

  parseBleData =
    bleDataRef?.current &&
    !isEmpty(bleDataRef?.current) &&
    !isObject(bleDataRef?.current) &&
    isString(bleDataRef?.current)
      ? JSON.parse(bleDataRef?.current)
      : {};

  const temp =
    isObject(parseBleData) &&
    !isEmpty(parseBleData) &&
    parseBleData?.Temperature
      ? parseBleData.Temperature
      : parseBleData.Temp;
  const batteryVoltage =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.BV
      ? parseBleData.BV
      : parseBleData.Bat;
  const SW =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.SW
      ? parseBleData.SW
      : parseBleData.s1;

  const retrieveConnected = async (deviceId) =>
    new Promise((resolve) => {
      BleManager.getConnectedPeripherals([]).then((results) => {
        if (results.length == 0) {
          console.log("No connected peripherals");
        }
        let matched = false;
        for (let i = 0; i < results.length; i++) {
          const peripheral = results[i];
          if (deviceId == peripheral.id) matched = true;
          // peripheral.connected = true;
          // peripherals.set(peripheral.id, peripheral);
          // setList(Array.from(peripherals.values()));
        }

        resolve(matched);
      });
    });

  useEffect(() => {
    if (deviceID) {
      readData();
    } else {
      if (isBleConnected) {
        // Toast.show("Please conenct device manually");
        console.log("DISPATCH 1");
        dispatch(BluetoothActions.setDeviceID(""));
      }
      setstartRead(false);
    }
  }, [deviceID]);

  useEffect(() => {
    if (Platform.OS === "android" && Platform.Version >= 23) {
      console.log("called---4");
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
          // sendErrorReport(true, "Fine_location");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
          ).then((res) => {
            if (res) {
              console.log("User accept");
            } else {
              console.log("User refuse");
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE).then((res) => {
        if (res !== "granted") {
          sendErrorReport(true, "getCurrentLocation5");
          console.log("getCurrentLocation callled---2");
          getCurrentLocation(false, "");
        }
      });
      // Request location permission here
      request("location", { type: "always" })
        .then((result) => {
          if (result === "granted") {
            console.log("Location permission granted");
            // You can start using location services here
          } else {
            console.log("Location permission denied");
          }
        })
        .catch((error) => {
          console.error("Error requesting location permission:", error);
        });
    }
  }, []);

  // //! This effect to autoconnect the device when back in range.
  useEffect(() => {
    const intervalTime = appState ? 5000 : 15000;
    const intervalId = BackgroundTimer.setInterval(() => {
      if (!isBleConnected && lastDeviceId) {
        BleManager.isPeripheralConnected(lastDeviceId, []).then(
          (isConnected) => {
            const index =
              connectedDeviceList &&
              findIndex(
                connectedDeviceList,
                (i) => i.product_id === lastDeviceId
              );
            if (isConnected) {
              console.log("Peripheral is connected 111!");
              dispatch(
                BluetoothActions.setActiveChildDetail(
                  connectedDeviceList[index]
                )
              );
            } else {
              console.log("Peripheral is NOT connected 111!");
              dispatch(BluetoothActions.setDeviceID(lastDeviceId));
              dispatch(
                BluetoothActions.setActiveChildDetail(
                  connectedDeviceList[index]
                )
              );
              readData();
            }
          }
        );
      }
    }, intervalTime);

    return () => BackgroundTimer.clearInterval(intervalId); // cleanup on unmount
  }, [isBleConnected, lastDeviceId]);

  //! This Function send the Data to API
  const sendBleDataToApi = async (BD, cond) => {
    if (isBleDataSubmited < 10) {
      const headers = {
        "Content-Type": "application/json",
        authorization: accessToken ? `Bearer ${accessToken}` : "",
      };
      const data = {
        product_id: connectedDeviceDetail?.id || deviceID,
        platform: Platform.OS === "ios" ? "IOS" : "ANDROID",
        data_info: BD || {},
        date_time: moment().format(),
        cond,
      };
      console.log("sendBleDataToApi ~ data", data);
      // setIsBleDataSubmited(isBleDataSubmited + 1);

      map(data, (v, k) => {
        if (isEmpty(v)) {
          console.log("sendBleDataToApi ~ RETURNNNNNNNNNNNNNNNNNNNNN", v, k);
          return;
        }
      });
      try {
        const response = await getApiData(
          BaseSetting.endpoints.saveDeviceDataInfo,
          "POST",
          data,
          headers
        );
        console.log(
          "🚀 ~ file: index.js ~ line 175 ~ sendBleDataToApi ~ response",
          response
        );
        setIsBleDataSubmited(isBleDataSubmited + 1);
      } catch (error) {
        console.log("sendBleDataToApi error ===", error);
        Toast.show(error?.message || "Unable to send Data");
        setIsBleDataSubmited(isBleDataSubmited + 1);
      }
    }
  };

  // this functioon for disconnect child device
  async function disconnectChildDevice() {
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    if (!isEmpty(connectedDeviceDetail) && connectedDeviceDetail?.id) {
      try {
        const response = await getApiData(
          BaseSetting.endpoints.disconnectChildDevice,
          "POST",
          {
            device_id: connectedDeviceDetail?.id,
            product_id: connectedDeviceDetail?.product_id,
          },
          headers
        );
        if (response.success) {
          dispatch(BluetoothActions.setConnectedDeviceDetail({}));
          dispatch(BluetoothActions.setcharacteristicID(""));
          dispatch(BluetoothActions.setServiceID(""));
          // dispatch(BluetoothActions.setActiveChildDetail({}));
        }
      } catch (error) {
        console.log("disconnect device error ===", error);
        sendErrorReport(error, "disconnect_child_device");
      }
    }
  }

  const handleDisconnectedPeripheral = (data) => {
    const peripheral = peripherals.get(data.peripheral);
    if (peripheral) {
      peripheral.connected = false;
      peripherals.set(peripheral.id, peripheral);
    }
    console.log(`Disconnected from ${data.peripheral}`);
    dispatch(BluetoothActions.setIsBleConnected(false));
    console.log("DISPATCH 2");
    dispatch(BluetoothActions.setDeviceID(""));
    // setisConnecting(false);
    setstartRead(false);
    Toast.show("Disconnected from device");
    // setTimeout(() => {
    setTimeout(() => {
      // if (parseBleData.s1 > 2) { // for jason
      if (parseBleData.s1 === 0) {
        //for me
        dispatch(BluetoothActions.setEmergencyAlert(true));
        dispatch(BluetoothActions.setBleData({}));
      } else {
        dispatch(BluetoothActions.setActiveChildDetail({}));
      }
      dispatch(BluetoothActions.setcharacteristicID(""));
      dispatch(BluetoothActions.setServiceID(""));
    }, 300);
    disconnectChildDevice();
    // }, 90000);
  };

  /* Works fine in Background - Tested By Krunal */
  const handleUpdateValueForCharacteristic = (data) => {
    // console.log("handleUpdateValueForCharacteristic -> data", data);
    // console.log(
    //   `Received data from ${data.peripheral} characteristic ${data.characteristic}`,
    //   data.value
    // );
    if (data) {
      const datas = bytesToString(data.value);
      // console.log("CHECK --> handleUpdateValueForCharacteristic===", datas);
      try {
        // let realObj = JSON.parse(datas); //ONLY TO CHECK IF DATA IS PROPER AND VALID
        if (isString(datas)) {
          dispatch(BluetoothActions.setBleData(datas));
          sendErrorReport(datas, "datas_update");
        }

        // sendBleDataToApi(datas, "LISTENER");
        // console.log("handleUpdateValueForCharacteristic success", realObj);
      } catch (err) {
        console.log("handleUpdateValueForCharacteristic catch", err);
        Toast.show("ERROR: Unreadable data from device");
        sendErrorReport(err, "unreadable_char");
      }
    }
  };

  // const handleDiscoverPeripheral = (peripheral) => {
  //   console.log("Got ble peripheral", peripheral);
  //   if (!peripheral.name) {
  //     peripheral.name = "NO NAME";
  //   }
  //   peripherals.set(peripheral.id, peripheral);
  // };

  // const handleStopScan = () => {
  //   console.log("Scan is stopped");
  // };

  // const readOnlyData = () => {
  //   console.log("IDS******", deviceID);
  //   // Alert.alert("deviceID", deviceID);
  //   setTimeout(() => {
  //     if (deviceID && serviceID && characteristicID) {
  //       BleManager.read(deviceID, serviceID, characteristicID)
  //         .then((readData) => {
  //           const data = bytesToString(readData);
  //           // sendBleDataToApi(data, "IF");
  //           // Alert.alert("NEW DATAss", JSON.stringify(data));
  //           dispatch(BluetoothActions.setBleData(data));
  //         })
  //         .catch((error) => {
  //           // Failure code
  //           console.log("read error", error);
  //         });
  //     } else {
  //       BleManager.retrieveServices(deviceID)
  //         .then((peripheralData) => {
  //           // sendBleDataToApi(peripheralData, "ELSE");
  //           console.log(
  //             "Retrieved peripheral services readonly",
  //             peripheralData,
  //           );
  //           const char = peripheralData.characteristics;
  //           const mainChar = char[char.length - 1];
  //           const newBleData = mainChar?.value?.bytes;
  //           // Alert.alert("NEW DATA", JSON.stringify(newBleData));
  //           if (isArray(newBleData) && newBleData.length > 0) {
  //             const nString = bytesToString(newBleData);
  //             // Alert.alert("NEW nString", JSON.stringify(nString));
  //             dispatch(BluetoothActions.setBleData(nString));
  //           }
  //         })
  //         .catch((err) => {
  //           console.log("err readData ===1", err);
  //           dispatch(BluetoothActions.setIsBleConnected(false));
  //           BleManager.disconnect(deviceID)
  //             .then(() => {
  //               console.log("device disconnect ===1");
  //               readData();
  //             })
  //             .catch((error) => {
  //               console.log("error disconnect ===1", error);
  //             });
  //           if (startRead) {
  //             setstartRead(false);
  //           }
  //         });
  //     }
  //   }, 500);
  // };

  // useEffect(() => {
  //   BackgroundTimer.stopBackgroundTimer();
  //   // Alert.alert("startRead", startRead);
  //   if (startRead) {
  //     BackgroundTimer.runBackgroundTimer(() => {
  //       // code that will be called every 3 seconds
  //       readOnlyData();
  //     }, 3000);
  //   } else {
  //     BackgroundTimer.stopBackgroundTimer();
  //   }
  // }, [startRead]);

  // useEffect(() => {
  //   if (appState === false) {
  //     BackgroundTimer.setTimeout(() => {
  //       const getLocationInBackground = async () => {
  //         try {
  //           const location = await GetLocation.getCurrentPosition({
  //             enableHighAccuracy: true,
  //             timeout: 15000,
  //           });
  //           console.log("Location:", location);
  //         } catch (error) {
  //           console.error("Error getting location:", error);
  //         }
  //       };

  //       getLocationInBackground();
  //     }, 5000);
  //   }
  // }, [appState]);
  useEffect(() => {
    const type = isCancelAlert ? "cancel" : "ok";
    if (!isAlert) {
      // setVisible((pre) => !pre);
      setVisible(false);
      dispatch(setIsCancelAlert(false));
      if (type === "ok" && alertData.type === "leftChild") {
        console.log("ok called", activeChildDetail.id);
        getCurrentLocation(activeChildDetail.id, "click");
      }

      if (type === "cancel" && alertData.type === "leftChild") {
        console.log("cancel called");
        setstartRead(false);
      }

      if (alertData.type === "temp") {
        dispatch(setTempModal(false));
      }
      console.log("setalert data called==18");
      setAlertData({ title: "", message: "", type: "", carPlayTitle: "" });
      if (emergencyAlert) {
        dispatch(setEmergencyAlert(false));
      }
      console.log("set sound---------flase----");
      setSound(false);
    }
  }, [isAlert]);
  useEffect(() => {
    const listener = bleManagerEmitter.addListener(
      "BleManagerDidUpdateValueForCharacteristic",
      handleUpdateValueForCharacteristic
    );

    return () => {
      listener.remove();
    };
  }, [isBleConnected]);
  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    // bleManagerEmitter.addListener(
    //   "BleManagerCentralManagerWillRestoreState",
    //   (data) => {
    //     console.log(
    //       "BLE ==> BleManagerCentralManagerWillRestoreState ===> ",
    //       data
    //     );
    //   }
    // );

    /* Listing to Characteristic updates - Works on IOS ForeGround and Background both - Tested by Krunal */
    bleManagerEmitter.addListener(
      "BleManagerDidUpdateValueForCharacteristic",
      handleUpdateValueForCharacteristic
    );

    // // this line comment for device connection issue
    // BleManager.start({ showAlert: false });

    // bleManagerEmitter.addListener(
    //   "BleManagerDiscoverPeripheral",
    //   handleDiscoverPeripheral
    // );
    // bleManagerEmitter.addListener("BleManagerStopScan", handleStopScan);
    bleManagerEmitter.addListener(
      "BleManagerDisconnectPeripheral",
      handleDisconnectedPeripheral
    );

    if (Platform.OS === "android" && Platform.Version >= 23) {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
            PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
          ).then((res) => {
            if (res) {
              console.log("User accept");
            } else {
              console.log("User refuse");
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE).then((res) => {
        if (res !== "granted") {
          console.log("getCurrentLocation callled---3");
          getCurrentLocation(false, "");
        }
      });
    }
    return () => {
      console.log("unmount");
      // bleManagerEmitter.removeListener(
      //   'BleManagerDiscoverPeripheral',
      //   handleDiscoverPeripheral
      // );
      // bleManagerEmitter.removeListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.removeListener(
      //   'BleManagerDisconnectPeripheral',
      //   handleDisconnectedPeripheral
      // );
      // bleManagerEmitter.removeListener(
      //   'BleManagerDidUpdateValueForCharacteristic',
      //   handleUpdateValueForCharacteristic
      // );
    };
  }, []);
  useEffect(() => {
    // Start BLE Manager
    BleManager.start({
      showAlert: false,
    });

    // Listen for restored peripherals
    const restoreListener = bleManagerEmitter.addListener(
      "BleManagerCentralManagerWillRestoreState",
      (data) => {
        if (data?.peripherals?.length > 0) {
          data.peripherals.forEach((peripheral) => {
            console.log("Restored Peripheral:", peripheral);
          });
        } else if (data?.peripheral) {
          // in case you’re only sending one UUID in native
          console.log("Restored Peripheral UUID:", data.peripheral);
        }
      }
    );

    return () => {
      console.log("unmount reconnnect");
      restoreListener.remove();
    };
  }, []);

  /* To connect device => Returns a promise */
  const connectDevice = async () =>
    new Promise((resolve) => {
      console.log("BLE ==> Connecting", deviceID);
      if (!deviceID) {
        resolve(false);
      }
      BleManager.connect(deviceID)
        .then(() => {
          console.log("BLE ==> Connected");
          resolve(true);
        })
        .catch((error) => {
          console.log("BLE ==> Error");
          // setisConnecting(false);
          // Failure code
          console.log("error====*****", error, deviceID);
          sendErrorReport(error, "connect_device");
          resolve(false);
        });
    });

  const readData = async () => {
    // setisConnecting(true);
    let connected = null;
    try {
      const isAlreadyConnected = await retrieveConnected(deviceID);
      console.log(
        "CHECK --> BLE ==> isAlreadyConnected ==> 1",
        isAlreadyConnected
      );
      if (!isAlreadyConnected) {
        connected = await connectDevice(deviceID);
      } else {
        connected = true;
      }
    } catch (err) {
      console.log("CHECK --> BLE ==> coonect ==> Error 1", err);
      sendErrorReport(err, "read_data");
      // setisConnecting(false);
    }
    console.log("CHECK --> BLE ==> connected ===> 1", connected);

    // Success code
    console.log("CHECK --> Connected");
    // setconnnectedID(id);

    if (connected) {
      console.log("CHECK --> Before MTU");
      await setMTU(deviceID);
      console.log("CHECK --> Before Retreive");
      await BleManager.retrieveServices(deviceID)
        .then(async (peripheralData) => {
          console.log(
            "CHECK --> Retrieved peripheral services 1",
            peripheralData
          );
          sendBleDataToApi(JSON.stringify(peripheralData), "Read");
          // Alert.alert("peripheral service", JSON.stringify(peripheralData));
          const char = peripheralData.characteristics;

          // const mainChar = char[char.length - 1];

          // console.log("char-----", char);
          let mainChar = {};

          if (Platform.OS === "android") {
            char.forEach((charEle) => {
              // console.log("char-----element--", charEle.properties.Notify);
              if (charEle?.properties.Notify === "Notify") {
                // console.log("obj===notify--in for", charEle);
                mainChar = charEle;
              }
            });
          } else {
            mainChar = char.find((obj) => {
              // console.log("obj===-", obj);
              if (obj?.properties.includes("Notify")) {
                console.log("obj===notify", obj);
                return obj;
              }
            });
          }

          // const newBleData = mainChar?.value?.bytes;
          // const newBleData = mainChar?.manufacturerData?.bytes;

          console.log("CHECK --> CHAR==>>>>", mainChar);

          // char.map(async (item) => {
          // console.log(
          //   "id, mainChar.service, mainChar.characteristic",
          //   deviceID,
          //   item.service,
          //   item.characteristic,
          // );

          // setTimeout(async () => {
          // BleManager2.startNotification(
          //   deviceID,
          //   mainChar.service,
          //   mainChar.characteristic,
          // )
          //   .then(() => {
          //     console.log(
          //       `Started notification on ${deviceID} ${mainChar.characteristic}`,
          //     );
          //   })
          //   .catch((error) => {
          //     console.log("Notification error", error);
          //     Toast.show("Start Notification Error:" + JSON.stringify(error));
          //   });
          // }, 1000);
          // });
          // if (isObject(mainChar) && !isEmpty(mainChar) && mainChar.value) {
          //   if (isArray(newBleData) && newBleData.length > 0) {
          //     sendBleDataToApi(JSON.stringify(mainChar), "If array");
          //     const nString = bytesToString(newBleData);
          //     dispatch(BluetoothActions.setBleData(nString));
          //     // Alert.alert("nString", JSON.stringify(nString));
          //   }
          // } else if (deviceID && mainChar.service && mainChar.characteristic) {
          //   console.log("Ble== else if");
          //   BleManager.read(deviceID, mainChar.service, mainChar.characteristic)
          //     .then((rData) => {
          //       sendBleDataToApi(JSON.stringify(mainChar), "If else");
          //       // Alert.alert("Read Data From Device");
          //       const data = bytesToString(rData);
          //       dispatch(BluetoothActions.setBleData(data));
          //       dispatch(
          //         BluetoothActions.setcharacteristicID(mainChar.characteristic),
          //       );
          //       dispatch(BluetoothActions.setServiceID(mainChar.service));
          //     })
          //     .catch((error) => {
          //       // Failure code
          //       sendBleDataToApi(
          //         {
          //           data: JSON.stringify(mainChar),
          //           error: JSON.stringify(error),
          //         },
          //         "ERROR",
          //       );
          //       console.log("read erroreee", error);
          //     });
          // }

          // Alert.alert("characteristics", peripheralData.characteristics);

          // dispatch(BluetoothAction.setDeviceID(id));
          // dispatch(BluetoothAction.setcharacteristicID(mainChar.characteristic));
          // dispatch(BluetoothAction.setServiceID(mainChar.service));

          // setCharID(mainChar.characteristic);
          // setservID(mainChar.service);

          // char.map((item) => {
          //   console.log(
          //     "id, mainChar.service, mainChar.characteristic",
          //     deviceID,
          //     item.service,
          //     item.characteristic,
          //   );

          // setTimeout(async () => {
          // bManager.monitorCharacteristicForDevice(
          //   deviceID,
          //   item.service,
          //   item.characteristic,
          //   listener: (error: ?Error, characteristic: ?Characteristic) => void,
          // ):
          // console.log("Before111 Stop notification ===> ", mainChar);
          // try {
          //   const stopped = await BleManager.stopNotification(
          //     deviceID,
          //     mainChar.service,
          //     mainChar.characteristic,
          //   );

          //   console.log("Stoppeddd ====> ", stopped);
          // } catch (err) {
          //   console.log("stopped errr ===> ", err);
          // }
          BleManager.startNotification(
            deviceID,
            mainChar.service,
            mainChar.characteristic
          )
            .then(() => {
              console.log(
                `CHECK --> Started notification on ${deviceID} ${mainChar.characteristic}`
              );
            })
            .catch((error) => {
              // Toast.show("ERROR:" + JSON.stringify(error));
              console.log("CHECK --> Notification error", error);
              sendErrorReport(error, "unable_to_start_notification");
            });
          // }, 1000);
          // });
          return true;
        })
        .catch((e) => {
          console.log(
            "CHECK EEEE --> Error -> Unable to Retrive Services",
            e?.message
          );
          sendErrorReport(e, "unable_retrieve_service");
          // sendErrorReport(JSON.stringify(e), "unable_retrieve_service");
          return true;
        });
      console.log("CHECK --> After Retreive");

      dispatch(BluetoothActions.setIsBleConnected(true));

      setTimeout(() => {
        setstartRead(true);
        // setisConnecting(false);
        Toast.show("CONNECTED");
      }, 2000);
    } else {
      console.log("CHECK --> not connected");
      // setisConnecting(false);
    }
  };

  const setMTU = (did) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve(true);
      }, 3000);
      BleManager.requestMTU(did, 512)
        .then((mtu) => {
          // Success code
          console.log("CHECK --> MTU size changed to " + mtu + " bytes");
          resolve(true);
        })
        .catch((error) => {
          // Failure code
          console.log("CHECK --> MTU", error);
          resolve(true);
        });
    });
  };

  useEffect(() => {
    PushNotification.createChannel(
      {
        channelId: "default-channel-id", // (required)
        channelName: "Default channel", // (required)
        channelDescription: "A default channel", // (optional) default: undefined.
        soundName: "default", // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      (created) =>
        console.log(`createChannel 'default-channel-id' returned '${created}'`) // (optional) callback returns whether the channel was created, false means it already existed.
    );

    // Configure notification handlers to access location data
    PushNotification.configure({
      onNotification: function (notification) {
        console.log("NOTIFICATION CLICKED:", notification);

        // Extract location data from notification
        if (
          notification.userInfo &&
          notification.userInfo.latitude &&
          notification.userInfo.longitude
        ) {
          const locationData = {
            latitude: notification.userInfo.latitude,
            longitude: notification.userInfo.longitude,
            accuracy: notification.userInfo.accuracy,
            timestamp: notification.userInfo.timestamp,
            cached: notification.userInfo.cached || false,
          };

          console.log("LOCATION FROM NOTIFICATION:", locationData);

          // Store location data for use in your app
          AsyncStorage.setItem(
            "notificationLocation",
            JSON.stringify(locationData)
          );

          // You can now use this location data as needed
          // For example, send SMS with location, update UI, etc.
        }

        notification.finish(PushNotificationIOS.FetchResult.NoData);
      },

      requestPermissions: Platform.OS === "ios",
    });
  }, []);

  useEffect(() => {
    AppState.addEventListener("change", handleAppStateChange);
    return () => {
      AppState.removeEventListener("change", handleAppStateChange);
    };
  }, []);

  // this function for update alert list in db
  async function addAlert(title, body, type = "") {
    const state = await NetInfo.fetch();
    if (state?.isConnected) {
      return;
    }
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    try {
      await getApiData(
        BaseSetting.endpoints.addAlert,
        "POST",
        {
          title: title || "",
          body: body || "",
          app_state: appState ? "active" : "background",
          platform: Platform.OS,
          type,
        },
        headers
      );
    } catch (error) {
      sendErrorReport(error, "add_alert_api");
      console.log("add alert error ===", error);
    }
  }

  const handleAppStateChange = (nextAppState) => {
    console.log(`App State: ${nextAppState}`);
    if (nextAppState === "active") {
      console.log("App State======", true);
      setAppState(true);
    } else {
      console.log("App State======", false);
      setAppState(false);
    }
  };

  useEffect(() => {
    const battery =
      batteryVoltage > 2.75 ? 100 : ((batteryVoltage - 1.8) * 100) / 0.69;
    // if ((temp > Number(highAlert) || temp < Number(lowAlert)) && !visible) {
    //   const message =
    //     temp > Number(highAlert)
    //       ? "The temperature is high around your child, please ensure they are safe and comfortable."
    //       : "The temperature is low around your child, please ensure they are safe and comfortable.";
    //   if (alertTime.temp === 0 || durationTemp.minutes() >= 1) {
    //     if (
    //       !isEmpty(connectedDeviceDetail) &&
    //       (connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
    //         "babyauto-csa" ||
    //         connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
    //           "babyauto-csb" ||
    //         connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
    //           "reebaby-csa" ||
    //         connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
    //           "dorel-csa")
    //     ) {
    //       setAlertData({
    //         title: translate("tempAlertTitle"),
    //         message,
    //         type: "",
    //       });
    //       // setVisible(true);
    //       dispatch(setAlertTime({ ...alertTime, temp: moment() }));
    //       addAlert("Temperature alert", message);
    //     }
    //   }
    if (temp > 30 && !visible) {
      console.log("setalert data called==1");
      setAlertData({
        title: translate("tempAlertTitle"),
        message: `The temperature inside the vehicle is very high, please cool down before placing ${
          activeChildDetail?.nick_name || "child"
        } in the vehicle`,
        type: "",
        carPlayTitle: translate("tempAlertTitle"),
      });

      setVisible(true);
      dispatch(setAlertTime({ ...alertTime, temp: moment() }));
      addAlert(
        "Temperature alert",
        `The temperature inside the vehicle is very high, please cool down before placing ${
          activeChildDetail?.nick_name || "child"
        } in the vehicle`
      );
    } else if (battery < 25 && !visible) {
      // if ((alertTime.voc === 0 || batteryAlert.minutes() >= 30) && !visible) {
      console.log("setalert data called==2");
      setAlertData({
        title: translate("batteryAlertTitle"),
        message: translate("batteryLowMsg"),
        type: "",
        carPlayTitle: translate("batteryAlertTitle"),
      });

      setVisible(true);
      dispatch(setAlertTime({ ...alertTime, voc: moment() }));
      addAlert("Battery alert", "Battery is lower then 25%");
      // }
    }
    let screenName = "";
    if (navRef !== undefined) {
      const currentScreen = navRef?.getCurrentRoute()?.name;
      screenName = currentScreen; // ChildInfo
    }

    // console.log("jjjjjdjjjin condition=====", screenName);
    if (screenName !== "ChildInfo" && isBleConnected) {
      if (
        parseBleData?.s1 > 0 &&
        parseBleData?.s2 == 0 &&
        parseBleData?.s3 == 0 &&
        parseBleData?.s4 == 0 &&
        parseBleData?.s5 == 0 &&
        parseBleData?.s6 == 1 &&
        parseBleData?.s7 == 0 &&
        parseBleData?.s8 == 0 &&
        parseBleData?.s9 == 0 &&
        parseBleData?.s10 == 1 &&
        parseBleData?.s12 > 85 &&
        parseBleData?.s12 < 105 &&
        parseBleData?.s13 < 12
      ) {
        setAlertData({
          title: "Safe To Travel",
          message: `${
            activeChildDetail?.nick_name || "child"
          }’s car seat is safely set up.`,
          type: "",
          carPlayTitle: "Safe To Travel",
        });
        if (!appState) {
          pushNotification(
            "Safe To Travel",
            `${
              activeChildDetail?.nick_name || "child"
            }’s car seat is safely set up.`
          );
        }
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Safe To Travel",
          `${
            activeChildDetail?.nick_name || "child"
          }’s car seat is safely set up.`
        );
      } else if (parseBleData.s18 > 80 && parseBleData.s10 === 1) {
        console.log("setalert data called==5");
        setAlertData({
          title: "Alert",
          message: `${
            activeChildDetail?.nick_name || "child"
          } is too tall to travel rear facing, please rotate the car seat to front facing`,
          type: "",
          carPlayTitle: "Incorrect Orientation Alert",
        });

        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `${
              activeChildDetail?.nick_name || "child"
            } is too tall to travel rear facing, please rotate the car seat to front facing`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `${
            activeChildDetail?.nick_name || "child"
          } is too tall to travel rear facing, please rotate the car seat to front facing`
        );
      } else if (parseBleData?.s18 < 80 && parseBleData.s9 === 1) {
        console.log("setalert data called==4");
        setAlertData({
          title: "Alert",
          message: `${
            activeChildDetail?.nick_name || "child"
          } is not yet tall enough to travel forward facing, please rotate the car seat to rear facing`,
          type: "",
          carPlayTitle: "Alert",
        });

        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `${
              activeChildDetail?.nick_name || "child"
            } is not yet tall enough to travel forward facing, please rotate the car seat to rear facing`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `${
            activeChildDetail?.nick_name || "child"
          } is not yet tall enough to travel forward facing, please rotate the car seat to rear facing`
        );
      } else if (parseBleData.s18 > 100 && parseBleData.s9 === 1) {
        console.log("setalert data called==6");
        setAlertData({
          title: "Alert",
          message: `${
            activeChildDetail?.nick_name || "Child"
          } is too tall to travel in this mode, please set up the car seat in booster mode`,
          type: "",
          carPlayTitle: "Alert",
        });

        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `${
              activeChildDetail?.nick_name || "Child"
            } is too tall to travel in this mode, please set up the car seat in booster mode`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `${
            activeChildDetail?.nick_name || "Child"
          } is too tall to travel in this mode, please set up the car seat in booster mode`
        );
      } else if (
        parseBleData.s1 > 2 &&
        parseBleData.s9 === 0 &&
        parseBleData.s10 === 0
      ) {
        console.log("setalert data called==7");
        setAlertData({
          title: "Alert",
          message: `${
            activeChildDetail?.nick_name || "Child"
          } is not locked in position, please rotate to either front or rear facing mode`,
          type: "",
          carPlayTitle: "Incorrect Orientation Alert",
        });
        if (!appState) {
          pushNotification(
            "Alert",
            `${
              activeChildDetail?.nick_name || "Child"
            } is not locked in position, please rotate to either front or rear facing mode`
          );
        }
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `${
            activeChildDetail?.nick_name || "Child"
          } is not locked in position, please rotate to either front or rear facing mode`
        );
      } else if (parseBleData.s12 < 80) {
        console.log("setalert data called==14");
        setAlertData({
          title: "Alert",
          message: `Please move ${
            activeChildDetail?.nick_name || "Child"
          }'s headrest up untill the value on screen turn white`,
          type: "",
          carPlayTitle: "Headrest Height Alert",
        });
        if (!appState) {
          pushNotification(
            "Alert",
            `Please move ${
              activeChildDetail?.nick_name || "Child"
            }'s headrest up untill the value on screen turn white`
          );
        }
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please move ${
            activeChildDetail?.nick_name || "Child"
          }'s headrest up untill the value on screen turn white`
        );
      } else if (parseBleData.s12 > 120) {
        console.log("setalert data called==15");
        setAlertData({
          title: "Alert",
          message: `Please move ${
            activeChildDetail?.nick_name || "Child"
          }’s headrest down untill, the value on screen turn white`,
          type: "",
          carPlayTitle: "Headrest Height Alert",
        });
        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `Please move ${
              activeChildDetail?.nick_name || "Child"
            }’s headrest down untill, the value on screen turn white`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please move ${
            activeChildDetail?.nick_name || "Child"
          }’s headrest down untill, the value on screen turn white`
        );
      } else if (parseBleData.s6 === 0) {
        console.log("setalert data called==8");
        setAlertData({
          title: "Alert",
          message: `Please check ${
            activeChildDetail?.nick_name || "child"
          }'s standing leg is down and firmly making contact with the floor of the vehicle`,
          type: "",
          carPlayTitle: "Standing leg Alert",
        });
        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `Please check ${
              activeChildDetail?.nick_name || "child"
            }'s standing leg is down and firmly making contact with the floor of the vehicle`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please check ${
            activeChildDetail?.nick_name || "child"
          }'s standing leg is down and firmly making contact with the floor of the vehicle`
        );
      } else if (parseBleData.s7 === 1) {
        console.log("setalert data called==9");
        setAlertData({
          title: "Alert",
          message:
            "Please check the left ISO fix on the car seat is securley attached to the ISO fix bars in the vehicle",
          type: "",
          carPlayTitle: "ISO fix Alert",
        });
        if (!appState) {
          pushNotification(
            "Alert",
            "Please check the left ISO fix on the car seat is securley attached to the ISO fix bars in the vehicle"
          );
        }
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          "Please check the left ISO fix on the car seat is securley attached to the ISO fix bars in the vehicle"
        );
      } else if (parseBleData.s8 === 1) {
        console.log("setalert data called==10");
        setAlertData({
          title: "Alert",
          message:
            "Please check the right ISO fix on the car seat is securley attached to the ISO fix bars in the vehicle",
          type: "",
          carPlayTitle: "ISO fix Alert",
        });
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          "Please check the right ISO fix on the car seat is securley attached to the ISO fix bars in the vehicle"
        );
      } else if (
        parseBleData.s18 > 100 &&
        parseBleData.s4 === 0 &&
        parseBleData.s5 === 0
      ) {
        console.log("setalert data called==11");
        setAlertData({
          title: "Alert",
          message: `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s shoulder harness is correctly fitted on the left or right shoulder.`,
          type: "",
          carPlayTitle: "Alert",
        });
        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `Please check ${
              activeChildDetail?.nick_name || "child"
            }’s shoulder harness is correctly fitted on the left or right shoulder`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s shoulder harness is correctly fitted on the left or right shoulder`
        );
      } else if (parseBleData.s18 > 100 && parseBleData.s2 === 0) {
        console.log("setalert data called==12");
        setAlertData({
          title: "Alert",
          message: `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s hip harness is correctly fitted on the left hip.`,
          type: "",
          carPlayTitle: "Alert",
        });
        setVisible(true);
        if (!appState) {
          pushNotification(
            "Alert",
            `Please check ${
              activeChildDetail?.nick_name || "child"
            }’s hip harness is correctly fitted on the left hip.`
          );
        }
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s hip harness is correctly fitted on the left hip.`
        );
      } else if (parseBleData.s18 > 100 && parseBleData.s3 === 0) {
        console.log("setalert data called==13");
        setAlertData({
          title: "Alert",
          message: `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s hip harness is correctly fitted on the right hip.`,
          type: "",
          carPlayTitle: "Alert",
        });
        if (!appState) {
          pushNotification(
            "Alert",
            `Please check ${
              activeChildDetail?.nick_name || "child"
            }’s hip harness is correctly fitted on the right hip.`
          );
        }
        setVisible(true);
        dispatch(setAlertTime({ ...alertTime, air: moment() }));
        addAlert(
          "Alert",
          `Please check ${
            activeChildDetail?.nick_name || "child"
          }’s hip harness is correctly fitted on the right hip.`
        );
      }
    }
    // console.log("==============================", bleData);
  }, [bleData]);

  useEffect(() => {
    const battery =
      batteryVoltage > 2.75 ? 100 : ((batteryVoltage - 1.8) * 100) / 0.69;
    if ((temp > Number(highAlert) || temp < Number(lowAlert)) && !appState) {
      if (alertTime.temp === 0 || durationTemp.minutes() >= 1) {
        if (
          !isEmpty(connectedDeviceDetail) &&
          (connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
            "babyauto-csa" ||
            connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
              "babyauto-csb" ||
            connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
              "reebaby-csa" ||
            connectedDeviceDetail?.device_bluetooth_name.toLowerCase() !==
              "dorel-csa")
        ) {
          const message =
            temp > Number(highAlert)
              ? translate("highAlertMsg")
              : translate("lowTempAlertMsg");
          dispatch(setAlertTime({ ...alertTime, temp: moment() }));
          pushNotification(translate("tempAlertTitle"), message, true); // Include location for temperature alerts
          addAlert(
            "Temperature alert",
            temp > Number(highAlert)
              ? "The temperature is high around your child, please ensure they are safe and comfortable."
              : "The temperature is low around your child, please ensure they are safe and comfortable."
          );
        }
      }
    } else if (battery < 25) {
      if ((alertTime.voc === 0 || batteryAlert.minutes() >= 10) && !appState) {
        dispatch(setAlertTime({ ...alertTime, voc: moment() }));
        pushNotification(
          translate("batteryAlertTitle"),
          translate("batteryLowMsg")
        );
        addAlert("Battery alert", "Battery is lower then 25%");
      }
    }
  }, [bleData]);

  // // this function for get current location
  // async function getCurrentLocation(id, click = "") {
  //   GetLocation.getCurrentPosition({
  //     enableHighAccuracy: true,
  //     timeout: !appState ? 15000 : 0,
  //   })
  //     .then((location) => {
  //       console.log("get_location==resss==", JSON.stringify(location));
  //       // AsyncStorage.setItem("setLocation", JSON.stringify(location));
  //       if (id) {
  //         // sendSMS(location, id, click);
  //       }
  //     })
  //     .catch(async (error) => {
  //       // const getLocation = await AsyncStorage.getItem("setLocation");
  //       // sendErrorReport(JSON.parse(getLocation), "get_location");
  //       // sendErrorReport(id, "SMS_id122");
  //       // console.log("get_location===catch=", JSON.stringify(getLocation));

  //       const { code, message } = error;
  //       // if (
  //       //   getLocation !== "" &&
  //       //   id &&
  //       //   code !== "UNAVAILABLE" &&
  //       //   Platform.OS === "android"
  //       // ) {
  //       //   sendSMS(JSON.parse(getLocation), id, click);
  //       // }
  //       console.log("get_location=ios===", location, id);
  //       // if (getLocation !== "" && id && Platform.OS === "ios") {
  //       sendSMS(location, id, click);
  //       // }
  //       console.warn("error -- location", code, message);
  //       sendErrorReport(code, "SMS_code");
  //       if (code === "UNAVAILABLE") {
  //         Toast.show(
  //           "Please enable your location service to send emergency alert."
  //         );
  //         GetLocation.openAppSettings();
  //       }
  //     });
  // }

  // this function for get current location
  const getLocationInBackground = async () => {
    console.log("in gegetLocationInBackground======");
    try {
      const location = await GetLocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000,
      });
      // console.log("Location:", location);
      return location;
    } catch (error) {
      console.error("Error getting location:", error);
    }
  };
  const [location, setLocation] = useState(null);

  const handleLocationChange = (position) => {
    setLocation(position);
    console.log("ddddd=======location=====", position);
  };

  const onPress = () => {
    navigator.geolocation.watchPosition(handleLocationChange, {
      enableHighAccuracy: true,
      maximumAge: 60000,
      timeout: 10000,
    });
  };
  async function getCurrentLocation(id, click = "") {
    console.log("called----1");

    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: !appState ? 15000 : 0,
    })
      .then((location) => {
        AsyncStorage.setItem("setLocation", JSON.stringify(location));
        if (id) {
          console.log("in1--->", id);
          // sendErrorReport(location, "SMS_location");
          // sendErrorReport(id, "SMS_id111");

          sendSMS(location, id, click);
        }
      })
      .catch(async (error) => {
        const getLocation = await AsyncStorage.getItem("setLocation");

        // Safely parse location data with validation
        let parsedLocation = null;
        if (
          getLocation &&
          getLocation.trim() !== "" &&
          getLocation !== "null"
        ) {
          try {
            parsedLocation = JSON.parse(getLocation);
            sendErrorReport(parsedLocation, "get_location");
          } catch (parseError) {
            console.warn(
              "Failed to parse location JSON:",
              parseError,
              "Raw data:",
              getLocation
            );
            sendErrorReport({ error: "Invalid location data" }, "get_location");
          }
        } else {
          sendErrorReport(
            { error: "No location data available" },
            "get_location"
          );
        }

        sendErrorReport(id, "SMS_id122");
        console.log("get location in eles---------", getLocation);
        const { code, message } = error;

        if (
          parsedLocation &&
          id &&
          code !== "UNAVAILABLE" &&
          Platform.OS === "android"
        ) {
          sendSMS(parsedLocation, id, click);
        }
        if (parsedLocation && id && Platform.OS === "ios") {
          sendSMS(parsedLocation, id, click);
        }
        console.warn("error -- location", code, message);
        sendErrorReport(code, "SMS_code");
        if (code === "UNAVAILABLE") {
          Toast.show(
            "Please enable your location service to send emergency alert."
          );
          GetLocation.openAppSettings();
        }
      });
  }
  // console.log("RedirectLS -> languageData", languageData);
  // this function for send SMS for emergency alert
  async function sendSMS(location, id, click = "") {
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    // console.log(
    //   "location====== sms----out--",
    //   location.latitude,
    //   typeof location.longitude
    // );
    if (location.latitude && location.longitude) {
      console.log("location======in sms------", location, click);
      // try {
      //   const response = await getApiData(
      //     BaseSetting.endpoints.sendEmergencyMessageToUser,
      //     "POST",
      //     {
      //       latitude: location?.latitude,
      //       longitude: location?.longitude,
      //       child_id: id || activeChildDetail?.id,
      //       lang_code: languageData?.languageData || "es",
      //       btn: click || "",
      //       app_state: appState ? "active" : "background",
      //     },
      //     headers
      //   );
      //   console.log("ddddd api data----", {
      //     latitude: location.latitude,
      //     longitude: location.longitude,
      //     child_id: id || activeChildDetail.id,
      //     lang_code: languageData?.languageData || "es",
      //     btn: click || "",
      //     app_state: appState ? "active" : "background",
      //   });
      //   console.log(
      //     "🚀 ~ file: index.js ~ line 334 ~ sendSMS ~ response",
      //     response
      //   );
      //   if (response.success) {
      //     setAlertData({
      //       title: "Emergency",
      //       message: "Emergency sms sent",
      //       type: "",
      //     });

      //     setVisible(true);

      //     if (!appState) {
      //       addAlert("Emergency", "Emergency sms sent.");
      //       pushNotification("Emergency", "Emergency sms sent.");
      //     }
      //   } else {
      //     console.log("smssssssss", response);
      //   }
      //   Toast.show(response.message);
      //   dispatch(BluetoothActions.setActiveChildDetail({}));
      // } catch (error) {
      //   console.log("disconnect device error ===", error);
      //   Toast.show("Something went wrong while sending sms");
      //   sendErrorReport(error, "send_sms");
      // }
    }
  }

  useEffect(() => {
    if (!isBleConnected && emergencyAlert && accessToken !== "") {
      setAlertData({
        title: translate("childLeftInSeatTitle"),
        message: translate("childLeftInSeatMessage"),
        type: "leftChild",
        carPlayTitle: translate("childLeftInSeatTitle"),
      });
      setVisible(true);
      console.log("set sound---------true----");
      setSound(true);
      if (!appState) {
        pushNotification(
          "Child Seat",
          "Your child is still in their car seat. Please ensure they are with a responsible adult.",
          true // Include location for emergency alerts
        );
      }
      addAlert(
        "Child Seat",
        "Your child is still in their car seat. Please ensure they are with a responsible adult."
      );
      console.log("emeregencyy called");
      BackgroundTimer.setTimeout(() => {
        console.log("emeregencyy called", alertData.type);
        if (emergencyAlert && alertData.type === "leftChild") {
          console.log("getCurrentLocation callled---4");
          getCurrentLocation(activeChildDetail.id, "");
        }
      }, 30000);
    }
  }, [emergencyAlert, isBleConnected]);
  // Check and request location permissions
  const requestLocationPermission = async () => {
    const permission =
      Platform.OS === "ios"
        ? PERMISSIONS.IOS.LOCATION_ALWAYS
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

    const result = await check(permission);

    if (result === RESULTS.DENIED) {
      const requestResult = await request(permission);
      return requestResult === RESULTS.GRANTED;
    }

    return result === RESULTS.GRANTED;
  };

  useEffect(() => {
    console.log("OPENING Temperature Modal ==>", tempModal);
    if (tempModal) {
      setAlertData({
        title: translate("temperatureAlertTitle"),
        message: "",
        type: "temp",
        carPlayTitle: translate("temperatureAlertTitle"),
      });
      setVisible(true);
    }
  }, [tempModal]);

  useEffect(() => {
    if (
      isBleConnected &&
      SW === 1 &&
      accessToken !== "" &&
      !productConnect &&
      !tempModal
    ) {
      console.log("setalert data called==15");
      setAlertData({
        title: "",
        message: `${translate("productConnected", {
          child_name: activeChildDetail?.nick_name || "Child",
        })}`,
        type: "",
        carPlayTitle: "",
      });
      // setVisible(true);
      setProductConnect(true);
      if (!appState) {
        pushNotification(
          "Child Seat",
          `${translate("productConnected", {
            child_name: activeChildDetail?.nick_name || "child",
          })}`
        );
      }
      addAlert(
        "Child Seat",
        `${translate("productConnected", {
          child_name: activeChildDetail?.nick_name || "Child",
        })}`
      );
    }
  }, [isBleConnected, parseBleData]);
  //mansi   commented-----------sw
  // useEffect(() => {
  //   // handle power alert
  //   if (powerDetail.value !== SW) {
  //     setTimeout(() => {
  //       setPowerDetail({ powerTime: moment(), value: SW });
  //     }, 500);
  //   }

  //   // if (parseBleData.SW === 0 && accessToken !== "") {
  //   // }
  //   setTimeout(() => {
  //     if (
  //       SW === 0 &&
  //       accessToken !== "" &&
  //       !tempModal &&
  //       alertData.type !== "leftChild"
  //     ) {
  //       setAlertData({
  //         title: translate("leftSeatTitle"),
  //         message: translate("leftSeatMessage"),
  //         type: "",
  //       });
  //       // setVisible(true);
  //       setProductConnect(false);
  //       if (!appState) {
  //         pushNotification(
  //           "Child has left seat",
  //           "The child is out of seat and the system will power down in 30 seconds."
  //         );
  //       }
  //       addAlert(
  //         "Child has left seat",
  //         "The child is out of seat and the system will power down in 30 seconds."
  //       );
  //     }
  //   }, 300);
  // }, [SW]);

  useEffect(() => {
    // handle fan alert
    if (
      parseBleData &&
      !isEmpty(connectedDeviceDetail) &&
      (connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
        "babyauto-csb" ||
        connectedDeviceDetail?.device_bluetooth_name.toLowerCase() ===
          "reebaby-csa")
    ) {
      if (parseBleData.s11) {
        const val = parseBleData.s11;

        if (val !== fanStatus) {
          const message = val ? "fanOn" : "fanOff";
          console.log("setalert data called==16");
          setAlertData({
            title: translate("fanTitle"),
            message: translate(message),
            type: "",
            carPlayTitle: translate("fanTitle"),
          });
          // setVisible(true);
          setFanStatus(val);
          if (!appState) {
            pushNotification("Fan", val ? "Fan is on" : "Fan is off");
          }
          addAlert("Fan", val ? "Fan is on" : "Fan is off");
        }
      }
    }

    const powerAlert = moment.duration(nowTime.diff(powerDetail.powerTime));
    if (powerAlert && powerAlert.seconds() >= 60) {
      setTimeout(() => {
        console.log("setalert data called==17");
        setAlertData({
          title: translate("powerDownTitle"),
          message: translate("powerDownMessage"),
          type: "",
          carPlayTitle: translate("powerDownTitle"),
        });
        setVisible(true);
        if (!appState) {
          pushNotification("Power Down", "Powering down");
        }
        addAlert("Power Down", "Powering down");
      }, 500);
    }
  }, [bleData]);

  function handleModal(type) {
    setVisible((pre) => !pre);
    if (type === "ok" && alertData.type === "leftChild") {
      console.log("getCurrentLocation callled---6");
      getCurrentLocation(activeChildDetail.id, "click");
    }

    if (type === "cancel" && alertData.type === "leftChild") {
      setstartRead(false);
    }

    if (alertData.type === "temp") {
      dispatch(setTempModal(false));
    }

    setAlertData({ title: "", message: "", type: "", carPlayTitle: "" });
    if (emergencyAlert) {
      dispatch(setEmergencyAlert(false));
    }
    console.log("set sound---------false----");
    setSound(false);
  }

  // Helper function to safely parse JSON from AsyncStorage
  const safeJsonParse = (jsonString, fallback = null) => {
    if (!jsonString || jsonString.trim() === "" || jsonString === "null") {
      return fallback;
    }
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn("Failed to parse JSON:", error, "Raw data:", jsonString);
      return fallback;
    }
  };

  // Enhanced push notification function with location support
  async function pushNotification(title, message, includeLocation = false) {
    console.log("PUSH NOTIFICATION", title, message);

    let notificationData = {
      channelId: "default-channel-id",
      title, // (optional)
      message, // (required)
      ignoreInForeground: false,
    };

    // Add location data if requested
    if (includeLocation) {
      try {
        const location = await GetLocation.getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 5000,
        });

        // Add location to notification data
        notificationData.userInfo = {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy,
          timestamp: new Date().toISOString(),
        };

        // Also add location to message for immediate visibility
        notificationData.message = `${message}\nLocation: ${location.latitude.toFixed(
          6
        )}, ${location.longitude.toFixed(6)}`;

        console.log("PUSH NOTIFICATION WITH LOCATION", notificationData);
      } catch (error) {
        console.warn("Failed to get location for notification:", error);
        // Fallback to cached location if available
        try {
          const cachedLocation = await AsyncStorage.getItem("setLocation");
          if (
            cachedLocation &&
            cachedLocation.trim() !== "" &&
            cachedLocation !== "null"
          ) {
            const parsedLocation = safeJsonParse(cachedLocation);
            // Validate that parsed location has required properties
            if (
              parsedLocation &&
              parsedLocation.latitude &&
              parsedLocation.longitude
            ) {
              notificationData.userInfo = {
                latitude: parsedLocation.latitude,
                longitude: parsedLocation.longitude,
                accuracy: parsedLocation.accuracy || 0,
                timestamp: new Date().toISOString(),
                cached: true,
              };
              notificationData.message = `${message}\nLocation: ${parsedLocation.latitude.toFixed(
                6
              )}, ${parsedLocation.longitude.toFixed(6)} (cached)`;
            } else {
              console.warn("Cached location data is invalid:", parsedLocation);
            }
          } else {
            console.warn("No valid cached location available");
          }
        } catch (cacheError) {
          console.warn("Failed to get cached location:", cacheError);
        }
      }
    }

    PushNotification.localNotification(notificationData);
  }

  // Utility function to get location from notification
  const getLocationFromNotification = async () => {
    try {
      const notificationLocation = await AsyncStorage.getItem(
        "notificationLocation"
      );
      if (
        notificationLocation &&
        notificationLocation.trim() !== "" &&
        notificationLocation !== "null"
      ) {
        const locationData = safeJsonParse(notificationLocation);
        // Validate that parsed location has required properties
        if (locationData && locationData.latitude && locationData.longitude) {
          console.log("Retrieved location from notification:", locationData);
          return locationData;
        } else {
          console.warn("Notification location data is invalid:", locationData);
          return null;
        }
      }
      return null;
    } catch (error) {
      console.warn("Failed to get location from notification:", error);
      return null;
    }
  };

  // Example function to demonstrate using location from notifications
  const handleEmergencyWithLocation = async () => {
    const locationData = await getLocationFromNotification();
    if (locationData) {
      console.log("Emergency alert with location:", {
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp,
        googleMapsUrl: `https://maps.google.com/?q=${locationData.latitude},${locationData.longitude}`,
      });

      // You can now use this location data for:
      // 1. Sending SMS with location
      // 2. Updating UI with location info
      // 3. Logging location for emergency services
      // 4. Opening maps app with location
    } else {
      console.log("No location data available from notification");
    }
  };

  const currentRoute = navRef?.getCurrentRoute()?.name || "";
  const dontShowAlertOn = [
    "SplashScreen",
    "Walkthrough",
    "RedirectLS",
    "Login",
    "Signup",
    "ForgotPassword",
    "Otp",
  ].includes(currentRoute);
  const alertVisible = (visibleRef?.current && !dontShowAlertOn) || false;

  // const playRing = () => (
  //   <Video
  //     source={require("../../config/siren.wav")}
  //     ref={playerRef}
  //     audioOnly
  //     poster=""
  //     paused={!sound}
  //     ignoreSilentSwitch="ignore"
  //     repeat
  //     volume={1.0}
  //     muted={false}
  //     playInBackground={true}
  //     playWhenInactive={true}
  //     onEnd={() => {
  //       if (Platform.OS !== "ios") {
  //         console.log("set sound---------flase----");
  //         setSound(false);
  //       }
  //     }}
  //   />
  // );
  useEffect(() => {
    console.log("alert visible=====", alertData.message);
    dispatch(setIsAlert(alertVisible));
    const obj = {
      title: alertData.title,
      message: alertData.message,
      type: alertData.type,
      carPlayTitle: alertData.carPlayTitle,
    };
    dispatch(setAlertDataRedux(obj));
  }, [alertVisible, alertData]);
  let screenName = "";
  if (navRef !== undefined) {
    const currentScreen = navRef?.getCurrentRoute()?.name;
    screenName = currentScreen; // ChildInfo
    // console.log("curreen scre-------------", screenName, alertVisible, visible);
  }
  return (
    <>
      <CAlert
        visible={screenName === "DASHBOARD" ? false : alertVisible} //{alertVisible}
        onRequestClose={handleModal}
        onCancelPress={() => handleModal("cancel")}
        onOkPress={() => handleModal("ok")}
        type={alertData.type !== "" ? alertData.type : "tempAlert"}
        alertTitle={alertData.title}
        alertMessage={alertData.message}
        agreeTxt={
          alertData.type === "leftChild"
            ? translate("sendSMS")
            : translate("alertOkBtn")
        }
        cancelTxt={
          alertData.type === "leftChild"
            ? translate("responsibleAdult")
            : translate("alertCancelBtn")
        }
      />
      {/* {playRing()} */}
    </>
  );
};

export default TempAlert;
