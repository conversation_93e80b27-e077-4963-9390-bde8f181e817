/* eslint-disable quotes */
import React, { useEffect } from "react";
import { Text, View, TouchableOpacity } from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome5";
import MCIcon from "react-native-vector-icons/MaterialCommunityIcons";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import {
  safeUseSharedValue,
  safeUseAnimatedStyle,
  safeWithTiming,
} from "../../utils/animationSafety";
import { useTheme } from "@react-navigation/native";
import moment from "moment";
import { CustomIcon } from "../../config/LoadIcons";
import styles from "./styles";
// import { translate } from "../../lang/Translate";

const AlertCard = (props) => {
  const {
    largeIcon,
    // smallIcon,
    // waveIcon,
    title,
    onPress = () => {},
    time,
    rightIcon,
    altMsg,
    // childName,
    animTime = 1000,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const listAlertAnim = safeUseSharedValue(600, 600); // Start from right side
  const opacityAnim = safeUseSharedValue(0, 0);

  const alertStyleAnim = safeUseAnimatedStyle(
    () => {
      return {
        transform: [
          {
            translateX: safeWithTiming(listAlertAnim.value, {
              duration: animTime,
            }),
          },
        ],
        opacity: safeWithTiming(opacityAnim.value, {
          duration: animTime,
        }),
      };
    },
    {
      transform: [{ translateX: 0 }],
      opacity: 1,
    }
  );

  useEffect(() => {
    try {
      // Use runOnJS to safely update shared values
      const startAnimation = () => {
        opacityAnim.value = 1;
        listAlertAnim.value = 0; // Animate to center position
      };

      // Small delay to ensure component is mounted
      const timer = setTimeout(startAnimation, 100);

      return () => clearTimeout(timer);
    } catch (error) {
      console.warn("Animation setup error:", error);
    }
  }, []);

  return (
    <Animated.View style={[styles.renderView, alertStyleAnim]}>
      <View
        style={[styles.cardView, { backgroundColor: BaseColor.whiteColor }]}
      >
        <View style={styles.touchableContent}>
          <View
            style={[styles.icons, { backgroundColor: BaseColor.whiteColor }]}
          >
            <CustomIcon
              name={largeIcon}
              size={22}
              color={BaseColor.blueLight}
              //   style={styles.icons}
            />
            {/* {smallIcon ? (
              <View
                style={{
                  ...styles.icons,
                  position: "absolute",
                  backgroundColor: "#e91e63",
                  bottom: 0,
                  right: 0,
                  borderRadius: 50,
                  height: 18,
                  width: 18,
                }}
              >
                <CustomIcon
                  name={smallIcon}
                  size={8}
                  color={BaseColor.whiteColor}
                />
              </View>
            ) : ( */}
            <View
              style={{
                ...styles.icons,
                position: "absolute",
                width: 18,
                height: 18,
                backgroundColor: "#e91e63",
                bottom: 0,
                right: 0,
                borderRadius: 50,
              }}
            >
              <MCIcon name="sine-wave" size={8} color={BaseColor.whiteColor} />
            </View>
            {/* )} */}
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={onPress}
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <View style={styles.contentTextView}>
              <Text style={[styles.title, { color: BaseColor.blackColor }]}>
                {title}
              </Text>
              {/* <Text style={[styles.time, { color: BaseColor.textGrey }]}>
                {childName}
              </Text> */}
              <Text style={styles.time}>{moment(time).fromNow()}</Text>
              {altMsg ? <Text style={styles.altMsg}>{altMsg}</Text> : null}
            </View>
            <FAIcon name={rightIcon} size={16} color={BaseColor.textGrey} />
          </TouchableOpacity>
        </View>
        {/* {altMsg == "Cry Alert" ? (
          <View>
            <View
              style={[
                styles.horizontalLine,
                { backgroundColor: BaseColor.textGrey },
              ]}
            />
            <View style={styles.textInputView}>
              <TextInput
                placeholder={translate("listenIntoAlert")}
                style={styles.textInput}
                placeholderTextColor={BaseColor.placeHolderColor}
              />
              <TouchableOpacity
                activeOpacity={0.7}
                style={[
                  styles.sendIcon,
                  {
                    backgroundColor: BaseColor.blueLight,
                  },
                ]}
              >
                <CustomIcon
                  name="send-2"
                  size={16}
                  color={BaseColor.whiteColor}
                />
              </TouchableOpacity>
            </View>
          </View>
        ) : null} */}
      </View>
    </Animated.View>
  );
};

export default AlertCard;
