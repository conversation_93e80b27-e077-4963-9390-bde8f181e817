import React, { useEffect, useRef } from "react";
import { NativeModules } from "react-native";
import { useSelector } from "react-redux";

const { RNAndroidAutoManager } = NativeModules;

const AndroidAutoComponent = () => {
  const bleData = useSelector((state) => state.bluetooth.bleData);
  const user = useSelector((state) => state.auth.userData);
  const alertData = useSelector((state) => state.bluetooth.alertData);
  const activeChildDetail = useSelector(
    (state) => state.bluetooth.activeChildDetail
  );
  const bleDataRef = useRef();
  bleDataRef.current = bleData;

  const alertDataRef = useRef();
  alertDataRef.current = alertData;

  const activeChildRef = useRef();
  activeChildRef.current = activeChildDetail;
  useEffect(() => {
    if (!RNAndroidAutoManager) {
      console.error("RNAndroidAutoManager is null!");
      return;
    }

    // Safety check for required native methods
    if (
      typeof RNAndroidAutoManager.updateBLEData !== "function" ||
      typeof RNAndroidAutoManager.isAppActive !== "function"
    ) {
      console.error(
        "RNAndroidAutoManager native methods are missing!",
        RNAndroidAutoManager
      );
      return;
    }

    const sendDataToAndroidAuto = async () => {
      try {
        // Fetch active state from native store
        const isActive = await RNAndroidAutoManager.isAppActive();

        const parsedBleData =
          typeof bleDataRef.current === "string"
            ? JSON.parse(bleDataRef.current)
            : bleDataRef.current || {};

        const parsedAlertData = alertDataRef.current || {};

        const s6 = parsedBleData?.s6 || 0;
        const s7 = parsedBleData?.s7 || 0;
        const s8 = parsedBleData?.s8 || 0;
        const s10 = parsedBleData?.s10 || 0;
        const childName = user?.full_name || "Guest";

        const alertActive = !!parsedAlertData.message;
        const alertTitle = parsedAlertData?.carPlayTitle || "Alert!";
        const alertMessage = parsedAlertData?.message || "";

        console.log("🚀 Sending to Android Auto", {
          isActive,
          s6,
          s7,
          s8,
          s10,
          childName,
          alertActive,
          alertTitle,
          alertMessage,
        });

        if (isActive && alertActive) {
          // Update Android Auto screen
          RNAndroidAutoManager.updateBLEData(
            s6,
            s7,
            s8,
            s10,
            childName,
            alertActive,
            alertTitle,
            alertMessage
          );
        }
      } catch (e) {
        console.error("Error sending data to Android Auto:", e);
      }
    };

    // Initial call
    sendDataToAndroidAuto();
  }, [bleData, alertData, activeChildDetail, user]);

  return null;
};

export default AndroidAutoComponent;
