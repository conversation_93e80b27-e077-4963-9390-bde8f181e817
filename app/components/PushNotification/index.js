/* eslint-disable no-nested-ternary */
/* eslint-disable quotes */
import React, { useEffect, useState } from "react";
import { Platform, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { isObject, isEmpty, isString } from "lodash";
import messaging from "@react-native-firebase/messaging";
import { withInAppNotification } from "../../lib/react-native-in-app-notification";
import BaseSetting from "../../config/setting";
import AuthActions from "../../redux/reducers/auth/actions";
import { getApiData } from "../../utils/apiHelper";
import InAppModal from "../InAppModal";
import { addAction, sendErrorReport } from "../../utils/commonFunction";

const IOS = Platform.OS === "ios";
// const PushNotification = React.forwardRef((props, ref) => {
/**
 *
 *@module PushNotification
 *
 */
const PushNotification = (props) => {
  const { navigation } = props;
  const { setUUid, setNotiCount } = AuthActions;
  const dispatch = useDispatch();
  const { uuid, userData, accessToken } = useSelector((state) => state.auth);
  const [notificationDetail, setNotificationDetail] = useState({});
  const [postModal, setPostModal] = useState(false);

  useEffect(() => {
    if (isEmpty(accessToken)) {
      const dontGo = ["SplashScreen", "Walkthrough", "RedirectLS"];
      const currentScreen = navigation.current?.getCurrentRoute().name;
      if (!dontGo.includes(currentScreen)) {
        navigation.current.navigate("RedirectLS");
      }
    }
  }, [accessToken]);

  useEffect(() => {
    if (isObject(userData) && !isEmpty(userData)) {
      checkNotificationPermission();
    }
  }, [props, userData, accessToken]);

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      "Content-Type": "application/json",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        "POST",
        {},
        headers
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
      }
    } catch (error) {
      console.log("error for device list ===", error);
      sendErrorReport(error, "get_device_list");
    }
  }

  // this function for check notification permission
  async function checkNotificationPermission() {
    const hasPermission = await messaging().hasPermission();
    try {
      const enabled =
        hasPermission === messaging.AuthorizationStatus.AUTHORIZED ||
        hasPermission === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        const authorizationStatus = await messaging().requestPermission();
        if (authorizationStatus === messaging.AuthorizationStatus.AUTHORIZED) {
        } else if (
          authorizationStatus === messaging.AuthorizationStatus.PROVISIONAL
        ) {
        }
      }
      if (!uuid && isObject(userData) && !isEmpty(userData)) {
        setTimeout(() => {
          getFcmToken();
        }, 1000);
      }
    } catch (error) {
      sendErrorReport(error, "notification_permission");
      console.log("checkApplicationPermission -> error", error);
    }
  }

  // this function for send token to server
  /** this function for send token to server
   * @function sendFcmToken
   * @param {object} data {}
   */
  async function sendFcmToken(token) {
    const data = {
      token,
      platform: IOS ? "IOS" : "ANDROID",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addToken,
        "POST",
        data,
        {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        }
      );
    } catch (err) {
      console.log("ERRR==", err);
      sendErrorReport(err, "send_fcm_token");
    }
  }
  // this function for get firebase token
  async function getFcmToken() {
    try {
      const fcmToken = await messaging().getToken();
      console.log("🚀 ~ getFcmToken ~ fcmToken:", fcmToken);

      if (fcmToken && accessToken) {
        sendFcmToken(fcmToken);
        setFCMListeners();
        dispatch(setUUid(fcmToken));
      }
    } catch (e) {
      console.log("❌ Error getting FCM token:", e);
    }
  }

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      console.log("On message", remoteMessage);
      handleNotification(remoteMessage.data, true);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const openNotification = messaging().onNotificationOpenedApp(
      async (remoteMessage) => {
        if (isObject(remoteMessage?.data) && !isEmpty(remoteMessage?.data)) {
          handleNotification(remoteMessage?.data, false);
        }
      }
    );

    return openNotification;
  }, []);

  // this function set listeners for firebase
  async function setFCMListeners() {
    try {
      const onTokenRefreshListener = messaging().onTokenRefresh((fcmToken) => {
        if (fcmToken) {
          console.log("setFCMListeners -> fcmToken", fcmToken);
        }
      });

      console.log("onTokenRefreshListener ==", onTokenRefreshListener);

      messaging()
        .getInitialNotification()
        .then(async (remoteMessage) => {
          if (remoteMessage) {
            console.log(
              "Notification caused app to open from quit state:",
              remoteMessage
            );
            // handleNotification(remoteMessage);
          }
        });
    } catch (error) {
      console.log("setFCMListeners -> error", error);
      sendErrorReport(error, "fcm_listner");
    }
  }

  // this function for handle notification
  async function handleNotification(notificationData, bool) {
    const nData =
      isString(notificationData?.objData) && !isEmpty(notificationData?.objData)
        ? JSON.parse(notificationData?.objData) || notificationData?.objData
        : {};

    setNotificationDetail(notificationData);

    if (
      isObject(nData) &&
      !isEmpty(nData) &&
      (nData.campaign_type === "push_message" ||
        notificationData.type === "test_post")
    ) {
      if (!postModal) {
        setNotificationDetail(nData);
        setPostModal(true);
      }

      if (nData.campaign_type === "push_message") {
        addAction(nData, "viewed", accessToken);
      }
    } else {
      setNotificationDetail(notificationData);
    }
    // condition for setting notification badge
    // if (has(notificationData, 'badge') && Number(notificationData.badge) > 0) {
    //   dispatch(setNotiBadge(notificationData.badge));
    // }
    props.showNotification({
      title: notificationData.title || "Title",
      message: notificationData.body || "Message",
      onPress: () => {},
    });
  }

  if (postModal) {
    return (
      <InAppModal
        visible={postModal}
        detail={notificationDetail}
        campaignType={
          notificationDetail?.campaign_type === "feed_post" ? "feed_post" : ""
        }
        button={notificationDetail?.button_info || []}
        title={
          !isEmpty(notificationDetail) ? notificationDetail?.text_info : {}
        }
        image={
          !isEmpty(notificationDetail)
            ? notificationDetail?.campaign_type === "feed_post"
              ? notificationDetail.media_link
              : notificationDetail?.post_file
            : ""
        }
        position={
          !isEmpty(notificationDetail)
            ? notificationDetail?.message_position
            : ""
        }
        onClose={() => {
          setPostModal(false);
          getBadgeCount();
          addAction(notificationDetail, "clicked", accessToken);
        }}
      />
    );
  }
  return <View />;
};

PushNotification.propTypes = {};

PushNotification.defaultProps = {};

export default withInAppNotification(PushNotification);
