/**
 * Animation Safety Utilities for React Native Reanimated
 * Provides safe wrappers and error handling for animations on Android 15
 */

import { Platform } from 'react-native';

// Global error handler for Reanimated crashes
export const setupAnimationErrorHandler = () => {
  if (Platform.OS === 'android') {
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const errorMessage = args.join(' ');
      
      // Catch and handle Reanimated-related errors
      if (
        errorMessage.includes('reanimated') ||
        errorMessage.includes('Animation') ||
        errorMessage.includes('JSI') ||
        errorMessage.includes('worklet')
      ) {
        console.warn('Reanimated error caught and handled:', errorMessage);
        return;
      }
      
      originalConsoleError(...args);
    };
  }
};

// Safe wrapper for useSharedValue
export const safeUseSharedValue = (initialValue, fallback = 0) => {
  try {
    const { useSharedValue } = require('react-native-reanimated');
    return useSharedValue(initialValue);
  } catch (error) {
    console.warn('useSharedValue failed, using fallback:', error);
    return { value: fallback };
  }
};

// Safe wrapper for useAnimatedStyle
export const safeUseAnimatedStyle = (styleFunction, fallbackStyle = {}) => {
  try {
    const { useAnimatedStyle } = require('react-native-reanimated');
    return useAnimatedStyle(() => {
      'worklet';
      try {
        return styleFunction();
      } catch (error) {
        console.warn('Animation style error:', error);
        return fallbackStyle;
      }
    });
  } catch (error) {
    console.warn('useAnimatedStyle failed, using fallback:', error);
    return fallbackStyle;
  }
};

// Safe wrapper for withTiming
export const safeWithTiming = (toValue, config = {}) => {
  try {
    const { withTiming } = require('react-native-reanimated');
    return withTiming(toValue, config);
  } catch (error) {
    console.warn('withTiming failed, using direct value:', error);
    return toValue;
  }
};

// Initialize animation safety on app start
setupAnimationErrorHandler();
