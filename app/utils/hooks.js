import { useCallback } from "react";
import { Platform } from "react-native";
import DeviceInfo from "react-native-device-info";
import {
  check,
  checkMultiple,
  PERMISSIONS,
  PermissionStatus,
  RESULTS,
  request,
} from "react-native-permissions";

export const usePermissions = () => {
  const requestCameraAccess = useCallback(async () => {
    let result = null;
    const permission = await checkMultiple([
      PERMISSIONS?.IOS?.CAMERA,
      PERMISSIONS?.ANDROID?.CAMERA,
    ]);
    if (
      permission[PERMISSIONS?.IOS?.CAMERA] !== RESULTS.GRANTED ||
      permission[PERMISSIONS?.ANDROID?.CAMERA] !== RESULTS.GRANTED
    ) {
      result = await request(
        Platform.select({
          android: PERMISSIONS?.ANDROID?.CAMERA,
          ios: PERMISSIONS?.IOS?.CAMERA,
        })
      );
    } else {
      result =
        Platform.select({
          android: permission[PERMISSIONS?.ANDROID?.CAMERA],
          ios: permission[PERMISSIONS?.IOS?.CAMERA],
        }) ?? null;
    }
    return result;
  }, []);

  const requestGalleryAccess = async () => {
    const apiLevel = await DeviceInfo.getApiLevel();
    let permissionToCheck;

    if (Platform.OS === "ios") {
      permissionToCheck = PERMISSIONS.IOS.PHOTO_LIBRARY;
    } else {
      permissionToCheck =
        apiLevel >= 33
          ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
          : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
    }

    const status = await check(permissionToCheck);
    console.log("Current permission status:", status);

    if (status !== RESULTS.GRANTED) {
      const result = await request(permissionToCheck);
      console.log("Requested permission result:", result);
      return result;
    }

    return status; // already granted
  };

  return { requestCameraAccess, requestGalleryAccess };
};
