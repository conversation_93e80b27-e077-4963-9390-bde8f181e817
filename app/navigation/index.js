/* eslint-disable quotes */
import "react-native-gesture-handler";
import React, { useEffect, useRef, useState } from "react";
import {
  DarkTheme,
  DefaultTheme,
  NavigationContainer,
} from "@react-navigation/native";
import {
  CardStyleInterpolators,
  createStackNavigator,
} from "@react-navigation/stack";
import DatePicker from "react-native-datepicker";
import { View, TextInput, Text, Platform } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createDrawerNavigator } from "@react-navigation/drawer";
import notifee from "@notifee/react-native";
import { useDispatch, useSelector } from "react-redux";
import { EventRegister } from "react-native-event-listeners";
import RedirectLS from "../screens/RedirectLS";
import ForgotPassword from "../screens/ForgotPassword";
import Otp from "../screens/Otp";
import SplashScreen from "../screens/SplashScreen";
import Walkthrough from "../screens/Walkthrough";
import Login from "../screens/Login";
import Signup from "../screens/Signup";
import Gender from "../screens/Gender/index";
import FamilyProfiles from "../screens/FamilyProfiles/index";
import ChildInfo from "../screens/ChildInfo/index";
import Dashboard from "../screens/Dashboard";
import Home from "../screens/Home";
import Devices from "../screens/Devices";
import TabBar from "./TabBar";
import Setting from "../screens/Setting/index";
import Alerts from "../screens/Alert/index";
import CDeviceList from "../screens/ConDeviceList/index";
import Connect from "../screens/Connect";
import QRScanner from "../screens/QRScanner";
import ChatScreen from "../screens/ChatScreen";
import { translate } from "../lang/Translate";
import UpdatePassword from "../screens/UpdatePassword/index";
import PushNotification from "../components/PushNotification";
import FAQScreen from "../screens/FAQScreen";
import EditDevice from "../screens/EditDevice";
// import NetworkModal from "../screens/NetworkModal";
import Products from "../screens/Products";
import AuthAction from "../redux/reducers/auth/actions";
import BluetoothAction from "../redux/reducers/bluetooth/actions";
import BaseColor, { DarkBaseColor } from "../config/colors";
import ProductDetail from "../screens/ProductDetail";
import { getApiData } from "../utils/apiHelper";
import BaseSetting from "../config/setting";
import messaging from "@react-native-firebase/messaging";
import CAlert from "../components/CAlert";
import TempAlert from "../components/TempAlert";
import { getLocationAndStore, sendErrorReport } from "../utils/commonFunction";
import MyQRcode from "../screens/MyQRcode";
import TempSetting from "../screens/TempSetting";
import UserManual from "../screens/UserManual";
import MyAccount from "../screens/MyAccount";
import CarPlayComponent from "../components/MyCarPlay";
import AndroidAutoModule from "../components/AndroidAuto/AndroidAutoComponent";
import RemotePushController from "../components/Common/RemotePushController";

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;
DatePicker.defaultProps = DatePicker.defaultProps || {};
DatePicker.defaultProps.allowFontScaling = false;

const NavStart = () => {
  const dispatch = useDispatch();
  const { setBaseColor, setDarkmode, setBrandToken } = AuthAction;
  const darkmode = useSelector((state) => state.auth.darkmode);
  const accessToken = useSelector((state) => state.auth.accessToken);

  if (darkmode === false) {
    dispatch(setBaseColor(BaseColor));
  } else {
    dispatch(setBaseColor(DarkBaseColor));
  }

  const Stack = createStackNavigator();
  const Tab = createBottomTabNavigator();
  const Drawer = createDrawerNavigator();

  const navigationRef = useRef();

  useEffect(() => {
    dispatch(BluetoothAction.setIsBleConnected(false));
    dispatch(BluetoothAction.setBleData({}));
    getLocationAndStore();
    const eventListener = EventRegister.addEventListener(
      "changeAppTheme",
      (data) => {
        setdarkApp(data);
        setDarkmode(data);
      }
    );
    return () => {
      EventRegister.removeEventListener(eventListener);
    };
  }, []);

  async function onDisplayNotification() {
    // Request permissions (required for iOS)
    await notifee.requestPermission();
    // console.log('Triggering...')
    // NativeModules.MaxRCTCarPlayNotificationManager.sendCarPlayNotification('Samrez', 'Ikram');
    await notifee.setNotificationCategories([
      {
        id: "carplayNotifee",
        allowInCarPlay: true,
      },
    ]);

    const notificationConfig = await notifee.getNotificationSettings();

    if (notificationConfig?.ios?.authorizationStatus > 0) {
      // Display a notification
      await notifee.displayNotification({
        title: "Notification Title 1",
        body: "Main body content of the notification 1",
        ios: {
          categoryId: "carplayNotifee",
        },
      });
    }

    console.log("NOTIFICATIONS CONFIG ", notificationConfig);
  }

  const [darkApp, setdarkApp] = useState(darkmode);

  const lightTheme = {
    ...DefaultTheme,
    colors: {
      ...BaseColor,
      ulla: "#ff0000",
    },
  };

  const darkTheme = {
    ...DarkTheme,
    colors: {
      ...DarkBaseColor,
      ulla: "#ff0000",
    },
  };

  const appTheme = darkApp ? darkTheme : lightTheme;

  const sendPackage = () => {
    const data = {
      package_name: "com.cbtsmartcar",
    };

    const headers = {
      "Content-Type": "application/json",
      // authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    getApiData(BaseSetting.endpoints.sendPackage, "post", data, headers)
      .then((respose) => {
        if (respose.brand_token) {
          dispatch(setBrandToken(respose.brand_token));
        }
      })
      .catch((err) => {
        console.log("ERROR", err);
        sendErrorReport(err, "send_token_package");
      });
  };

  useEffect(() => {
    sendPackage();
  }, [accessToken]);

  messaging().onNotificationOpenedApp((remoteMessage) => {
    console.log(
      "messaging -> remoteMessage",
      JSON.parse(remoteMessage.data.objData)
    );
    // navigation.navigate(remoteMessage.data.type);
    // Alert.alert(remoteMessage.data);
  });

  const DashboardNavigationTabs = () => (
    <Tab.Navigator
      tabBar={(props) => (
        <View
          style={{
            backgroundColor: "#0000",
            position: "absolute",
            bottom: 0,
            right: 0,
            left: 0,
          }}
        >
          <TabBar {...props} />
        </View>
      )}
      initialRouteName={translate("home")}
    >
      <Tab.Screen name={translate("home")} component={Home} />
      <Tab.Screen name={translate("dashboard")} component={Dashboard} />
      <Tab.Screen name={translate("devices")} component={Devices} />
    </Tab.Navigator>
  );

  const DrawerNav = () => (
    <Drawer.Navigator
      initialRouteName="Home"
      drawerStyle={{ width: "100%" }}
      drawerContent={(props) => <Setting {...props} />}
      openByDefault={false}
    >
      <Drawer.Screen name="Dashboard" component={DashboardNavigationTabs} />
    </Drawer.Navigator>
  );

  return (
    <NavigationContainer ref={navigationRef} theme={appTheme}>
      <Stack.Navigator
        screenOptions={{
          cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
          headerMode: "screen",
          headerTintColor: "white",
          cardStyle: { backgroundColor: "white" },
        }}
      >
        <Stack.Screen
          name="SplashScreen"
          component={SplashScreen}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="Walkthrough"
          component={Walkthrough}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="RedirectLS"
          component={RedirectLS}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="Login"
          component={Login}
          options={{
            headerShown: false,
            cardStyleInterpolator:
              CardStyleInterpolators.forRevealFromBottomAndroid,
          }}
        />
        <Stack.Screen
          name="Signup"
          component={Signup}
          options={{
            headerShown: false,
            cardStyleInterpolator:
              CardStyleInterpolators.forRevealFromBottomAndroid,
          }}
        />
        <Stack.Screen
          name="ForgotPassword"
          component={ForgotPassword}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Otp"
          component={Otp}
          options={{ headerShown: false, gestureEnabled: false }}
          initialParams={{ login: false }}
        />
        <Stack.Screen
          name="UpdatePassword"
          component={UpdatePassword}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="Gender"
          component={Gender}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ChildInfo"
          component={ChildInfo}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="FamilyProfiles"
          component={FamilyProfiles}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="DrawerNav"
          component={DrawerNav}
          options={{
            headerShown: false,
            gestureEnabled: false,
            animationEnabled: false,
          }}
        />
        <Stack.Screen
          name="Alerts"
          component={Alerts}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="CDeviceList"
          component={CDeviceList}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Connect"
          component={Connect}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="MyAccount"
          component={MyAccount}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="TempSetting"
          component={TempSetting}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="UserManual"
          component={UserManual}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="MyQRcode"
          component={MyQRcode}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="QRScanner"
          component={QRScanner}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen
          name="ChatScreen"
          component={ChatScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="FAQScreen"
          component={FAQScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="EditDevice"
          component={EditDevice}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Products"
          component={Products}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ProductDetail"
          component={ProductDetail}
          options={{ headerShown: false }}
        />
        {/* <Stack.Screen
      name={translate("DashboardScreen")}
      component={Dashboard}
      options={{ headerShown: false, gestureEnabled: false }}
    /> */}
      </Stack.Navigator>
      {Platform.OS === "android" ? (
        <AndroidAutoModule />
      ) : (
        <CarPlayComponent navigation={navigationRef} />
      )}
      <RemotePushController />
      {/* <NetworkModal /> */}
      <TempAlert navigation={navigationRef} />
    </NavigationContainer>
  );
};

export default NavStart;
