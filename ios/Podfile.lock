PODS:
  - Base64 (1.1.2)
  - boost (1.76.0)
  - BugsnagReactNative (7.25.1):
    - React-Core
  - BVLinearGradient (2.8.3):
    - React-Core
  - CodePush (7.1.0):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.68.5)
  - FBReactNativeSpec (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.5)
    - RCTTypeSafety (= 0.68.5)
    - React-Core (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - Firebase (10.20.0):
    - Firebase/Core (= 10.20.0)
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - Permission-Camera (3.6.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.6.1):
    - RNPermissions
  - Permission-PhotoLibraryAddOnly (3.6.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.68.5)
  - RCTTypeSafety (0.68.5):
    - FBLazyVector (= 0.68.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.5)
    - React-Core (= 0.68.5)
  - React (0.68.5):
    - React-Core (= 0.68.5)
    - React-Core/DevSupport (= 0.68.5)
    - React-Core/RCTWebSocket (= 0.68.5)
    - React-RCTActionSheet (= 0.68.5)
    - React-RCTAnimation (= 0.68.5)
    - React-RCTBlob (= 0.68.5)
    - React-RCTImage (= 0.68.5)
    - React-RCTLinking (= 0.68.5)
    - React-RCTNetwork (= 0.68.5)
    - React-RCTSettings (= 0.68.5)
    - React-RCTText (= 0.68.5)
    - React-RCTVibration (= 0.68.5)
  - React-callinvoker (0.68.5)
  - React-Codegen (0.68.5):
    - FBReactNativeSpec (= 0.68.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.5)
    - RCTTypeSafety (= 0.68.5)
    - React-Core (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-Core (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.5)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/Default (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/DevSupport (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.5)
    - React-Core/RCTWebSocket (= 0.68.5)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-jsinspector (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-Core/RCTWebSocket (0.68.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.5)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsiexecutor (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - Yoga
  - React-CoreModules (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.5)
    - React-Codegen (= 0.68.5)
    - React-Core/CoreModulesHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-RCTImage (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-cxxreact (0.68.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-jsinspector (= 0.68.5)
    - React-logger (= 0.68.5)
    - React-perflogger (= 0.68.5)
    - React-runtimeexecutor (= 0.68.5)
  - React-jsi (0.68.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.68.5)
  - React-jsi/Default (0.68.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.68.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-perflogger (= 0.68.5)
  - React-jsinspector (0.68.5)
  - React-logger (0.68.5):
    - glog
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-ble-manager (10.1.5):
    - React-Core
  - react-native-blur (4.4.1):
    - React-Core
  - react-native-camera (3.40.0):
    - React-Core
    - react-native-camera/RCT (= 3.40.0)
    - react-native-camera/RN (= 3.40.0)
  - react-native-camera/RCT (3.40.0):
    - React-Core
  - react-native-camera/RN (3.40.0):
    - React-Core
  - react-native-carplay (2.3.0):
    - React
  - react-native-config (1.5.9):
    - react-native-config/App (= 1.5.9)
  - react-native-config/App (1.5.9):
    - React-Core
  - react-native-image-picker (5.4.0):
    - React-Core
  - react-native-netinfo (6.2.1):
    - React-Core
  - react-native-restart (0.0.22):
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-simple-toast (1.1.4):
    - React-Core
    - Toast (~> 4.0.0)
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-video (5.2.2):
    - React-Core
    - react-native-video/Video (= 5.2.2)
  - react-native-video/Video (5.2.2):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-perflogger (0.68.5)
  - React-RCTActionSheet (0.68.5):
    - React-Core/RCTActionSheetHeaders (= 0.68.5)
  - React-RCTAnimation (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.5)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTAnimationHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTBlob (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTBlobHeaders (= 0.68.5)
    - React-Core/RCTWebSocket (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-RCTNetwork (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTImage (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.5)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTImageHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-RCTNetwork (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTLinking (0.68.5):
    - React-Codegen (= 0.68.5)
    - React-Core/RCTLinkingHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTNetwork (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.5)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTNetworkHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTSettings (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.5)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTSettingsHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-RCTText (0.68.5):
    - React-Core/RCTTextHeaders (= 0.68.5)
  - React-RCTVibration (0.68.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.5)
    - React-Core/RCTVibrationHeaders (= 0.68.5)
    - React-jsi (= 0.68.5)
    - ReactCommon/turbomodule/core (= 0.68.5)
  - React-runtimeexecutor (0.68.5):
    - React-jsi (= 0.68.5)
  - ReactCommon/turbomodule/core (0.68.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.5)
    - React-Core (= 0.68.5)
    - React-cxxreact (= 0.68.5)
    - React-jsi (= 0.68.5)
    - React-logger (= 0.68.5)
    - React-perflogger (= 0.68.5)
  - ReactNativeGetLocation (2.2.1):
    - React-Core
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBluetoothStateManager (1.3.5):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPicker (2.4.8):
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDateTimePicker (3.5.2):
    - React-Core
  - RNDeviceInfo (8.7.1):
    - React-Core
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBMessaging (18.9.0):
    - Firebase/Messaging (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNInAppBrowser (3.7.0):
    - React-Core
  - RNNotifee (7.9.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.9.0)
  - RNNotifee/NotifeeCore (7.9.0):
    - React-Core
  - RNPermissions (3.6.1):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (2.18.1):
    - React-Core
  - RNShare (7.9.1):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (8.1.0):
    - React-Core
  - SSZipArchive (2.2.3)
  - Toast (4.0.0)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - "BugsnagReactNative (from `../node_modules/@bugsnag/react-native`)"
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - Firebase/Messaging
  - FirebaseCore
  - FirebaseCoreInternal
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - Permission-PhotoLibraryAddOnly (from `../node_modules/react-native-permissions/ios/PhotoLibraryAddOnly`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-ble-manager (from `../node_modules/react-native-ble-manager`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-carplay (from `../node_modules/react-native-carplay`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-simple-toast (from `../node_modules/react-native-simple-toast`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNBluetoothStateManager (from `../node_modules/react-native-bluetooth-state-manager`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNInAppBrowser (from `../node_modules/react-native-inappbrowser-reborn`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Base64
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - JWT
    - nanopb
    - PromisesObjC
    - SSZipArchive
    - Toast

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BugsnagReactNative:
    :path: "../node_modules/@bugsnag/react-native"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  Permission-PhotoLibraryAddOnly:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibraryAddOnly"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-ble-manager:
    :path: "../node_modules/react-native-ble-manager"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-carplay:
    :path: "../node_modules/react-native-carplay"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-simple-toast:
    :path: "../node_modules/react-native-simple-toast"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNBluetoothStateManager:
    :path: "../node_modules/react-native-bluetooth-state-manager"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNInAppBrowser:
    :path: "../node_modules/react-native-inappbrowser-reborn"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  boost: aa35e27b639bf67de7573b279486f84a3c6546c2
  BugsnagReactNative: 3369fa488e98e74c24ac3dc552a71fec8f290bd7
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CodePush: 7224d68c56c6e380cf3f3624e21e614ecb992058
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBLazyVector: 2b47ff52037bd9ae07cc9b051c9975797814b736
  FBReactNativeSpec: 0e0d384ef17a33b385f13f0c7f97702c7cd17858
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 476ee3e89abb49e07f822b48323c51c57124b572
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  Permission-Camera: 6df5352286fe05bb07b018e072c7b22b151e08ce
  Permission-PhotoLibrary: e3709d7e2b89fa0bbe1c38022ec6a2cf350a84ff
  Permission-PhotoLibraryAddOnly: 4d0c21db12dd436fd9a567c442f7d2f7646a9a6a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 98c12368076b400dc54d2cedb1a4574deee64356
  RCTRequired: 0f06b6068f530932d10e1a01a5352fad4eaacb74
  RCTTypeSafety: b0ee81f10ef1b7d977605a2b266823dabd565e65
  React: 3becd12bd51ea8a43bdde7e09d0f40fba7820e03
  React-callinvoker: 11abfff50e6bf7a55b3a90b4dc2187f71f224593
  React-Codegen: f8946ce0768fb8e92e092e30944489c4b2955b2d
  React-Core: ba6db3a3ab2447674c4a888a31459240693ed94e
  React-CoreModules: 6eb0c06a4a223fde2cb6a8d0f44f58b67e808942
  React-cxxreact: f66d3d2921d2f3a30bd3e45c75b1ca4e70680fc5
  React-jsi: 1219cf42778ebf3c5825bf271fa31607976d0d67
  React-jsiexecutor: 6547ddb57b140f21bf442d22fa32e3afc961daa9
  React-jsinspector: eb202e43b3879aba9a14f3f65788aec85d4e1ea9
  React-logger: cd36509e03584be6593f6df642c68453057f198f
  react-native-background-timer: 4638ae3bee00320753647900b21260b10587b6f7
  react-native-ble-manager: b7ce735a9a79123a5e5af567b164d9918cf688e2
  react-native-blur: 31d3c608b337f9d15d356148e0d88549fbf5fdc7
  react-native-camera: 22dbf8ce6a4c6a25ae49f4e2fa5859c5277f1631
  react-native-carplay: 013dc270dacdaf917968d7eb531e64eadd44bb1b
  react-native-config: d08b60865268872495a43e211059b4606131fe01
  react-native-image-picker: 44b1de2447dd55f08251294dc106ab98d0bde175
  react-native-netinfo: 9f09b27271b5c2ea60c0c4fbe62fdf92d2116234
  react-native-restart: f6f591aeb40194c41b9b5013901f00e6cf7d0f29
  react-native-safe-area-context: 667324e20fb3dd9c39c12d6036675ed90099bcd5
  react-native-simple-toast: 59d8b58998dac9f063b46d95f9fd7fd7caab6101
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-video: ca03eab980865b0dc199c3235f94dcb0417cb5a6
  react-native-webview: ec195db71ebf55705d7b46a7da5f1b1746bb7efd
  React-perflogger: 0458a87ea9a7342079e7a31b0d32b3734fb8415f
  React-RCTActionSheet: 22538001ea2926dea001111dd2846c13a0730bc9
  React-RCTAnimation: 732ce66878d4aa151d56a0d142b1105aa12fd313
  React-RCTBlob: 9cb9e3e9a41d27be34aaf89b0e0f52c7ca415d57
  React-RCTImage: 6bd16627eb9c4bb79903c4cdec7c551266ee1a5b
  React-RCTLinking: e9edfc8919c8fa9a3f3c7b34362811f58a2ebba4
  React-RCTNetwork: 880eccd21bbe2660a0b63da5ccba75c46eceeaa6
  React-RCTSettings: 8c85d8188c97d6c6bd470af6631a6c4555b79bb3
  React-RCTText: bbd275ee287730c5acbab1aadc0db39c25c5c64e
  React-RCTVibration: 9819a3bf6230e4b2a99877c21268b0b2416157a1
  React-runtimeexecutor: b1f1995089b90696dbc2a7ffe0059a80db5c8eb1
  ReactCommon: 1bf47544e1c9fd0927b374aead9527441b550c2d
  ReactNativeGetLocation: ee01d28cc3a1980d96a1f81ad1cca82fa806b166
  rn-fetch-blob: 25612b6d6f6e980c6f17ed98ba2f58f5696a51ca
  RNBluetoothStateManager: 929f132d0dacf65cb4767a11e2803e47fdda9e73
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNCClipboard: b0541df1bb88935d40bdc93b41b7d8e3334c998a
  RNCMaskedView: 4c5ee1c8667d56077246cc6d1977f77393923560
  RNCPicker: a04f825ecc28764441425987e78946032850102d
  RNCPushNotificationIOS: 6c4ca3388c7434e4a662b92e4dfeeee858e6f440
  RNDateTimePicker: 21fbf94479a551f0e50d37e662febe03e16b7012
  RNDeviceInfo: d3e91ffb33ee97a7982108476edb68cb3672efa6
  RNFBApp: 20bfba7e2a61a959518c1d57e5d48817c62ed3f6
  RNFBMessaging: 48579eec1f6ffaed4038b67426d7076963ab9401
  RNGestureHandler: 6572a5f44759900730562b418da289c373de8d06
  RNInAppBrowser: 6d3eb68d471b9834335c664704719b8be1bfdb20
  RNNotifee: 271cfeb505183d2cd1b858c14c3968b6ca30a642
  RNPermissions: 8709597abda86902b17f5b547e76be4bde8a306f
  RNReanimated: ff94350aa68b1465c4d0ea1e5d2adf812e81fcfb
  RNScreens: 6a2106686e0de9866ab14ca250d7e47a49416b51
  RNShare: 06d69a0de7cc111302172cd8bd6d228462a6bbc1
  RNSVG: d7cb8bd34550cf4c4fc7edd7ac627905e2b71f3f
  RNVectorIcons: 1f688883f7dfb5b346a03689800a88c5e3376029
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  Yoga: c4d61225a466f250c35c1ee78d2d0b3d41fe661c

PODFILE CHECKSUM: 0b023fbf41115ff7e26a11fe76517e0096ad40c9

COCOAPODS: 1.16.2
