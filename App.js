import React, { Component } from "react";
import SplashScreen from "react-native-splash-screen";
import { Provider } from "react-redux";
import codePush from "react-native-code-push";
import { PersistGate } from "redux-persist/integration/react";
import { ActivityIndicator, StatusBar, LogBox, Alert } from "react-native";
// import { AppearanceProvider } from "react-native-appearance";
import Toast from "react-native-simple-toast";
import { ThemeProvider } from "@react-navigation/native";
import { persistor, store } from "./app/redux/store/configureStore";
import CTopNotify from "./app/components/CTopNotify";
import NavStart from "./app/navigation";
import { initTranslate } from "./app/lang/Translate";
import BaseColor from "./app/config/colors";
import { InAppNotificationProvider } from "./app/lib/react-native-in-app-notification/src";
import CarPlayComponent from "./app/components/MyCarPlay";

LogBox.ignoreLogs(["Warning: ..."]); // Ignore log notification by message
LogBox.ignoreAllLogs();

const codePushOptions = {
  installMode: codePush.InstallMode.IMMEDIATE,
  checkFrequency: codePush.CheckFrequency.MANUAL,

  updateDialog: {
    appendReleaseDescription: false,
    descriptionPrefix: "\n\nWhat's New:",
    mandatoryContinueButtonLabel: "Install",
  },
};

class index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      processing: false,
      loading: true,
    };
  }

  componentDidMount() {
    SplashScreen.hide();
    codePush.notifyAppReady();
  }

  onBeforeLift = () => {
    initTranslate(store);
    this.setState({
      loading: false,
    });
  };

  /* Codepush Events */
  codePushStatusDidChange(status) {
    console.log("Codepush status change ==> ", status);
    this.codepushStatus = status;
    switch (status) {
      case codePush.SyncStatus.CHECKING_FOR_UPDATE:
        console.log("Codepush: Checking for updates.");
        break;
      case codePush.SyncStatus.DOWNLOADING_PACKAGE:
        this.setState({
          processing: false,
        });
        Toast.show("New app update is available and being downloaded.");
        console.log("Codepush: Downloading package.");
        break;
      case codePush.SyncStatus.INSTALLING_UPDATE:
        Toast.show("New app update is available and being installed.");
        console.log("Codepush: Installing update.");
        break;
      case codePush.SyncStatus.UP_TO_DATE:
        console.log("Codepush: Up-to-date.");
        break;
      case codePush.SyncStatus.UPDATE_INSTALLED:
        console.log("Codepush: Update installed.");
        break;
    }
  }

  render() {
    const { processing, loading } = this.state;
    return (
        <ThemeProvider>
          <StatusBar
            backgroundColor={BaseColor.whiteColor}
            barStyle='dark-content'
          />
          <Provider store={store}>
            {/* <CarPlayComponent /> */}
            <PersistGate
              loading={<ActivityIndicator />}
              persistor={persistor}
              onBeforeLift={this.onBeforeLift}
            >
              <InAppNotificationProvider
                iconApp={require("./app/assets/images/app_logo.png")}
                closeInterval={4000}
                backgroundColour='transparent'
              >
                {loading ? <ActivityIndicator /> : <NavStart />}
              </InAppNotificationProvider>
              {processing && <CTopNotify title='Installing Updates...' />}
            </PersistGate>
          </Provider>
        </ThemeProvider>
    );
  }
}

let indexExport = index;
if (!__DEV__) {
  indexExport = codePush(codePushOptions)(index);
}

export default indexExport;
