{"name": "maxicos<PERSON>", "version": "0.0.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "maxicos<PERSON>", "version": "0.0.1", "hasInstallScript": true, "dependencies": {"@bugsnag/react-native": "^7.10.5", "@notifee/react-native": "7.9.0", "@react-native-async-storage/async-storage": "^1.14.1", "@react-native-community/blur": "^4.3.2", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^3.4.1", "@react-native-community/masked-view": "^0.1.10", "@react-native-community/netinfo": "^6.0.0", "@react-native-community/push-notification-ios": "^1.8.0", "@react-native-firebase/app": "^18.1.0", "@react-native-firebase/messaging": "^18.1.0", "@react-native-picker/picker": "2.4.8", "@react-navigation/bottom-tabs": "^5.11.8", "@react-navigation/drawer": "^5.12.4", "@react-navigation/native": "^5.9.3", "@react-navigation/stack": "^5.14.3", "@twotalltotems/react-native-otp-input": "^1.3.11", "convert-string": "^0.1.0", "i18n-js": "^3.8.0", "lodash": "^4.17.21", "metro-config": "^0.76.7", "moment": "^2.29.4", "react": "16.13.1", "react-native": "0.68.5", "react-native-action-button": "^2.8.5", "react-native-animatable": "^1.3.3", "react-native-app-intro-slider": "^4.0.4", "react-native-background-timer": "^2.4.1", "react-native-ble-manager": "10.1.5", "react-native-bluetooth-state-manager": "1.3.5", "react-native-camera": "3.40.0", "react-native-card-stack-swiper": "^1.2.5", "react-native-carplay": "^2.3.0", "react-native-code-push": "^7.0.1", "react-native-config": "^1.4.2", "react-native-country-picker-modal": "^2.0.0", "react-native-datepicker": "^1.7.2", "react-native-deck-swiper": "^2.0.5", "react-native-device-info": "^8.1.3", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "^1.10.3", "react-native-get-location": "^2.1.0", "react-native-image-picker": "5.4.0", "react-native-image-slider-box": "^1.0.12", "react-native-in-app-notification": "^3.1.0", "react-native-inappbrowser-reborn": "^3.5.1", "react-native-iphone-x-helper": "^1.3.1", "react-native-linear-gradient": "^2.5.6", "react-native-permissions": "3.6.1", "react-native-progress": "^4.1.2", "react-native-push-notification": "^7.3.1", "react-native-qrcode-scanner": "^1.5.3", "react-native-reanimated": "^2.17.0", "react-native-render-html": "^5.1.0", "react-native-restart": "^0.0.22", "react-native-safe-area-context": "^3.1.9", "react-native-screens": "^2.18.0", "react-native-share": "^7.0.0", "react-native-simple-toast": "^1.1.3", "react-native-splash-screen": "^3.2.0", "react-native-svg": "^12.3.0", "react-native-switch": "^2.0.0", "react-native-vector-icons": "^8.1.0", "react-native-video": "^5.2.1", "react-native-webview": "^11.4.0", "react-reconciler": "^0.33.0", "react-redux": "^7.2.2", "redux": "^4.0.5", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "rn-fetch-blob": "^0.12.0", "sails.io.js": "^1.2.1", "socket.io-client": "^2.0.3", "toggle-switch-react-native": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.13.8", "@babel/runtime": "^7.13.8", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.6.3", "eslint": "^7.21.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.5", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.65.1", "patch-package": "^8.0.1", "postinstall-postinstall": "^2.1.0", "react-native-svg-transformer": "^0.14.3", "react-test-renderer": "16.13.1"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.4", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.28.3", "@babel/helpers": "^7.28.4", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.4", "@babel/types": "^7.28.4", "@jridgewell/remapping": "^2.3.5", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.3", "@babel/types": "^7.28.2", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.28.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.24.7", "license": "MIT", "dependencies": {"@babel/types": "^7.24.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.28.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.4"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/types": "^7.28.4"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.28.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.20.7", "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread": {"version": "7.20.7", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.7"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-chaining": {"version": "7.21.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-flow": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-static-block": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.28.3", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-globals": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.28.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-explicit-resource-management": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-flow-strip-types": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-json-strings": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-assign": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/traverse": "^7.28.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-display-name": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-pure-annotations": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regexp-modifiers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.27.1", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.27.1", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.27.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.27.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.28.3", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.28.0", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.28.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.28.3", "@babel/plugin-transform-classes": "^7.28.3", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-explicit-resource-management": "^7.28.0", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.28.0", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.28.3", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "core-js-compat": "^3.43.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-flow": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/preset-react": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/register": {"version": "7.28.3", "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "find-cache-dir": "^2.0.0", "make-dir": "^2.1.0", "pirates": "^4.0.6", "source-map-support": "^0.5.16"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/register/node_modules/clone-deep": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@babel/register/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@babel/register/node_modules/shallow-clone": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/@babel/runtime": {"version": "7.28.4", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/types": "^7.28.4", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "dev": true, "license": "MIT"}, "node_modules/@bugsnag/core": {"version": "7.25.0", "license": "MIT", "dependencies": {"@bugsnag/cuid": "^3.0.0", "@bugsnag/safe-json-stringify": "^6.0.0", "error-stack-parser": "^2.0.3", "iserror": "0.0.2", "stack-generator": "^2.0.3"}}, "node_modules/@bugsnag/cuid": {"version": "3.2.1", "license": "MIT"}, "node_modules/@bugsnag/delivery-react-native": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-console-breadcrumbs": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-network-breadcrumbs": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}, "peerDependenciesMeta": {"@bugsnag/core": {"optional": true}}}, "node_modules/@bugsnag/plugin-react-native-client-sync": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react-native-event-sync": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react-native-global-error-handler": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react-native-hermes": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react-native-session": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/plugin-react-native-unhandled-rejection": {"version": "7.25.0", "license": "MIT", "peerDependencies": {"@bugsnag/core": "^7.0.0"}}, "node_modules/@bugsnag/react-native": {"version": "7.25.1", "license": "MIT", "dependencies": {"@bugsnag/core": "^7.25.0", "@bugsnag/delivery-react-native": "^7.25.0", "@bugsnag/plugin-console-breadcrumbs": "^7.25.0", "@bugsnag/plugin-network-breadcrumbs": "^7.25.0", "@bugsnag/plugin-react": "^7.25.0", "@bugsnag/plugin-react-native-client-sync": "^7.25.0", "@bugsnag/plugin-react-native-event-sync": "^7.25.0", "@bugsnag/plugin-react-native-global-error-handler": "^7.25.0", "@bugsnag/plugin-react-native-hermes": "^7.25.0", "@bugsnag/plugin-react-native-session": "^7.25.0", "@bugsnag/plugin-react-native-unhandled-rejection": "^7.25.0", "iserror": "^0.0.2"}}, "node_modules/@bugsnag/safe-json-stringify": {"version": "6.1.0", "license": "MIT"}, "node_modules/@callstack/react-theme-provider": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/hoist-non-react-statics": "^3.3.1", "deepmerge": "^3.2.0", "hoist-non-react-statics": "^3.3.0"}, "peerDependencies": {"react": "^16.3.0"}}, "node_modules/@cnakazawa/watch": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"exec-sh": "^0.3.2", "minimist": "^1.2.0"}, "bin": {"watch": "cli.js"}, "engines": {"node": ">=0.1.95"}}, "node_modules/@egjs/hammerjs": {"version": "2.0.17", "license": "MIT", "dependencies": {"@types/hammerjs": "^2.0.36"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@eslint/eslintrc": {"version": "0.4.3", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "globals": "^13.9.0", "ignore": "^4.0.6", "import-fresh": "^3.2.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/@hapi/hoek": {"version": "9.3.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.5.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.0", "debug": "^4.1.1", "minimatch": "^3.0.4"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@inquirer/external-editor": {"version": "1.0.2", "license": "MIT", "dependencies": {"chardet": "^2.1.0", "iconv-lite": "^0.7.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"@types/node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "node_modules/@inquirer/external-editor/node_modules/iconv-lite": {"version": "0.7.0", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^26.6.2", "jest-util": "^26.6.2", "slash": "^3.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/core": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^26.6.2", "@jest/reporters": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "jest-changed-files": "^26.6.2", "jest-config": "^26.6.3", "jest-haste-map": "^26.6.2", "jest-message-util": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-resolve-dependencies": "^26.6.3", "jest-runner": "^26.6.3", "jest-runtime": "^26.6.3", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "jest-watcher": "^26.6.2", "micromatch": "^4.0.2", "p-each-series": "^2.1.0", "rimraf": "^3.0.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/create-cache-key-function": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/create-cache-key-function/node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@jest/create-cache-key-function/node_modules/@types/yargs": {"version": "16.0.9", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@jest/environment": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/fake-timers": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "@sinonjs/fake-timers": "^6.0.1", "@types/node": "*", "jest-message-util": "^26.6.2", "jest-mock": "^26.6.2", "jest-util": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/globals": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^26.6.2", "@jest/types": "^26.6.2", "expect": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/reporters": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.2", "graceful-fs": "^4.2.4", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^4.0.3", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "jest-haste-map": "^26.6.2", "jest-resolve": "^26.6.2", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "slash": "^3.0.0", "source-map": "^0.6.0", "string-length": "^4.0.1", "terminal-link": "^2.0.0", "v8-to-istanbul": "^7.0.0"}, "engines": {"node": ">= 10.14.2"}, "optionalDependencies": {"node-notifier": "^8.0.0"}}, "node_modules/@jest/reporters/node_modules/istanbul-lib-instrument": {"version": "4.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.7.5", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.4", "source-map": "^0.6.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/test-result": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^26.6.2", "@jest/types": "^26.6.2", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/test-sequencer": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^26.6.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.2", "jest-runner": "^26.6.3", "jest-runtime": "^26.6.3"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/transform": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.1.0", "@jest/types": "^26.6.2", "babel-plugin-istanbul": "^6.0.0", "chalk": "^4.0.0", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-util": "^26.6.2", "micromatch": "^4.0.2", "pirates": "^4.0.1", "slash": "^3.0.0", "source-map": "^0.6.1", "write-file-atomic": "^3.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jest/transform/node_modules/convert-source-map": {"version": "1.9.0", "dev": true, "license": "MIT"}, "node_modules/@jest/types": {"version": "26.6.2", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "chalk": "^4.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.13", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/remapping": {"version": "2.3.5", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.11", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.5", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.31", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@noble/hashes": {"version": "1.8.0", "license": "MIT", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@notifee/react-native": {"version": "7.9.0", "license": "Apache-2.0", "peerDependencies": {"react-native": "*"}}, "node_modules/@paralleldrive/cuid2": {"version": "2.2.2", "license": "MIT", "dependencies": {"@noble/hashes": "^1.1.5"}}, "node_modules/@react-native-async-storage/async-storage": {"version": "1.24.0", "license": "MIT", "dependencies": {"merge-options": "^3.0.4"}, "peerDependencies": {"react-native": "^0.0.0-0 || >=0.60 <1.0"}}, "node_modules/@react-native-community/art": {"version": "1.1.2", "license": "MIT", "dependencies": {"art": "^0.10.3", "invariant": "^2.2.4", "prop-types": "^15.7.2"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/@react-native-community/async-storage": {"version": "1.12.1", "license": "MIT", "dependencies": {"deep-assign": "^3.0.0"}, "peerDependencies": {"react": "^16.8", "react-native": ">=0.59"}}, "node_modules/@react-native-community/blur": {"version": "4.4.1", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/@react-native-community/cli": {"version": "7.0.4", "license": "MIT", "dependencies": {"@react-native-community/cli-debugger-ui": "^7.0.3", "@react-native-community/cli-hermes": "^6.3.1", "@react-native-community/cli-plugin-metro": "^7.0.4", "@react-native-community/cli-server-api": "^7.0.4", "@react-native-community/cli-tools": "^6.2.1", "@react-native-community/cli-types": "^6.0.0", "appdirsjs": "^1.2.4", "chalk": "^4.1.2", "command-exists": "^1.2.8", "commander": "^2.19.0", "cosmiconfig": "^5.1.0", "deepmerge": "^3.2.0", "envinfo": "^7.7.2", "execa": "^1.0.0", "find-up": "^4.1.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "graceful-fs": "^4.1.3", "joi": "^17.2.1", "leven": "^3.1.0", "lodash": "^4.17.15", "minimist": "^1.2.0", "node-stream-zip": "^1.9.1", "ora": "^3.4.0", "pretty-format": "^26.6.2", "prompts": "^2.4.0", "semver": "^6.3.0", "serve-static": "^1.13.1", "strip-ansi": "^5.2.0", "sudo-prompt": "^9.0.0", "wcwidth": "^1.0.1"}, "bin": {"react-native": "build/bin.js"}, "engines": {"node": ">=12"}, "peerDependencies": {"react-native": "*"}}, "node_modules/@react-native-community/cli-debugger-ui": {"version": "7.0.3", "license": "MIT", "dependencies": {"serve-static": "^1.13.1"}}, "node_modules/@react-native-community/cli-hermes": {"version": "6.3.1", "license": "MIT", "dependencies": {"@react-native-community/cli-platform-android": "^6.3.1", "@react-native-community/cli-tools": "^6.2.1", "chalk": "^4.1.2", "hermes-profile-transformer": "^0.0.6", "ip": "^1.1.5"}}, "node_modules/@react-native-community/cli-hermes/node_modules/@react-native-community/cli-platform-android": {"version": "6.3.1", "license": "MIT", "dependencies": {"@react-native-community/cli-tools": "^6.2.1", "chalk": "^4.1.2", "execa": "^1.0.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "jetifier": "^1.6.2", "lodash": "^4.17.15", "logkitty": "^0.7.1", "slash": "^3.0.0", "xmldoc": "^1.1.2"}}, "node_modules/@react-native-community/cli-platform-android": {"version": "7.0.1", "license": "MIT", "dependencies": {"@react-native-community/cli-tools": "^7.0.1", "chalk": "^4.1.2", "execa": "^1.0.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "jetifier": "^1.6.2", "lodash": "^4.17.15", "logkitty": "^0.7.1", "slash": "^3.0.0", "xmldoc": "^1.1.2"}}, "node_modules/@react-native-community/cli-platform-android/node_modules/@react-native-community/cli-tools": {"version": "7.0.1", "license": "MIT", "dependencies": {"appdirsjs": "^1.2.4", "chalk": "^4.1.2", "lodash": "^4.17.15", "mime": "^2.4.1", "node-fetch": "^2.6.0", "open": "^6.2.0", "ora": "^5.4.1", "semver": "^6.3.0", "shell-quote": "^1.7.3"}}, "node_modules/@react-native-community/cli-platform-ios": {"version": "7.0.1", "license": "MIT", "dependencies": {"@react-native-community/cli-tools": "^7.0.1", "chalk": "^4.1.2", "execa": "^1.0.0", "glob": "^7.1.3", "js-yaml": "^3.13.1", "lodash": "^4.17.15", "ora": "^5.4.1", "plist": "^3.0.2", "xcode": "^3.0.0"}}, "node_modules/@react-native-community/cli-platform-ios/node_modules/@react-native-community/cli-tools": {"version": "7.0.1", "license": "MIT", "dependencies": {"appdirsjs": "^1.2.4", "chalk": "^4.1.2", "lodash": "^4.17.15", "mime": "^2.4.1", "node-fetch": "^2.6.0", "open": "^6.2.0", "ora": "^5.4.1", "semver": "^6.3.0", "shell-quote": "^1.7.3"}}, "node_modules/@react-native-community/cli-plugin-metro": {"version": "7.0.4", "license": "MIT", "dependencies": {"@react-native-community/cli-server-api": "^7.0.4", "@react-native-community/cli-tools": "^6.2.1", "chalk": "^4.1.2", "metro": "^0.67.0", "metro-config": "^0.67.0", "metro-core": "^0.67.0", "metro-react-native-babel-transformer": "^0.67.0", "metro-resolver": "^0.67.0", "metro-runtime": "^0.67.0", "readline": "^1.3.0"}}, "node_modules/@react-native-community/cli-plugin-metro/node_modules/metro-cache": {"version": "0.67.0", "license": "MIT", "dependencies": {"metro-core": "0.67.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.4"}}, "node_modules/@react-native-community/cli-plugin-metro/node_modules/metro-config": {"version": "0.67.0", "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "jest-validate": "^26.5.2", "metro": "0.67.0", "metro-cache": "0.67.0", "metro-core": "0.67.0", "metro-runtime": "0.67.0"}}, "node_modules/@react-native-community/cli-plugin-metro/node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/@react-native-community/cli-server-api": {"version": "7.0.4", "license": "MIT", "dependencies": {"@react-native-community/cli-debugger-ui": "^7.0.3", "@react-native-community/cli-tools": "^6.2.1", "compression": "^1.7.1", "connect": "^3.6.5", "errorhandler": "^1.5.0", "nocache": "^2.1.0", "pretty-format": "^26.6.2", "serve-static": "^1.13.1", "ws": "^7.5.1"}}, "node_modules/@react-native-community/cli-tools": {"version": "6.2.1", "license": "MIT", "dependencies": {"appdirsjs": "^1.2.4", "chalk": "^4.1.2", "lodash": "^4.17.15", "mime": "^2.4.1", "node-fetch": "^2.6.0", "open": "^6.2.0", "semver": "^6.3.0", "shell-quote": "^1.7.3"}}, "node_modules/@react-native-community/cli-types": {"version": "6.0.0", "license": "MIT", "dependencies": {"ora": "^3.4.0"}}, "node_modules/@react-native-community/cli-types/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli-types/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/cli-cursor": {"version": "2.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/log-symbols": {"version": "2.2.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/mimic-fn": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/onetime": {"version": "2.0.1", "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/ora": {"version": "3.4.0", "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli-types/node_modules/restore-cursor": {"version": "2.0.0", "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli-types/node_modules/strip-ansi": {"version": "5.2.0", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli-types/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/cli-cursor": {"version": "2.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/log-symbols": {"version": "2.2.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/log-symbols/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/mimic-fn": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/onetime": {"version": "2.0.1", "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/ora": {"version": "3.4.0", "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli/node_modules/ora/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/restore-cursor": {"version": "2.0.0", "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/cli/node_modules/strip-ansi": {"version": "5.2.0", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/@react-native-community/cli/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@react-native-community/clipboard": {"version": "1.5.1", "license": "MIT", "peerDependencies": {"react": ">=16.0", "react-native": ">=0.57.0"}}, "node_modules/@react-native-community/datetimepicker": {"version": "3.5.2", "license": "MIT", "dependencies": {"invariant": "^2.2.4"}}, "node_modules/@react-native-community/eslint-config": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"@react-native-community/eslint-plugin": "^1.1.0", "@typescript-eslint/eslint-plugin": "^3.1.0", "@typescript-eslint/parser": "^3.1.0", "babel-eslint": "^10.1.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-eslint-comments": "^3.1.2", "eslint-plugin-flowtype": "2.50.3", "eslint-plugin-jest": "22.4.1", "eslint-plugin-prettier": "3.1.2", "eslint-plugin-react": "^7.20.0", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-react-native": "^3.8.1", "prettier": "^2.0.2"}, "peerDependencies": {"eslint": ">=6"}}, "node_modules/@react-native-community/eslint-config/node_modules/eslint-config-prettier": {"version": "6.15.0", "dev": true, "license": "MIT", "dependencies": {"get-stdin": "^6.0.0"}, "bin": {"eslint-config-prettier-check": "bin/cli.js"}, "peerDependencies": {"eslint": ">=3.14.1"}}, "node_modules/@react-native-community/eslint-plugin": {"version": "1.3.0", "dev": true, "license": "MIT"}, "node_modules/@react-native-community/masked-view": {"version": "0.1.11", "license": "MIT", "peerDependencies": {"react": ">=16.0", "react-native": ">=0.57"}}, "node_modules/@react-native-community/netinfo": {"version": "6.2.1", "license": "MIT", "peerDependencies": {"react-native": ">=0.59"}}, "node_modules/@react-native-community/push-notification-ios": {"version": "1.11.0", "license": "MIT", "dependencies": {"invariant": "^2.2.4"}, "peerDependencies": {"react": ">=16.6.3", "react-native": ">=0.58.4"}}, "node_modules/@react-native-firebase/app": {"version": "18.9.0", "license": "Apache-2.0", "dependencies": {"opencollective-postinstall": "^2.0.3", "superstruct": "^0.6.2"}, "peerDependencies": {"expo": ">=47.0.0", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}}}, "node_modules/@react-native-firebase/messaging": {"version": "18.9.0", "license": "Apache-2.0", "peerDependencies": {"@react-native-firebase/app": "18.9.0", "expo": ">=47.0.0"}, "peerDependenciesMeta": {"expo": {"optional": true}}}, "node_modules/@react-native-picker/picker": {"version": "2.4.8", "license": "MIT", "peerDependencies": {"react": ">=16", "react-native": ">=0.57"}}, "node_modules/@react-native/assets": {"version": "1.0.0", "license": "MIT"}, "node_modules/@react-native/normalize-color": {"version": "2.1.0", "license": "MIT"}, "node_modules/@react-native/polyfills": {"version": "2.0.0", "license": "MIT"}, "node_modules/@react-navigation/bottom-tabs": {"version": "5.11.15", "license": "MIT", "dependencies": {"color": "^3.1.3", "react-native-iphone-x-helper": "^1.3.0"}, "peerDependencies": {"@react-navigation/native": "^5.0.5", "react": "*", "react-native": "*", "react-native-safe-area-context": ">= 0.6.0", "react-native-screens": ">= 2.0.0-alpha.0 || >= 2.0.0-beta.0 || >= 2.0.0"}}, "node_modules/@react-navigation/core": {"version": "5.16.1", "license": "MIT", "dependencies": {"@react-navigation/routers": "^5.7.4", "escape-string-regexp": "^4.0.0", "nanoid": "^3.1.15", "query-string": "^6.13.6", "react-is": "^16.13.0"}, "peerDependencies": {"react": "*"}}, "node_modules/@react-navigation/core/node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@react-navigation/drawer": {"version": "5.12.9", "license": "MIT", "dependencies": {"color": "^3.1.3", "react-native-iphone-x-helper": "^1.3.0"}, "peerDependencies": {"@react-navigation/native": "^5.0.5", "react": "*", "react-native": "*", "react-native-gesture-handler": ">= 1.0.0", "react-native-reanimated": ">= 1.0.0", "react-native-safe-area-context": ">= 0.6.0", "react-native-screens": ">= 2.0.0-alpha.0 || >= 2.0.0-beta.0 || >= 2.0.0"}}, "node_modules/@react-navigation/native": {"version": "5.9.8", "license": "MIT", "dependencies": {"@react-navigation/core": "^5.16.1", "escape-string-regexp": "^4.0.0", "nanoid": "^3.1.15"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/@react-navigation/native/node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@react-navigation/routers": {"version": "5.7.4", "license": "MIT", "dependencies": {"nanoid": "^3.1.15"}}, "node_modules/@react-navigation/stack": {"version": "5.14.9", "license": "MIT", "dependencies": {"color": "^3.1.3", "react-native-iphone-x-helper": "^1.3.0"}, "peerDependencies": {"@react-native-community/masked-view": ">= 0.1.0", "@react-navigation/native": "^5.0.5", "react": "*", "react-native": "*", "react-native-gesture-handler": ">= 1.0.0", "react-native-safe-area-context": ">= 0.6.0", "react-native-screens": ">= 2.0.0-alpha.0 || >= 2.0.0-beta.0 || >= 2.0.0"}}, "node_modules/@rtsao/scc": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/@sideway/address": {"version": "4.1.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "license": "MIT"}, "node_modules/@sinonjs/commons": {"version": "1.8.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "6.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^1.7.0"}}, "node_modules/@svgr/babel-plugin-add-jsx-attribute": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-remove-jsx-attribute": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-remove-jsx-empty-expression": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-replace-jsx-attribute-value": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-svg-dynamic-title": {"version": "4.3.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-svg-em-dimensions": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-transform-react-native-svg": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-plugin-transform-svg-component": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@svgr/babel-preset": {"version": "4.3.3", "dev": true, "license": "MIT", "dependencies": {"@svgr/babel-plugin-add-jsx-attribute": "^4.2.0", "@svgr/babel-plugin-remove-jsx-attribute": "^4.2.0", "@svgr/babel-plugin-remove-jsx-empty-expression": "^4.2.0", "@svgr/babel-plugin-replace-jsx-attribute-value": "^4.2.0", "@svgr/babel-plugin-svg-dynamic-title": "^4.3.3", "@svgr/babel-plugin-svg-em-dimensions": "^4.2.0", "@svgr/babel-plugin-transform-react-native-svg": "^4.2.0", "@svgr/babel-plugin-transform-svg-component": "^4.2.0"}, "engines": {"node": ">=8"}}, "node_modules/@svgr/core": {"version": "4.3.3", "dev": true, "license": "MIT", "dependencies": {"@svgr/plugin-jsx": "^4.3.3", "camelcase": "^5.3.1", "cosmiconfig": "^5.2.1"}, "engines": {"node": ">=8"}}, "node_modules/@svgr/hast-util-to-babel-ast": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.4.4"}, "engines": {"node": ">=8"}}, "node_modules/@svgr/plugin-jsx": {"version": "4.3.3", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.4.5", "@svgr/babel-preset": "^4.3.3", "@svgr/hast-util-to-babel-ast": "^4.3.2", "svg-parser": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@svgr/plugin-svgo": {"version": "4.3.1", "dev": true, "license": "MIT", "dependencies": {"cosmiconfig": "^5.2.1", "merge-deep": "^3.0.2", "svgo": "^1.2.2"}, "engines": {"node": ">=8"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@tootallnate/quickjs-emscripten": {"version": "0.23.0", "license": "MIT"}, "node_modules/@twotalltotems/react-native-otp-input": {"version": "1.3.11", "license": "MIT", "dependencies": {"@react-native-community/clipboard": "^1.2.2"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}}, "node_modules/@types/eslint-visitor-keys": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/hammerjs": {"version": "2.0.46", "license": "MIT"}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.7", "license": "MIT", "dependencies": {"hoist-non-react-statics": "^3.3.0"}, "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "24.7.0", "license": "MIT", "dependencies": {"undici-types": "~7.14.0"}}, "node_modules/@types/normalize-package-data": {"version": "2.4.4", "dev": true, "license": "MIT"}, "node_modules/@types/prettier": {"version": "2.7.3", "dev": true, "license": "MIT"}, "node_modules/@types/q": {"version": "1.5.8", "dev": true, "license": "MIT"}, "node_modules/@types/react": {"version": "19.2.2", "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-redux": {"version": "7.1.34", "license": "MIT", "dependencies": {"@types/hoist-non-react-statics": "^3.3.0", "@types/react": "*", "hoist-non-react-statics": "^3.3.0", "redux": "^4.0.0"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/yargs": {"version": "15.0.19", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "3.10.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/experimental-utils": "3.10.1", "debug": "^4.1.1", "functional-red-black-tree": "^1.0.1", "regexpp": "^3.0.0", "semver": "^7.3.2", "tsutils": "^3.17.1"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^3.0.0", "eslint": "^5.0.0 || ^6.0.0 || ^7.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/experimental-utils": {"version": "3.10.1", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.3", "@typescript-eslint/types": "3.10.1", "@typescript-eslint/typescript-estree": "3.10.1", "eslint-scope": "^5.0.0", "eslint-utils": "^2.0.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}}, "node_modules/@typescript-eslint/parser": {"version": "3.10.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@types/eslint-visitor-keys": "^1.0.0", "@typescript-eslint/experimental-utils": "3.10.1", "@typescript-eslint/types": "3.10.1", "@typescript-eslint/typescript-estree": "3.10.1", "eslint-visitor-keys": "^1.1.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^5.0.0 || ^6.0.0 || ^7.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "3.10.1", "dev": true, "license": "MIT", "engines": {"node": "^8.10.0 || ^10.13.0 || >=11.10.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "3.10.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "3.10.1", "@typescript-eslint/visitor-keys": "3.10.1", "debug": "^4.1.1", "glob": "^7.1.6", "is-glob": "^4.0.1", "lodash": "^4.17.15", "semver": "^7.3.2", "tsutils": "^3.17.1"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "3.10.1", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": "^8.10.0 || ^10.13.0 || >=11.10.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@xmldom/xmldom": {"version": "0.8.11", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@yarnpkg/lockfile": {"version": "1.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/abab": {"version": "2.0.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/absolute-path": {"version": "0.0.0", "license": "MIT"}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "7.4.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "7.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/after": {"version": "0.8.2", "license": "MIT"}, "node_modules/agent-base": {"version": "7.1.4", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/anser": {"version": "1.4.10", "license": "MIT"}, "node_modules/ansi-colors": {"version": "4.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-fragments": {"version": "0.2.1", "license": "MIT", "dependencies": {"colorette": "^1.0.7", "slice-ansi": "^2.0.0", "strip-ansi": "^5.0.0"}}, "node_modules/ansi-fragments/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-fragments/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/ansi-fragments/node_modules/astral-regex": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-fragments/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-fragments/node_modules/slice-ansi": {"version": "2.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/ansi-fragments/node_modules/strip-ansi": {"version": "5.2.0", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ansi-styles/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/anymatch/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/appcenter-file-upload-client": {"version": "0.1.0", "license": "MIT", "dependencies": {"detect-node": "^2.0.4", "superagent": "5.1.0", "url-parse": "^1.4.7"}}, "node_modules/appdirsjs": {"version": "1.2.7", "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/aria-query": {"version": "5.3.2", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/arr-diff": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.24.0", "es-object-atoms": "^1.1.1", "get-intrinsic": "^1.3.0", "is-string": "^1.1.1", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-unique": {"version": "0.3.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array.prototype.findlast": {"version": "1.2.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.reduce": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-array-method-boxes-properly": "^1.0.0", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "is-string": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.slice": {"version": "0.0.7", "license": "MIT"}, "node_modules/art": {"version": "0.10.3", "license": "MIT", "engines": {"node": "*"}}, "node_modules/asap": {"version": "2.0.6", "license": "MIT"}, "node_modules/assign-symbols": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ast-types": {"version": "0.13.4", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/ast-types-flow": {"version": "0.0.8", "dev": true, "license": "MIT"}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/async-function": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/async-generator-function": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/async-limiter": {"version": "1.0.1", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axe-core": {"version": "4.10.3", "dev": true, "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/axobject-query": {"version": "4.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/babel-core": {"version": "7.0.0-bridge.0", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-eslint": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/types": "^7.7.0", "eslint-visitor-keys": "^1.0.0", "resolve": "^1.12.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"eslint": ">= 4.12.1"}}, "node_modules/babel-jest": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/babel__core": "^7.1.7", "babel-plugin-istanbul": "^6.0.0", "babel-preset-jest": "^26.6.2", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "slash": "^3.0.0"}, "engines": {"node": ">= 10.14.2"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.14", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.13.0", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5", "core-js-compat": "^3.43.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-syntax-trailing-function-commas": {"version": "7.0.0-beta.0", "license": "MIT"}, "node_modules/babel-plugin-transform-flow-enums": {"version": "0.0.2", "license": "MIT", "dependencies": {"@babel/plugin-syntax-flow": "^7.12.1"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.2.0", "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^8.0.0-0"}}, "node_modules/babel-preset-fbjs": {"version": "3.4.0", "license": "MIT", "dependencies": {"@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-syntax-class-properties": "^7.0.0", "@babel/plugin-syntax-flow": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-block-scoped-functions": "^7.0.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.0.0", "@babel/plugin-transform-for-of": "^7.0.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-member-expression-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-object-super": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-property-literals": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "babel-plugin-syntax-trailing-function-commas": "^7.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^26.6.2", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": ">= 10.14.2"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/backo2": {"version": "1.0.2", "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base-64": {"version": "0.1.0"}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-arraybuffer": {"version": "0.1.4", "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/baseline-browser-mapping": {"version": "2.8.13", "license": "Apache-2.0", "bin": {"baseline-browser-mapping": "dist/cli.js"}}, "node_modules/basic-ftp": {"version": "5.0.5", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/big-integer": {"version": "1.6.52", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/bl": {"version": "4.1.0", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/blob": {"version": "0.0.5", "license": "MIT"}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/bplist-creator": {"version": "0.1.1", "license": "MIT", "dependencies": {"stream-buffers": "2.2.x"}}, "node_modules/bplist-parser": {"version": "0.3.2", "license": "MIT", "dependencies": {"big-integer": "1.6.x"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-process-hrtime": {"version": "1.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/browserslist": {"version": "4.26.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"baseline-browser-mapping": "^2.8.9", "caniuse-lite": "^1.0.30001746", "electron-to-chromium": "^1.5.227", "node-releases": "^2.0.21", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cache-base": {"version": "1.0.1", "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caller-callsite": {"version": "2.0.0", "license": "MIT", "dependencies": {"callsites": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/caller-callsite/node_modules/callsites": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/caller-path": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-callsite": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001749", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/capture-exit": {"version": "2.0.0", "dev": true, "license": "ISC", "dependencies": {"rsvp": "^4.8.4"}, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/character-entities-html4": {"version": "1.1.4", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/character-entities-legacy": {"version": "1.1.4", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/chardet": {"version": "2.1.0", "license": "MIT"}, "node_modules/ci-info": {"version": "2.0.0", "license": "MIT"}, "node_modules/cjs-module-lexer": {"version": "0.6.0", "dev": true, "license": "MIT"}, "node_modules/class-utils": {"version": "0.3.6", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cli-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-spinners": {"version": "2.9.2", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-width": {"version": "3.0.0", "license": "ISC", "engines": {"node": ">= 10"}}, "node_modules/cliui": {"version": "6.0.0", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/clone": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-deep": {"version": "2.0.2", "license": "MIT", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/clone-deep/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/co": {"version": "4.6.0", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/coa": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"@types/q": "^1.5.1", "chalk": "^2.4.1", "q": "^1.1.2"}, "engines": {"node": ">= 4.0"}}, "node_modules/coa/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/coa/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/code-push": {"version": "4.2.3", "license": "MIT", "dependencies": {"appcenter-file-upload-client": "0.1.0", "proxy-agent": "^6.3.0", "recursive-fs": "^2.1.0", "slash": "^3.0.0", "superagent": "^8.0.0", "yazl": "^2.5.1"}}, "node_modules/code-push/node_modules/form-data": {"version": "4.0.4", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/code-push/node_modules/formidable": {"version": "2.1.5", "license": "MIT", "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "dezalgo": "^1.0.4", "once": "^1.4.0", "qs": "^6.11.0"}, "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/code-push/node_modules/semver": {"version": "7.7.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/code-push/node_modules/superagent": {"version": "8.1.2", "license": "MIT", "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.4", "debug": "^4.3.4", "fast-safe-stringify": "^2.1.1", "form-data": "^4.0.0", "formidable": "^2.1.2", "methods": "^1.1.2", "mime": "2.6.0", "qs": "^6.11.0", "semver": "^7.3.8"}, "engines": {"node": ">=6.4.0 <13 || >=14"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/collection-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}}, "node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-convert/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/colorette": {"version": "1.4.0", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/command-exists": {"version": "1.2.9", "license": "MIT"}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/commondir": {"version": "1.0.1", "license": "MIT"}, "node_modules/component-bind": {"version": "1.0.0"}, "node_modules/component-emitter": {"version": "1.3.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/component-inherit": {"version": "0.0.3"}, "node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compressible/node_modules/mime-db": {"version": "1.54.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.8.1", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.1.0", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/compression/node_modules/negotiator": {"version": "0.6.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/confusing-browser-globals": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/connect": {"version": "3.7.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/connect/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/connect/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/convert-string": {"version": "0.1.0"}, "node_modules/cookiejar": {"version": "2.1.4", "license": "MIT"}, "node_modules/copy-descriptor": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/core-js": {"version": "1.2.7", "license": "MIT"}, "node_modules/core-js-compat": {"version": "3.45.1", "license": "MIT", "dependencies": {"browserslist": "^4.25.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/cosmiconfig": {"version": "5.2.1", "license": "MIT", "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cosmiconfig/node_modules/import-fresh": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cosmiconfig/node_modules/parse-json": {"version": "4.0.0", "license": "MIT", "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/cosmiconfig/node_modules/resolve-from": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/cross-fetch": {"version": "3.2.0", "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-select": {"version": "2.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}}, "node_modules/css-select-base-adapter": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/css-select/node_modules/dom-serializer": {"version": "0.2.2", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}}, "node_modules/css-select/node_modules/domutils": {"version": "1.7.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/css-select/node_modules/domutils/node_modules/domelementtype": {"version": "1.3.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/css-tree": {"version": "1.1.3", "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/css-tree/node_modules/mdn-data": {"version": "2.0.14", "license": "CC0-1.0"}, "node_modules/css-what": {"version": "3.4.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/csso": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/cssom": {"version": "0.4.4", "dev": true, "license": "MIT"}, "node_modules/cssstyle": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"cssom": "~0.3.6"}, "engines": {"node": ">=8"}}, "node_modules/cssstyle/node_modules/cssom": {"version": "0.3.8", "dev": true, "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/damerau-levenshtein": {"version": "1.0.8", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/data-uri-to-buffer": {"version": "6.0.2", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/data-urls": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/data-view-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/dayjs": {"version": "1.11.18", "license": "MIT"}, "node_modules/debug": {"version": "4.4.3", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decimal.js": {"version": "10.6.0", "dev": true, "license": "MIT"}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-assign": {"version": "3.0.0", "license": "MIT", "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/defaults": {"version": "1.0.4", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-property": {"version": "0.2.5", "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-descriptor": {"version": "0.1.7", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/degenerator": {"version": "5.0.1", "license": "MIT", "dependencies": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}, "engines": {"node": ">= 14"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/denodeify": {"version": "1.2.1", "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/deprecated-prop-type": {"version": "1.0.0", "license": "ISC", "dependencies": {"warning": "4.0.1"}}, "node_modules/deprecated-prop-type/node_modules/warning": {"version": "4.0.1", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/deprecated-react-native-prop-types": {"version": "2.3.0", "license": "MIT", "dependencies": {"@react-native/normalize-color": "*", "invariant": "*", "prop-types": "*"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-newline": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/detect-node": {"version": "2.1.0", "license": "MIT"}, "node_modules/dezalgo": {"version": "1.0.4", "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/diff-sequences": {"version": "26.6.2", "dev": true, "license": "MIT", "engines": {"node": ">= 10.14.2"}}, "node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dom-serializer": {"version": "1.4.1", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/dom-serializer/node_modules/domhandler": {"version": "4.3.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domexception": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"webidl-conversions": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "5.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/domhandler": {"version": "5.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/domutils/node_modules/domhandler": {"version": "4.3.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.233", "license": "ISC"}, "node_modules/eme-encryption-scheme-polyfill": {"version": "2.2.3", "license": "Apache-2.0"}, "node_modules/emittery": {"version": "0.7.2", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/end-of-stream": {"version": "1.4.5", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/engine.io-client": {"version": "3.5.4", "license": "MIT", "dependencies": {"component-emitter": "~1.3.0", "component-inherit": "0.0.3", "debug": "~3.1.0", "engine.io-parser": "~2.2.0", "has-cors": "1.1.0", "indexof": "0.0.1", "parseqs": "0.0.6", "parseuri": "0.0.6", "ws": "~7.5.10", "xmlhttprequest-ssl": "~1.6.2", "yeast": "0.1.2"}}, "node_modules/engine.io-client/node_modules/debug": {"version": "3.1.0", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/engine.io-client/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/engine.io-parser": {"version": "2.2.1", "license": "MIT", "dependencies": {"after": "0.8.2", "arraybuffer.slice": "~0.0.7", "base64-arraybuffer": "0.1.4", "blob": "0.0.5", "has-binary2": "~1.0.2"}}, "node_modules/enquirer": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/entities": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/envinfo": {"version": "7.17.0", "license": "MIT", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/error-ex": {"version": "1.3.4", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser": {"version": "2.1.4", "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/errorhandler": {"version": "1.5.1", "license": "MIT", "dependencies": {"accepts": "~1.3.7", "escape-html": "~1.0.3"}, "engines": {"node": ">= 0.8"}}, "node_modules/es-abstract": {"version": "1.24.0", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-array-method-boxes-properly": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-iterator-helpers": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/escodegen": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/eslint": {"version": "7.32.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "7.12.11", "@eslint/eslintrc": "^0.4.3", "@humanwhocodes/config-array": "^0.5.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "escape-string-regexp": "^4.0.0", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.1.2", "globals": "^13.6.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.9", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-airbnb": {"version": "18.2.1", "dev": true, "license": "MIT", "dependencies": {"eslint-config-airbnb-base": "^14.2.1", "object.assign": "^4.1.2", "object.entries": "^1.1.2"}, "engines": {"node": ">= 6"}, "peerDependencies": {"eslint": "^5.16.0 || ^6.8.0 || ^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4 || ^3 || ^2.3.0 || ^1.7.0"}}, "node_modules/eslint-config-airbnb-base": {"version": "14.2.1", "dev": true, "license": "MIT", "dependencies": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.2"}, "engines": {"node": ">= 6"}, "peerDependencies": {"eslint": "^5.16.0 || ^6.8.0 || ^7.2.0", "eslint-plugin-import": "^2.22.1"}}, "node_modules/eslint-config-prettier": {"version": "8.10.2", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils": {"version": "2.12.1", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-eslint-comments": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5", "ignore": "^5.0.5"}, "engines": {"node": ">=6.5.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=4.19.1"}}, "node_modules/eslint-plugin-eslint-comments/node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/eslint-plugin-flowtype": {"version": "2.50.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"lodash": "^4.17.10"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": ">=2.0.0"}}, "node_modules/eslint-plugin-import": {"version": "2.32.0", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.9", "array.prototype.findlastindex": "^1.2.6", "array.prototype.flat": "^1.3.3", "array.prototype.flatmap": "^1.3.3", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.1", "hasown": "^2.0.2", "is-core-module": "^2.16.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.1", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.9", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-jest": {"version": "22.4.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"eslint": ">=5"}}, "node_modules/eslint-plugin-jsx-a11y": {"version": "6.10.2", "dev": true, "license": "MIT", "dependencies": {"aria-query": "^5.3.2", "array-includes": "^3.1.8", "array.prototype.flatmap": "^1.3.2", "ast-types-flow": "^0.0.8", "axe-core": "^4.10.0", "axobject-query": "^4.1.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "hasown": "^2.0.2", "jsx-ast-utils": "^3.3.5", "language-tags": "^1.0.9", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "safe-regex-test": "^1.0.3", "string.prototype.includes": "^2.0.1"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9"}}, "node_modules/eslint-plugin-prettier": {"version": "3.1.2", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "engines": {"node": ">=6.0.0"}, "peerDependencies": {"eslint": ">= 5.0.0", "prettier": ">= 1.13.0"}}, "node_modules/eslint-plugin-react": {"version": "7.37.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.3", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.2.1", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.values": "^1.2.1", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "node_modules/eslint-plugin-react-hooks": {"version": "4.6.2", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"}}, "node_modules/eslint-plugin-react-native": {"version": "3.11.0", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.7.4", "eslint-plugin-react-native-globals": "^0.1.1"}, "peerDependencies": {"eslint": "^3.17.0 || ^4 || ^5 || ^6 || ^7"}}, "node_modules/eslint-plugin-react-native-globals": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.5", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-utils": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/eslint-visitor-keys": {"version": "1.3.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/eslint/node_modules/@babel/code-frame": {"version": "7.12.11", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.10.4"}}, "node_modules/eslint/node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/eslint/node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/eslint-visitor-keys": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/eslint/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/espree": {"version": "7.3.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/exec-sh": {"version": "0.3.6", "dev": true, "license": "MIT"}, "node_modules/execa": {"version": "1.0.0", "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/execa/node_modules/cross-spawn": {"version": "6.0.6", "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/execa/node_modules/path-key": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/execa/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/execa/node_modules/shebang-command": {"version": "1.2.0", "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/execa/node_modules/shebang-regex": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/execa/node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/exit": {"version": "0.1.2", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expand-brackets": {"version": "2.1.4", "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/expand-brackets/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/expect": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "ansi-styles": "^4.0.0", "jest-get-type": "^26.3.0", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-regex-util": "^26.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/extend-shallow": {"version": "2.0.1", "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "license": "MIT"}, "node_modules/fast-uri": {"version": "3.1.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fb-watchman": {"version": "2.0.2", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/fbjs": {"version": "0.8.18", "license": "MIT", "dependencies": {"core-js": "^1.0.0", "isomorphic-fetch": "^2.1.1", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^0.7.30"}}, "node_modules/fbjs-css-vars": {"version": "1.0.2", "license": "MIT"}, "node_modules/fbjs/node_modules/ua-parser-js": {"version": "0.7.41", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/figures": {"version": "3.2.0", "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/filter-obj": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "1.1.2", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/finalhandler/node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/find-cache-dir": {"version": "2.1.0", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-cache-dir/node_modules/find-up": {"version": "3.0.0", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-cache-dir/node_modules/locate-path": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-cache-dir/node_modules/p-locate": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-cache-dir/node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/find-cache-dir/node_modules/pkg-dir": {"version": "3.0.0", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/find-yarn-workspace-root": {"version": "2.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"micromatch": "^4.0.2"}}, "node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "dev": true, "license": "ISC"}, "node_modules/flow-parser": {"version": "0.121.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/for-each": {"version": "0.3.5", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/for-in": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/for-own": {"version": "1.0.0", "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/form-data": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.35"}, "engines": {"node": ">= 6"}}, "node_modules/formidable": {"version": "1.2.6", "license": "MIT", "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/fragment-cache": {"version": "0.2.1", "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "8.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-extra/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/fs-extra/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functional-red-black-tree": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/fuse.js": {"version": "3.4.5", "license": "Apache-2.0", "engines": {"node": ">=6"}}, "node_modules/generator-function": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.1", "license": "MIT", "dependencies": {"async-function": "^1.0.0", "async-generator-function": "^1.0.0", "call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "generator-function": "^2.0.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stdin": {"version": "6.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/get-stream": {"version": "4.1.0", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-uri": {"version": "6.0.5", "license": "MIT", "dependencies": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/get-value": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.4", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/growly": {"version": "1.3.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/has-bigints": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-binary2": {"version": "1.0.3", "license": "MIT", "dependencies": {"isarray": "2.0.1"}}, "node_modules/has-binary2/node_modules/isarray": {"version": "2.0.1", "license": "MIT"}, "node_modules/has-cors": {"version": "1.1.0", "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hermes-engine": {"version": "0.11.0", "license": "MIT"}, "node_modules/hermes-estree": {"version": "0.12.0", "license": "MIT"}, "node_modules/hermes-parser": {"version": "0.5.0", "license": "MIT", "dependencies": {"hermes-estree": "0.5.0"}}, "node_modules/hermes-parser/node_modules/hermes-estree": {"version": "0.5.0", "license": "MIT"}, "node_modules/hermes-profile-transformer": {"version": "0.0.6", "license": "MIT", "dependencies": {"source-map": "^0.7.3"}, "engines": {"node": ">=8"}}, "node_modules/hermes-profile-transformer/node_modules/source-map": {"version": "0.7.6", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 12"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hosted-git-info": {"version": "2.8.9", "dev": true, "license": "ISC"}, "node_modules/html-encoding-sniffer": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.5"}, "engines": {"node": ">=10"}}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/htmlparser2": {"version": "5.0.1", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^3.3.0", "domutils": "^2.4.2", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/fb55/htmlparser2?sponsor=1"}}, "node_modules/htmlparser2/node_modules/domhandler": {"version": "3.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.0.1"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8.12.0"}}, "node_modules/i18n-js": {"version": "3.9.2", "license": "MIT"}, "node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/image-size": {"version": "1.2.1", "license": "MIT", "dependencies": {"queue": "6.0.2"}, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=16.x"}}, "node_modules/import-fresh": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/import-local": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indexof": {"version": "0.0.1"}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/inquirer": {"version": "8.2.7", "license": "MIT", "dependencies": {"@inquirer/external-editor": "^1.0.0", "ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/inquirer/node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/internal-slot": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/invariant": {"version": "2.2.4", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/ip": {"version": "1.1.9", "license": "MIT"}, "node_modules/ip-address": {"version": "10.0.1", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/is-accessor-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-async-function": {"version": "2.1.1", "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-buffer": {"version": "1.1.6", "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^2.0.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-descriptor": {"version": "1.0.1", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-data-view": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-descriptor": {"version": "1.0.3", "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-directory": {"version": "0.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extendable": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-generator-function": {"version": "1.1.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.4", "generator-function": "^2.0.0", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "3.0.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-obj": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-string": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-windows": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/iserror": {"version": "0.0.2", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/isomorphic-fetch": {"version": "2.2.1", "license": "MIT", "dependencies": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}}, "node_modules/isomorphic-fetch/node_modules/node-fetch": {"version": "1.7.3", "license": "MIT", "dependencies": {"encoding": "^0.1.11", "is-stream": "^1.0.1"}}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report/node_modules/make-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/istanbul-lib-report/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/iterator.prototype": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/jest": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^26.6.3", "import-local": "^3.0.2", "jest-cli": "^26.6.3"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-changed-files": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "execa": "^4.0.0", "throat": "^5.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-changed-files/node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/jest-changed-files/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-changed-files/node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-changed-files/node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-cli": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^26.6.3", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "import-local": "^3.0.2", "is-ci": "^2.0.0", "jest-config": "^26.6.3", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "prompts": "^2.0.1", "yargs": "^15.4.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-config": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.1.0", "@jest/test-sequencer": "^26.6.3", "@jest/types": "^26.6.2", "babel-jest": "^26.6.3", "chalk": "^4.0.0", "deepmerge": "^4.2.2", "glob": "^7.1.1", "graceful-fs": "^4.2.4", "jest-environment-jsdom": "^26.6.2", "jest-environment-node": "^26.6.2", "jest-get-type": "^26.3.0", "jest-jasmine2": "^26.6.3", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "micromatch": "^4.0.2", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}}, "node_modules/jest-config/node_modules/deepmerge": {"version": "4.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jest-diff": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^26.6.2", "jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-docblock": {"version": "26.0.0", "dev": true, "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-each": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "chalk": "^4.0.0", "jest-get-type": "^26.3.0", "jest-util": "^26.6.2", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-environment-jsdom": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2", "jest-util": "^26.6.2", "jsdom": "^16.4.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-environment-node": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2", "jest-util": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-get-type": {"version": "26.3.0", "license": "MIT", "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-haste-map": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.4", "jest-regex-util": "^26.0.0", "jest-serializer": "^26.6.2", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "micromatch": "^4.0.2", "sane": "^4.0.3", "walker": "^1.0.7"}, "engines": {"node": ">= 10.14.2"}, "optionalDependencies": {"fsevents": "^2.1.2"}}, "node_modules/jest-jasmine2": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.1.0", "@jest/environment": "^26.6.2", "@jest/source-map": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "expect": "^26.6.2", "is-generator-fn": "^2.0.0", "jest-each": "^26.6.2", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-runtime": "^26.6.3", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "pretty-format": "^26.6.2", "throat": "^5.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-leak-detector": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-matcher-utils": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^26.6.2", "jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-message-util": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "@jest/types": "^26.6.2", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "micromatch": "^4.0.2", "pretty-format": "^26.6.2", "slash": "^3.0.0", "stack-utils": "^2.0.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-mock": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "@types/node": "*"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "26.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-resolve": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "jest-pnp-resolver": "^1.2.2", "jest-util": "^26.6.2", "read-pkg-up": "^7.0.1", "resolve": "^1.18.1", "slash": "^3.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-resolve-dependencies": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-snapshot": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-runner": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^26.6.2", "@jest/environment": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.7.1", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "jest-config": "^26.6.3", "jest-docblock": "^26.0.0", "jest-haste-map": "^26.6.2", "jest-leak-detector": "^26.6.2", "jest-message-util": "^26.6.2", "jest-resolve": "^26.6.2", "jest-runtime": "^26.6.3", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "source-map-support": "^0.5.6", "throat": "^5.0.0"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-runtime": {"version": "26.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^26.6.2", "@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/globals": "^26.6.2", "@jest/source-map": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/yargs": "^15.0.0", "chalk": "^4.0.0", "cjs-module-lexer": "^0.6.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.4", "jest-config": "^26.6.3", "jest-haste-map": "^26.6.2", "jest-message-util": "^26.6.2", "jest-mock": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "slash": "^3.0.0", "strip-bom": "^4.0.0", "yargs": "^15.4.1"}, "bin": {"jest-runtime": "bin/jest-runtime.js"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-serializer": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "graceful-fs": "^4.2.4"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-snapshot": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0", "@jest/types": "^26.6.2", "@types/babel__traverse": "^7.0.4", "@types/prettier": "^2.0.0", "chalk": "^4.0.0", "expect": "^26.6.2", "graceful-fs": "^4.2.4", "jest-diff": "^26.6.2", "jest-get-type": "^26.3.0", "jest-haste-map": "^26.6.2", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-resolve": "^26.6.2", "natural-compare": "^1.4.0", "pretty-format": "^26.6.2", "semver": "^7.3.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-snapshot/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jest-util": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "is-ci": "^2.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-validate": {"version": "26.6.2", "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "camelcase": "^6.0.0", "chalk": "^4.0.0", "jest-get-type": "^26.3.0", "leven": "^3.1.0", "pretty-format": "^26.6.2"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watcher": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "jest-util": "^26.6.2", "string-length": "^4.0.1"}, "engines": {"node": ">= 10.14.2"}}, "node_modules/jest-worker": {"version": "26.6.2", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jetifier": {"version": "1.6.8", "license": "MIT", "bin": {"jetifier": "bin/jetify", "jetifier-standalone": "bin/jetifier-standalone", "jetify": "bin/jetify"}}, "node_modules/joi": {"version": "17.13.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsc-android": {"version": "250230.2.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/jsc-safe-url": {"version": "0.2.4", "license": "0BSD"}, "node_modules/jscodeshift": {"version": "0.13.1", "license": "MIT", "dependencies": {"@babel/core": "^7.13.16", "@babel/parser": "^7.13.16", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.12", "@babel/plugin-transform-modules-commonjs": "^7.13.8", "@babel/preset-flow": "^7.13.13", "@babel/preset-typescript": "^7.13.0", "@babel/register": "^7.13.16", "babel-core": "^7.0.0-bridge.0", "chalk": "^4.1.2", "flow-parser": "0.*", "graceful-fs": "^4.2.4", "micromatch": "^3.1.10", "neo-async": "^2.5.0", "node-dir": "^0.1.17", "recast": "^0.20.4", "temp": "^0.8.4", "write-file-atomic": "^2.3.0"}, "bin": {"jscodeshift": "bin/jscodeshift.js"}, "peerDependencies": {"@babel/preset-env": "^7.1.6"}}, "node_modules/jscodeshift/node_modules/braces": {"version": "2.3.2", "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/fill-range": {"version": "4.0.0", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/flow-parser": {"version": "0.287.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/jscodeshift/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/micromatch": {"version": "3.1.10", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/micromatch/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/to-regex-range": {"version": "2.1.1", "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jscodeshift/node_modules/write-file-atomic": {"version": "2.4.3", "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "node_modules/jsdom": {"version": "16.7.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.5", "acorn": "^8.2.4", "acorn-globals": "^6.0.0", "cssom": "^0.4.4", "cssstyle": "^2.3.0", "data-urls": "^2.0.0", "decimal.js": "^10.2.1", "domexception": "^2.0.1", "escodegen": "^2.0.0", "form-data": "^3.0.0", "html-encoding-sniffer": "^2.0.1", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.0", "parse5": "6.0.1", "saxes": "^5.0.1", "symbol-tree": "^3.2.4", "tough-cookie": "^4.0.0", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.5.0", "ws": "^7.4.6", "xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsdom/node_modules/acorn": {"version": "8.15.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/jsdom/node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/jsdom/node_modules/http-proxy-agent": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/jsdom/node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "isarray": "^2.0.5", "jsonify": "^0.0.1", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonify": {"version": "0.0.1", "dev": true, "license": "Public Domain", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jsx-ast-utils": {"version": "3.3.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "node_modules/keymirror": {"version": "0.1.1"}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "3.2.2", "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/klaw": {"version": "1.3.1", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.9"}}, "node_modules/klaw-sync": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11"}}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/language-subtag-registry": {"version": "0.3.23", "dev": true, "license": "CC0-1.0"}, "node_modules/language-tags": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"language-subtag-registry": "^0.3.20"}, "engines": {"node": ">=0.10"}}, "node_modules/lazy-cache": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash._reinterpolate": {"version": "3.0.0", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/lodash.frompairs": {"version": "4.0.1", "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.omit": {"version": "4.5.0", "license": "MIT"}, "node_modules/lodash.pick": {"version": "4.4.0", "license": "MIT"}, "node_modules/lodash.template": {"version": "4.5.0", "license": "MIT", "dependencies": {"lodash._reinterpolate": "^3.0.0", "lodash.templatesettings": "^4.0.0"}}, "node_modules/lodash.templatesettings": {"version": "4.2.0", "license": "MIT", "dependencies": {"lodash._reinterpolate": "^3.0.0"}}, "node_modules/lodash.throttle": {"version": "4.1.1", "license": "MIT"}, "node_modules/lodash.toarray": {"version": "4.4.0", "license": "MIT"}, "node_modules/lodash.truncate": {"version": "4.4.2", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/logkitty": {"version": "0.7.1", "license": "MIT", "dependencies": {"ansi-fragments": "^0.2.1", "dayjs": "^1.8.15", "yargs": "^15.1.0"}, "bin": {"logkitty": "bin/logkitty.js"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/make-dir": {"version": "2.1.0", "license": "MIT", "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/make-dir/node_modules/semver": {"version": "5.7.2", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/map-cache": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mdn-data": {"version": "2.0.4", "dev": true, "license": "CC0-1.0"}, "node_modules/merge-deep": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "clone-deep": "^0.2.4", "kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-deep/node_modules/clone-deep": {"version": "0.2.4", "dev": true, "license": "MIT", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "shallow-clone": "^0.1.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-deep/node_modules/for-own": {"version": "0.1.5", "dev": true, "license": "MIT", "dependencies": {"for-in": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-deep/node_modules/shallow-clone": {"version": "0.1.2", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "mixin-object": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-deep/node_modules/shallow-clone/node_modules/kind-of": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/merge-deep/node_modules/shallow-clone/node_modules/lazy-cache": {"version": "0.2.7", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/merge-options": {"version": "3.0.4", "license": "MIT", "dependencies": {"is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/metro": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/core": "^7.14.0", "@babel/generator": "^7.14.0", "@babel/parser": "^7.14.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.14.0", "@babel/types": "^7.0.0", "absolute-path": "^0.0.0", "accepts": "^1.3.7", "async": "^2.4.0", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^2.2.0", "denodeify": "^1.2.1", "error-stack-parser": "^2.0.6", "fs-extra": "^1.0.0", "graceful-fs": "^4.1.3", "hermes-parser": "0.5.0", "image-size": "^0.6.0", "invariant": "^2.2.4", "jest-haste-map": "^27.3.1", "jest-worker": "^26.0.0", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.67.0", "metro-cache": "0.67.0", "metro-cache-key": "0.67.0", "metro-config": "0.67.0", "metro-core": "0.67.0", "metro-hermes-compiler": "0.67.0", "metro-inspector-proxy": "0.67.0", "metro-minify-uglify": "0.67.0", "metro-react-native-babel-preset": "0.67.0", "metro-resolver": "0.67.0", "metro-runtime": "0.67.0", "metro-source-map": "0.67.0", "metro-symbolicate": "0.67.0", "metro-transform-plugins": "0.67.0", "metro-transform-worker": "0.67.0", "mime-types": "^2.1.27", "mkdirp": "^0.5.1", "node-fetch": "^2.2.0", "nullthrows": "^1.1.1", "rimraf": "^2.5.4", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "strip-ansi": "^6.0.0", "temp": "0.8.3", "throat": "^5.0.0", "ws": "^7.5.1", "yargs": "^15.3.1"}, "bin": {"metro": "src/cli.js"}}, "node_modules/metro-babel-transformer": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "hermes-parser": "0.5.0", "metro-source-map": "0.67.0", "nullthrows": "^1.1.1"}}, "node_modules/metro-cache": {"version": "0.76.9", "license": "MIT", "dependencies": {"metro-core": "0.76.9", "rimraf": "^3.0.2"}, "engines": {"node": ">=16"}}, "node_modules/metro-cache-key": {"version": "0.76.9", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/metro-cache/node_modules/metro-core": {"version": "0.76.9", "license": "MIT", "dependencies": {"lodash.throttle": "^4.1.1", "metro-resolver": "0.76.9"}, "engines": {"node": ">=16"}}, "node_modules/metro-cache/node_modules/metro-resolver": {"version": "0.76.9", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/metro-config": {"version": "0.76.9", "license": "MIT", "dependencies": {"connect": "^3.6.5", "cosmiconfig": "^5.0.5", "jest-validate": "^29.2.1", "metro": "0.76.9", "metro-cache": "0.76.9", "metro-core": "0.76.9", "metro-runtime": "0.76.9"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/@jest/types": {"version": "29.6.3", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/metro-config/node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/metro-config/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/metro-config/node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/metro-config/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/metro-config/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/metro-config/node_modules/hermes-parser": {"version": "0.12.0", "license": "MIT", "dependencies": {"hermes-estree": "0.12.0"}}, "node_modules/metro-config/node_modules/jest-get-type": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/metro-config/node_modules/jest-validate": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/metro-config/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/metro-config/node_modules/metro": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/parser": "^7.20.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "accepts": "^1.3.7", "async": "^3.2.2", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^2.2.0", "denodeify": "^1.2.1", "error-stack-parser": "^2.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.12.0", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^27.2.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.76.9", "metro-cache": "0.76.9", "metro-cache-key": "0.76.9", "metro-config": "0.76.9", "metro-core": "0.76.9", "metro-file-map": "0.76.9", "metro-inspector-proxy": "0.76.9", "metro-minify-uglify": "0.76.9", "metro-react-native-babel-preset": "0.76.9", "metro-resolver": "0.76.9", "metro-runtime": "0.76.9", "metro-source-map": "0.76.9", "metro-symbolicate": "0.76.9", "metro-transform-plugins": "0.76.9", "metro-transform-worker": "0.76.9", "mime-types": "^2.1.27", "node-fetch": "^2.2.0", "nullthrows": "^1.1.1", "rimraf": "^3.0.2", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "strip-ansi": "^6.0.0", "throat": "^5.0.0", "ws": "^7.5.1", "yargs": "^17.6.2"}, "bin": {"metro": "src/cli.js"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/metro-babel-transformer": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "hermes-parser": "0.12.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/metro-core": {"version": "0.76.9", "license": "MIT", "dependencies": {"lodash.throttle": "^4.1.1", "metro-resolver": "0.76.9"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/metro-react-native-babel-preset": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-async-generator-functions": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.20.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.20.0", "@babel/plugin-syntax-dynamic-import": "^7.8.0", "@babel/plugin-syntax-export-default-from": "^7.0.0", "@babel/plugin-syntax-flow": "^7.18.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-syntax-optional-chaining": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.20.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.20.0", "@babel/plugin-transform-flow-strip-types": "^7.20.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-typescript": "^7.5.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "@babel/template": "^7.0.0", "babel-plugin-transform-flow-enums": "^0.0.2", "react-refresh": "^0.4.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro-config/node_modules/metro-resolver": {"version": "0.76.9", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/metro-runtime": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/runtime": "^7.0.0", "react-refresh": "^0.4.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/metro-source-map": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "invariant": "^2.2.4", "metro-symbolicate": "0.76.9", "nullthrows": "^1.1.1", "ob1": "0.76.9", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-config/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro-config/node_modules/pretty-format": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/metro-config/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/metro-config/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/metro-config/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/metro-config/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/metro-config/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/metro-core": {"version": "0.67.0", "license": "MIT", "dependencies": {"jest-haste-map": "^27.3.1", "lodash.throttle": "^4.1.1", "metro-resolver": "0.67.0"}}, "node_modules/metro-core/node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-core/node_modules/@types/yargs": {"version": "16.0.9", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/metro-core/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/metro-core/node_modules/jest-haste-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^27.5.1", "jest-serializer": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "micromatch": "^4.0.4", "walker": "^1.0.7"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/metro-core/node_modules/jest-regex-util": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-core/node_modules/jest-serializer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-core/node_modules/jest-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-core/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/metro-core/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/metro-core/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/metro-file-map": {"version": "0.76.9", "license": "MIT", "dependencies": {"anymatch": "^3.0.3", "debug": "^2.2.0", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.4", "invariant": "^2.2.4", "jest-regex-util": "^27.0.6", "jest-util": "^27.2.0", "jest-worker": "^27.2.0", "micromatch": "^4.0.4", "node-abort-controller": "^3.1.1", "nullthrows": "^1.1.1", "walker": "^1.0.7"}, "engines": {"node": ">=16"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/metro-file-map/node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-file-map/node_modules/@types/yargs": {"version": "16.0.9", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/metro-file-map/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/metro-file-map/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/metro-file-map/node_modules/jest-regex-util": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-file-map/node_modules/jest-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro-file-map/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/metro-file-map/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro-file-map/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/metro-file-map/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/metro-hermes-compiler": {"version": "0.67.0", "license": "MIT"}, "node_modules/metro-inspector-proxy": {"version": "0.76.9", "license": "MIT", "dependencies": {"connect": "^3.6.5", "debug": "^2.2.0", "node-fetch": "^2.2.0", "ws": "^7.5.1", "yargs": "^17.6.2"}, "bin": {"metro-inspector-proxy": "src/cli.js"}, "engines": {"node": ">=16"}}, "node_modules/metro-inspector-proxy/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/metro-inspector-proxy/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/metro-inspector-proxy/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro-inspector-proxy/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/metro-inspector-proxy/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/metro-minify-terser": {"version": "0.76.9", "license": "MIT", "dependencies": {"terser": "^5.15.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-minify-uglify": {"version": "0.76.9", "license": "MIT", "dependencies": {"uglify-es": "^3.1.9"}, "engines": {"node": ">=16"}}, "node_modules/metro-react-native-babel-preset": {"version": "0.65.2", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-export-default-from": "^7.0.0", "@babel/plugin-syntax-flow": "^7.2.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-syntax-optional-chaining": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.0.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.0.0", "@babel/plugin-transform-exponentiation-operator": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.0.0", "@babel/plugin-transform-for-of": "^7.0.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-object-assign": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-regenerator": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "@babel/plugin-transform-typescript": "^7.5.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "@babel/template": "^7.0.0", "react-refresh": "^0.4.0"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro-react-native-babel-transformer": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "babel-preset-fbjs": "^3.4.0", "hermes-parser": "0.5.0", "metro-babel-transformer": "0.67.0", "metro-react-native-babel-preset": "0.67.0", "metro-source-map": "0.67.0", "nullthrows": "^1.1.1"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro-react-native-babel-transformer/node_modules/metro-react-native-babel-preset": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-export-default-from": "^7.0.0", "@babel/plugin-syntax-flow": "^7.2.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-syntax-optional-chaining": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.0.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.0.0", "@babel/plugin-transform-exponentiation-operator": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.0.0", "@babel/plugin-transform-for-of": "^7.0.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-object-assign": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-regenerator": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "@babel/plugin-transform-typescript": "^7.5.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "@babel/template": "^7.0.0", "react-refresh": "^0.4.0"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro-resolver": {"version": "0.67.0", "license": "MIT", "dependencies": {"absolute-path": "^0.0.0"}}, "node_modules/metro-runtime": {"version": "0.67.0", "license": "MIT"}, "node_modules/metro-source-map": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/traverse": "^7.14.0", "@babel/types": "^7.0.0", "invariant": "^2.2.4", "metro-symbolicate": "0.67.0", "nullthrows": "^1.1.1", "ob1": "0.67.0", "source-map": "^0.5.6", "vlq": "^1.0.0"}}, "node_modules/metro-source-map/node_modules/metro-symbolicate": {"version": "0.67.0", "license": "MIT", "dependencies": {"invariant": "^2.2.4", "metro-source-map": "0.67.0", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "through2": "^2.0.1", "vlq": "^1.0.0"}, "bin": {"metro-symbolicate": "src/index.js"}, "engines": {"node": ">=8.3"}}, "node_modules/metro-source-map/node_modules/ob1": {"version": "0.67.0", "license": "MIT"}, "node_modules/metro-source-map/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/metro-symbolicate": {"version": "0.76.9", "license": "MIT", "dependencies": {"invariant": "^2.2.4", "metro-source-map": "0.76.9", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "through2": "^2.0.1", "vlq": "^1.0.0"}, "bin": {"metro-symbolicate": "src/index.js"}, "engines": {"node": ">=16"}}, "node_modules/metro-symbolicate/node_modules/metro-source-map": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "invariant": "^2.2.4", "metro-symbolicate": "0.76.9", "nullthrows": "^1.1.1", "ob1": "0.76.9", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-symbolicate/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/metro-transform-plugins": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.20.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "babel-preset-fbjs": "^3.4.0", "metro": "0.76.9", "metro-babel-transformer": "0.76.9", "metro-cache": "0.76.9", "metro-cache-key": "0.76.9", "metro-minify-terser": "0.76.9", "metro-source-map": "0.76.9", "metro-transform-plugins": "0.76.9", "nullthrows": "^1.1.1"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/metro-transform-worker/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/metro-transform-worker/node_modules/hermes-parser": {"version": "0.12.0", "license": "MIT", "dependencies": {"hermes-estree": "0.12.0"}}, "node_modules/metro-transform-worker/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/metro-transform-worker/node_modules/metro": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/core": "^7.20.0", "@babel/generator": "^7.20.0", "@babel/parser": "^7.20.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "accepts": "^1.3.7", "async": "^3.2.2", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^2.2.0", "denodeify": "^1.2.1", "error-stack-parser": "^2.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.12.0", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^27.2.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.76.9", "metro-cache": "0.76.9", "metro-cache-key": "0.76.9", "metro-config": "0.76.9", "metro-core": "0.76.9", "metro-file-map": "0.76.9", "metro-inspector-proxy": "0.76.9", "metro-minify-uglify": "0.76.9", "metro-react-native-babel-preset": "0.76.9", "metro-resolver": "0.76.9", "metro-runtime": "0.76.9", "metro-source-map": "0.76.9", "metro-symbolicate": "0.76.9", "metro-transform-plugins": "0.76.9", "metro-transform-worker": "0.76.9", "mime-types": "^2.1.27", "node-fetch": "^2.2.0", "nullthrows": "^1.1.1", "rimraf": "^3.0.2", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "strip-ansi": "^6.0.0", "throat": "^5.0.0", "ws": "^7.5.1", "yargs": "^17.6.2"}, "bin": {"metro": "src/cli.js"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/metro-babel-transformer": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "hermes-parser": "0.12.0", "nullthrows": "^1.1.1"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/metro-core": {"version": "0.76.9", "license": "MIT", "dependencies": {"lodash.throttle": "^4.1.1", "metro-resolver": "0.76.9"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/metro-react-native-babel-preset": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-async-generator-functions": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.20.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.20.0", "@babel/plugin-syntax-dynamic-import": "^7.8.0", "@babel/plugin-syntax-export-default-from": "^7.0.0", "@babel/plugin-syntax-flow": "^7.18.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-syntax-optional-chaining": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.20.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.20.0", "@babel/plugin-transform-flow-strip-types": "^7.20.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-typescript": "^7.5.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "@babel/template": "^7.0.0", "babel-plugin-transform-flow-enums": "^0.0.2", "react-refresh": "^0.4.0"}, "engines": {"node": ">=16"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro-transform-worker/node_modules/metro-resolver": {"version": "0.76.9", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/metro-runtime": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/runtime": "^7.0.0", "react-refresh": "^0.4.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/metro-source-map": {"version": "0.76.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.20.0", "@babel/types": "^7.20.0", "invariant": "^2.2.4", "metro-symbolicate": "0.76.9", "nullthrows": "^1.1.1", "ob1": "0.76.9", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "engines": {"node": ">=16"}}, "node_modules/metro-transform-worker/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro-transform-worker/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/metro-transform-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/metro-transform-worker/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/metro-transform-worker/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/metro/node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro/node_modules/@types/yargs": {"version": "16.0.9", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/metro/node_modules/async": {"version": "2.6.4", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/metro/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/metro/node_modules/fs-extra": {"version": "1.0.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0"}}, "node_modules/metro/node_modules/image-size": {"version": "0.6.3", "license": "MIT", "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=4.0"}}, "node_modules/metro/node_modules/jest-haste-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^27.5.1", "jest-serializer": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "micromatch": "^4.0.4", "walker": "^1.0.7"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/metro/node_modules/jest-haste-map/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/metro/node_modules/jest-regex-util": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro/node_modules/jest-serializer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro/node_modules/jest-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/metro/node_modules/jest-util/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/metro/node_modules/jsonfile": {"version": "2.4.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/metro/node_modules/metro-cache": {"version": "0.67.0", "license": "MIT", "dependencies": {"metro-core": "0.67.0", "mkdirp": "^0.5.1", "rimraf": "^2.5.4"}}, "node_modules/metro/node_modules/metro-cache-key": {"version": "0.67.0", "license": "MIT"}, "node_modules/metro/node_modules/metro-config": {"version": "0.67.0", "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "jest-validate": "^26.5.2", "metro": "0.67.0", "metro-cache": "0.67.0", "metro-core": "0.67.0", "metro-runtime": "0.67.0"}}, "node_modules/metro/node_modules/metro-inspector-proxy": {"version": "0.67.0", "license": "MIT", "dependencies": {"connect": "^3.6.5", "debug": "^2.2.0", "ws": "^7.5.1", "yargs": "^15.3.1"}, "bin": {"metro-inspector-proxy": "src/cli.js"}}, "node_modules/metro/node_modules/metro-minify-uglify": {"version": "0.67.0", "license": "MIT", "dependencies": {"uglify-es": "^3.1.9"}}, "node_modules/metro/node_modules/metro-react-native-babel-preset": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-catch-binding": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-export-default-from": "^7.0.0", "@babel/plugin-syntax-flow": "^7.2.0", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-syntax-optional-chaining": "^7.0.0", "@babel/plugin-transform-arrow-functions": "^7.0.0", "@babel/plugin-transform-async-to-generator": "^7.0.0", "@babel/plugin-transform-block-scoping": "^7.0.0", "@babel/plugin-transform-classes": "^7.0.0", "@babel/plugin-transform-computed-properties": "^7.0.0", "@babel/plugin-transform-destructuring": "^7.0.0", "@babel/plugin-transform-exponentiation-operator": "^7.0.0", "@babel/plugin-transform-flow-strip-types": "^7.0.0", "@babel/plugin-transform-for-of": "^7.0.0", "@babel/plugin-transform-function-name": "^7.0.0", "@babel/plugin-transform-literals": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-object-assign": "^7.0.0", "@babel/plugin-transform-parameters": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0", "@babel/plugin-transform-regenerator": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-transform-shorthand-properties": "^7.0.0", "@babel/plugin-transform-spread": "^7.0.0", "@babel/plugin-transform-sticky-regex": "^7.0.0", "@babel/plugin-transform-template-literals": "^7.0.0", "@babel/plugin-transform-typescript": "^7.5.0", "@babel/plugin-transform-unicode-regex": "^7.0.0", "@babel/template": "^7.0.0", "react-refresh": "^0.4.0"}, "peerDependencies": {"@babel/core": "*"}}, "node_modules/metro/node_modules/metro-symbolicate": {"version": "0.67.0", "license": "MIT", "dependencies": {"invariant": "^2.2.4", "metro-source-map": "0.67.0", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "through2": "^2.0.1", "vlq": "^1.0.0"}, "bin": {"metro-symbolicate": "src/index.js"}, "engines": {"node": ">=8.3"}}, "node_modules/metro/node_modules/metro-transform-plugins": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "@babel/generator": "^7.14.0", "@babel/template": "^7.0.0", "@babel/traverse": "^7.14.0", "nullthrows": "^1.1.1"}}, "node_modules/metro/node_modules/metro-transform-worker": {"version": "0.67.0", "license": "MIT", "dependencies": {"@babel/core": "^7.14.0", "@babel/generator": "^7.14.0", "@babel/parser": "^7.14.0", "@babel/types": "^7.0.0", "babel-preset-fbjs": "^3.4.0", "metro": "0.67.0", "metro-babel-transformer": "0.67.0", "metro-cache": "0.67.0", "metro-cache-key": "0.67.0", "metro-hermes-compiler": "0.67.0", "metro-source-map": "0.67.0", "metro-transform-plugins": "0.67.0", "nullthrows": "^1.1.1"}}, "node_modules/metro/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/metro/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/metro/node_modules/rimraf": {"version": "2.7.1", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/metro/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/metro/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/metro/node_modules/temp": {"version": "0.8.3", "engines": ["node >=0.8.0"], "license": "MIT", "dependencies": {"os-tmpdir": "^1.0.0", "rimraf": "~2.2.6"}}, "node_modules/metro/node_modules/temp/node_modules/rimraf": {"version": "2.2.8", "license": "MIT", "bin": {"rimraf": "bin.js"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/micromatch/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/mime": {"version": "2.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mixin-deep": {"version": "1.3.2", "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-deep/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-object": {"version": "2.0.1", "license": "MIT", "dependencies": {"for-in": "^0.1.3", "is-extendable": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-object/node_modules/for-in": {"version": "0.1.8", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/modal-react-native-web": {"version": "0.2.0", "license": "MIT", "dependencies": {"warning": "^4.0.1"}, "peerDependencies": {"react": "16.x.x", "react-art": "16.x.x", "react-dom": "16.x.x", "react-native-web": "0.9.x"}}, "node_modules/moment": {"version": "2.30.1", "license": "MIT", "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/mute-stream": {"version": "0.0.8", "license": "ISC"}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/nanomatch": {"version": "1.2.13", "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/nanomatch/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/netmask": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/nice-try": {"version": "1.0.5", "license": "MIT"}, "node_modules/nocache": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/node-abort-controller": {"version": "3.1.1", "license": "MIT"}, "node_modules/node-dir": {"version": "0.1.17", "license": "MIT", "dependencies": {"minimatch": "^3.0.2"}, "engines": {"node": ">= 0.10.5"}}, "node_modules/node-emoji": {"version": "1.10.0", "license": "MIT", "dependencies": {"lodash.toarray": "^4.4.0"}}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-fetch/node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/node-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/node-fetch/node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "node_modules/node-notifier": {"version": "8.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}}, "node_modules/node-notifier/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/node-releases": {"version": "2.0.23", "license": "MIT"}, "node_modules/node-stream-zip": {"version": "1.15.0", "license": "MIT", "engines": {"node": ">=0.12.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/antelle"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "2.0.2", "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/nth-check": {"version": "1.0.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "~1.0.0"}}, "node_modules/nullthrows": {"version": "1.1.1", "license": "MIT"}, "node_modules/nwsapi": {"version": "2.2.22", "dev": true, "license": "MIT"}, "node_modules/ob1": {"version": "0.76.9", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy": {"version": "0.1.0", "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object-visit": {"version": "1.0.1", "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.1.8", "dev": true, "license": "MIT", "dependencies": {"array.prototype.reduce": "^1.0.6", "call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "gopd": "^1.0.1", "safe-array-concat": "^1.1.2"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.pick": {"version": "1.3.0", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.values": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "6.4.0", "license": "MIT", "dependencies": {"is-wsl": "^1.1.0"}, "engines": {"node": ">=8"}}, "node_modules/open/node_modules/is-wsl": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/opencollective-postinstall": {"version": "2.0.3", "license": "MIT", "bin": {"opencollective-postinstall": "index.js"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "5.4.1", "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/own-keys": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-each-series": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-finally": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pac-proxy-agent": {"version": "7.2.0", "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "node_modules/pac-resolver": {"version": "7.0.1", "license": "MIT", "dependencies": {"degenerator": "^5.0.0", "netmask": "^2.0.2"}, "engines": {"node": ">= 14"}}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "6.0.1", "dev": true, "license": "MIT"}, "node_modules/parseqs": {"version": "0.0.6", "license": "MIT"}, "node_modules/parseuri": {"version": "0.0.6", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascalcase": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/patch-package": {"version": "8.0.1", "dev": true, "license": "MIT", "dependencies": {"@yarnpkg/lockfile": "^1.1.0", "chalk": "^4.1.2", "ci-info": "^3.7.0", "cross-spawn": "^7.0.3", "find-yarn-workspace-root": "^2.0.0", "fs-extra": "^10.0.0", "json-stable-stringify": "^1.0.2", "klaw-sync": "^6.0.0", "minimist": "^1.2.6", "open": "^7.4.2", "semver": "^7.5.3", "slash": "^2.0.0", "tmp": "^0.2.4", "yaml": "^2.2.2"}, "bin": {"patch-package": "index.js"}, "engines": {"node": ">=14", "npm": ">5"}}, "node_modules/patch-package/node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/patch-package/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/patch-package/node_modules/open": {"version": "7.4.2", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0", "is-wsl": "^2.1.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/patch-package/node_modules/semver": {"version": "7.7.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/patch-package/node_modules/slash": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/path-dirname": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/pify": {"version": "4.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pirates": {"version": "4.0.7", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/plist": {"version": "3.1.0", "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/posix-character-classes": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postinstall-postinstall": {"version": "2.1.0", "dev": true, "hasInstallScript": true, "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-format": {"version": "26.6.2", "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^17.0.1"}, "engines": {"node": ">= 10"}}, "node_modules/pretty-format/node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/progress": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "7.3.1", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/proxy-agent": {"version": "6.5.0", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.6", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.1.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "node_modules/proxy-agent/node_modules/lru-cache": {"version": "7.18.3", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/psl": {"version": "1.15.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/pump": {"version": "3.0.3", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/q": {"version": "1.5.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "6.14.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/query-string": {"version": "6.14.1", "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.0", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/querystringify": {"version": "2.2.0", "license": "MIT"}, "node_modules/queue": {"version": "6.0.2", "license": "MIT", "dependencies": {"inherits": "~2.0.3"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/react": {"version": "16.13.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-addons-shallow-compare": {"version": "15.6.2", "license": "MIT", "dependencies": {"fbjs": "^0.8.4", "object-assign": "^4.1.0"}}, "node_modules/react-async-hook": {"version": "3.6.1", "license": "MIT", "engines": {"node": ">=8", "npm": ">=5"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/react-devtools-core": {"version": "4.28.5", "license": "MIT", "dependencies": {"shell-quote": "^1.6.1", "ws": "^7"}}, "node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/react-lifecycles-compat": {"version": "3.0.4", "license": "MIT"}, "node_modules/react-native": {"version": "0.68.5", "license": "MIT", "dependencies": {"@jest/create-cache-key-function": "^27.0.1", "@react-native-community/cli": "^7.0.3", "@react-native-community/cli-platform-android": "^7.0.1", "@react-native-community/cli-platform-ios": "^7.0.1", "@react-native/assets": "1.0.0", "@react-native/normalize-color": "2.0.0", "@react-native/polyfills": "2.0.0", "abort-controller": "^3.0.0", "anser": "^1.4.9", "base64-js": "^1.1.2", "deprecated-react-native-prop-types": "^2.3.0", "event-target-shim": "^5.0.1", "hermes-engine": "~0.11.0", "invariant": "^2.2.4", "jsc-android": "^250230.2.1", "metro-react-native-babel-transformer": "0.67.0", "metro-runtime": "0.67.0", "metro-source-map": "0.67.0", "nullthrows": "^1.1.1", "pretty-format": "^26.5.2", "promise": "^8.2.0", "react-devtools-core": "^4.23.0", "react-native-codegen": "^0.0.18", "react-native-gradle-plugin": "^0.0.6", "react-refresh": "^0.4.0", "react-shallow-renderer": "16.14.1", "regenerator-runtime": "^0.13.2", "scheduler": "^0.20.2", "stacktrace-parser": "^0.1.3", "use-subscription": ">=1.0.0 <1.6.0", "whatwg-fetch": "^3.0.0", "ws": "^6.1.4"}, "bin": {"react-native": "cli.js"}, "engines": {"node": ">=14"}, "peerDependencies": {"react": "17.0.2"}}, "node_modules/react-native-action-button": {"version": "2.8.5", "license": "MIT", "dependencies": {"prop-types": "^15.5.10"}, "peerDependencies": {"react-native": ">=0.11"}}, "node_modules/react-native-animatable": {"version": "1.4.0", "license": "MIT", "dependencies": {"prop-types": "^15.8.1"}}, "node_modules/react-native-app-intro-slider": {"version": "4.0.4", "license": "MIT"}, "node_modules/react-native-background-timer": {"version": "2.4.1", "license": "MIT", "peerDependencies": {"react-native": ">=0.47.0"}}, "node_modules/react-native-ble-manager": {"version": "10.1.5", "license": "Apache-2.0", "peerDependencies": {"react-native": ">=0.60.0"}}, "node_modules/react-native-bluetooth-state-manager": {"version": "1.3.5", "license": "MIT", "peerDependencies": {"prop-types": "*", "react-native": ">= 0.44"}}, "node_modules/react-native-camera": {"version": "3.40.0", "license": "MIT AND Apache-2.0 AND BSD-3-<PERSON><PERSON>", "dependencies": {"prop-types": "^15.6.2"}}, "node_modules/react-native-card-stack-swiper": {"version": "1.2.5", "license": "MIT", "dependencies": {"prop-types": "15.5.10", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": "^16.0.0-beta.5", "react-native": "^0.49.1"}}, "node_modules/react-native-card-stack-swiper/node_modules/prop-types": {"version": "15.5.10", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"fbjs": "^0.8.9", "loose-envify": "^1.3.1"}}, "node_modules/react-native-carplay": {"version": "2.3.0", "license": "MIT", "dependencies": {"traverse": "^0.6.6"}}, "node_modules/react-native-code-push": {"version": "7.1.0", "license": "MIT", "dependencies": {"code-push": "^4.1.0", "glob": "^7.1.7", "hoist-non-react-statics": "^3.3.2", "inquirer": "^8.1.5", "plist": "^3.0.4", "semver": "^7.3.5", "xcode": "3.0.1"}}, "node_modules/react-native-code-push/node_modules/semver": {"version": "7.7.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/react-native-codegen": {"version": "0.0.18", "license": "MIT", "dependencies": {"@babel/parser": "^7.14.0", "flow-parser": "^0.121.0", "jscodeshift": "^0.13.1", "nullthrows": "^1.1.1"}}, "node_modules/react-native-config": {"version": "1.5.9", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.0", "@babel/preset-react": "^7.24.7", "babel-jest": "^29.7.0"}, "peerDependencies": {"react-native-windows": ">=0.61"}, "peerDependenciesMeta": {"react-native-windows": {"optional": true}}}, "node_modules/react-native-config/node_modules/@jest/transform": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/@jest/types": {"version": "29.6.3", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/react-native-config/node_modules/babel-jest": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/react-native-config/node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/babel-preset-jest": {"version": "29.6.3", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/react-native-config/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/react-native-config/node_modules/jest-haste-map": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/react-native-config/node_modules/jest-regex-util": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/jest-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/jest-worker": {"version": "29.7.0", "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/react-native-config/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/react-native-config/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/react-native-config/node_modules/write-file-atomic": {"version": "4.0.2", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/react-native-country-picker-modal": {"version": "2.0.0", "license": "MIT", "dependencies": {"@callstack/react-theme-provider": "3.0.3", "fuse.js": "3.4.5", "modal-react-native-web": "0.2.0", "node-emoji": "1.10.0", "prop-types": "15.7.2", "react-async-hook": "3.6.1"}, "peerDependencies": {"react": "*", "react-dom": "*", "react-native": "*", "react-native-web": "*"}}, "node_modules/react-native-country-picker-modal/node_modules/prop-types": {"version": "15.7.2", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.8.1"}}, "node_modules/react-native-datepicker": {"version": "1.7.2", "license": "MIT", "dependencies": {"moment": "^2.22.0"}}, "node_modules/react-native-deck-swiper": {"version": "2.0.19", "license": "ISC", "dependencies": {"lodash": "^4.17.21", "prop-types": "15.5.10"}, "peerDependencies": {"react": "^16.0.0-beta.5 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-native": ">=0.49.1"}}, "node_modules/react-native-deck-swiper/node_modules/prop-types": {"version": "15.5.10", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"fbjs": "^0.8.9", "loose-envify": "^1.3.1"}}, "node_modules/react-native-device-info": {"version": "8.7.1", "license": "MIT", "peerDependencies": {"react-native": "*"}}, "node_modules/react-native-event-listeners": {"version": "1.0.7", "license": "MIT", "dependencies": {"type-detect": "^4.0.8"}}, "node_modules/react-native-event-listeners/node_modules/type-detect": {"version": "4.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/react-native-gesture-handler": {"version": "1.10.3", "license": "MIT", "dependencies": {"@egjs/hammerjs": "^2.0.17", "fbjs": "^3.0.0", "hoist-non-react-statics": "^3.3.0", "invariant": "^2.2.4", "prop-types": "^15.7.2"}}, "node_modules/react-native-gesture-handler/node_modules/fbjs": {"version": "3.0.5", "license": "MIT", "dependencies": {"cross-fetch": "^3.1.5", "fbjs-css-vars": "^1.0.0", "loose-envify": "^1.0.0", "object-assign": "^4.1.0", "promise": "^7.1.1", "setimmediate": "^1.0.5", "ua-parser-js": "^1.0.35"}}, "node_modules/react-native-get-location": {"version": "2.2.1", "license": "MIT", "peerDependencies": {"react-native": ">=0.60"}}, "node_modules/react-native-gradle-plugin": {"version": "0.0.6", "license": "MIT"}, "node_modules/react-native-image-picker": {"version": "5.4.0", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-image-slider-box": {"version": "1.1.24", "license": "MIT", "dependencies": {"react-native-snap-carousel": "latest"}}, "node_modules/react-native-in-app-notification": {"version": "3.2.0", "license": "MIT", "dependencies": {"hoist-non-react-statics": "^3.0.1", "react-native-iphone-x-helper": "^1.2.0", "react-native-swipe-gestures": "^1.0.2"}, "peerDependencies": {"prop-types": "^15.5.10", "react": ">=16.3.0", "react-native": ">=0.54.0"}}, "node_modules/react-native-inappbrowser-reborn": {"version": "3.7.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"invariant": "^2.2.4", "opencollective-postinstall": "^2.0.3"}, "peerDependencies": {"react-native": ">=0.56"}}, "node_modules/react-native-iphone-x-helper": {"version": "1.3.1", "license": "MIT", "peerDependencies": {"react-native": ">=0.42.0"}}, "node_modules/react-native-linear-gradient": {"version": "2.8.3", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-permissions": {"version": "3.6.1", "license": "MIT", "peerDependencies": {"react": ">=16.13.1", "react-native": ">=0.63.3", "react-native-windows": ">=0.62.0"}, "peerDependenciesMeta": {"react-native-windows": {"optional": true}}}, "node_modules/react-native-progress": {"version": "4.1.2", "license": "MIT", "dependencies": {"@react-native-community/art": "^1.1.2", "prop-types": "^15.7.2"}}, "node_modules/react-native-push-notification": {"version": "7.4.0", "license": "MIT", "peerDependencies": {"@react-native-community/push-notification-ios": "^1.8.0", "react-native": ">=0.33"}}, "node_modules/react-native-qrcode-scanner": {"version": "1.6.0", "license": "MIT", "dependencies": {"@react-native-community/async-storage": "^1.6.1", "prop-types": "^15.5.10", "react-native-permissions": "^2.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-native-qrcode-scanner"}, "peerDependencies": {"react-native-camera": ">=1.0.2"}}, "node_modules/react-native-qrcode-scanner/node_modules/react-native-permissions": {"version": "2.2.2", "license": "MIT", "peerDependencies": {"react": ">=16.8.6", "react-native": ">=0.60.0"}}, "node_modules/react-native-reanimated": {"version": "2.17.0", "resolved": "https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-2.17.0.tgz", "integrity": "sha512-bVy+FUEaHXq4i+aPPqzGeor1rG4scgVNBbBz21ohvC7iMpB9IIgvGsmy1FAoodZhZ5sa3EPF67Rcec76F1PXlQ==", "dependencies": {"@babel/plugin-transform-object-assign": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "invariant": "^2.2.4", "lodash.isequal": "^4.5.0", "setimmediate": "^1.0.5", "string-hash-64": "^1.0.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "react": "*", "react-native": "*"}}, "node_modules/react-native-render-html": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"deprecated-prop-type": "^1.0.0", "htmlparser2": "5.0.1", "prop-types": "^15.7.2", "stringify-entities": "^3.1.0"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-restart": {"version": "0.0.22", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-safe-area-context": {"version": "3.4.1", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-screens": {"version": "2.18.1", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-share": {"version": "7.9.1", "license": "MIT"}, "node_modules/react-native-simple-toast": {"version": "1.1.4", "license": "MIT"}, "node_modules/react-native-snap-carousel": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/react-native-snap-carousel/-/react-native-snap-carousel-3.9.1.tgz", "integrity": "sha512-xWEGusacIgK1YaDXLi7Gao2+ISLoGPVEBR8fcMf4tOOJQufutlNwkoLu0l6B8Qgsrre0nTxoVZikRgGRDWlLaQ==", "dependencies": {"prop-types": "^15.6.1", "react-addons-shallow-compare": "15.6.2"}, "peerDependencies": {"react": ">=15.0.0", "react-native": "*"}}, "node_modules/react-native-splash-screen": {"version": "3.3.0", "license": "MIT", "peerDependencies": {"react-native": ">=0.57.0"}}, "node_modules/react-native-svg": {"version": "12.5.1", "license": "MIT", "dependencies": {"css-select": "^5.1.0", "css-tree": "^1.1.3"}, "peerDependencies": {"react": "*", "react-native": ">=0.50.0"}}, "node_modules/react-native-svg-transformer": {"version": "0.14.3", "dev": true, "license": "MIT", "dependencies": {"@svgr/core": "^4.3.3", "@svgr/plugin-svgo": "^4.3.1", "path-dirname": "^1.0.2", "semver": "^5.6.0"}, "peerDependencies": {"react-native": ">=0.45.0", "react-native-svg": ">=6.5.1"}}, "node_modules/react-native-svg-transformer/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/react-native-svg/node_modules/css-select": {"version": "5.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/react-native-svg/node_modules/css-what": {"version": "6.2.2", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/react-native-svg/node_modules/dom-serializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/react-native-svg/node_modules/domutils": {"version": "3.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/react-native-svg/node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/react-native-svg/node_modules/nth-check": {"version": "2.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/react-native-swipe-gestures": {"version": "1.0.5", "license": "MIT"}, "node_modules/react-native-switch": {"version": "2.0.0", "license": "MIT", "dependencies": {"prop-types": "^15.6.0"}, "peerDependencies": {"react-native": ">=0.29"}}, "node_modules/react-native-vector-icons": {"version": "8.1.0", "license": "MIT", "dependencies": {"lodash.frompairs": "^4.0.1", "lodash.isequal": "^4.5.0", "lodash.isstring": "^4.0.1", "lodash.omit": "^4.5.0", "lodash.pick": "^4.4.0", "lodash.template": "^4.5.0", "prop-types": "^15.7.2", "yargs": "^16.1.1"}, "bin": {"fa5-upgrade": "bin/fa5-upgrade.sh", "generate-icon": "bin/generate-icon.js"}}, "node_modules/react-native-vector-icons/node_modules/cliui": {"version": "7.0.4", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/react-native-vector-icons/node_modules/yargs": {"version": "16.2.0", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/react-native-vector-icons/node_modules/yargs-parser": {"version": "20.2.9", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/react-native-video": {"version": "5.2.2", "license": "MIT", "dependencies": {"deprecated-react-native-prop-types": "^2.2.0", "keymirror": "^0.1.1", "prop-types": "^15.7.2", "shaka-player": "^2.5.9"}}, "node_modules/react-native-webview": {"version": "11.26.1", "license": "MIT", "dependencies": {"escape-string-regexp": "2.0.0", "invariant": "2.2.4"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "node_modules/react-native-webview/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/react-native/node_modules/@react-native/normalize-color": {"version": "2.0.0", "license": "MIT"}, "node_modules/react-native/node_modules/promise": {"version": "8.3.0", "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "node_modules/react-native/node_modules/ws": {"version": "6.2.3", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/react-reconciler": {"version": "0.33.0", "license": "MIT", "dependencies": {"scheduler": "^0.27.0"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"react": "^19.2.0"}}, "node_modules/react-reconciler/node_modules/scheduler": {"version": "0.27.0", "license": "MIT"}, "node_modules/react-redux": {"version": "7.2.9", "license": "MIT", "dependencies": {"@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-is": "^17.0.2"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "node_modules/react-redux/node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/react-refresh": {"version": "0.4.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-shallow-renderer": {"version": "16.14.1", "license": "MIT", "dependencies": {"object-assign": "^4.1.1", "react-is": "^16.12.0 || ^17.0.0"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0"}}, "node_modules/react-shallow-renderer/node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/react-test-renderer": {"version": "16.13.1", "dev": true, "license": "MIT", "dependencies": {"object-assign": "^4.1.1", "prop-types": "^15.6.2", "react-is": "^16.8.6", "scheduler": "^0.19.1"}, "peerDependencies": {"react": "^16.13.1"}}, "node_modules/react-test-renderer/node_modules/scheduler": {"version": "0.19.1", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "node_modules/read-pkg": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readable-stream/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/readline": {"version": "1.3.0", "license": "BSD"}, "node_modules/recast": {"version": "0.20.5", "license": "MIT", "dependencies": {"ast-types": "0.14.2", "esprima": "~4.0.0", "source-map": "~0.6.1", "tslib": "^2.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/recast/node_modules/ast-types": {"version": "0.14.2", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/recursive-fs": {"version": "2.1.0", "license": "MIT", "bin": {"recursive-copy": "bin/recursive-copy", "recursive-delete": "bin/recursive-delete"}, "engines": {"node": ">=10.0.0"}}, "node_modules/redux": {"version": "4.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.9.2"}}, "node_modules/redux-persist": {"version": "6.0.0", "license": "MIT", "peerDependencies": {"redux": ">4.0.0"}}, "node_modules/redux-thunk": {"version": "2.4.2", "license": "MIT", "peerDependencies": {"redux": "^4"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.2", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/regex-not": {"version": "1.0.2", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regex-not/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexpp": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/regexpu-core": {"version": "6.4.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.2", "regjsgen": "^0.8.0", "regjsparser": "^0.13.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.2.1"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "license": "MIT"}, "node_modules/regjsparser": {"version": "0.13.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.1.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/repeat-element": {"version": "1.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "2.0.0", "license": "ISC"}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-url": {"version": "0.2.1", "license": "MIT"}, "node_modules/restore-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/ret": {"version": "0.1.15", "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rn-fetch-blob": {"version": "0.12.0", "license": "MIT", "dependencies": {"base-64": "0.1.0", "glob": "7.0.6"}}, "node_modules/rn-fetch-blob/node_modules/glob": {"version": "7.0.6", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.2", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/rsvp": {"version": "4.8.5", "dev": true, "license": "MIT", "engines": {"node": "6.* || >= 7.*"}}, "node_modules/run-async": {"version": "2.4.1", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/rxjs": {"version": "7.8.2", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex": {"version": "1.1.0", "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sails.io.js": {"version": "1.2.1", "license": "MIT"}, "node_modules/sane": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"@cnakazawa/watch": "^1.0.3", "anymatch": "^2.0.0", "capture-exit": "^2.0.0", "exec-sh": "^0.3.2", "execa": "^1.0.0", "fb-watchman": "^2.0.0", "micromatch": "^3.1.4", "minimist": "^1.1.1", "walker": "~1.0.5"}, "bin": {"sane": "src/cli.js"}, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/sane/node_modules/anymatch": {"version": "2.0.0", "dev": true, "license": "ISC", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/sane/node_modules/braces": {"version": "2.3.2", "dev": true, "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/define-property": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/fill-range": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/is-extendable": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/kind-of": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/micromatch": {"version": "3.1.10", "dev": true, "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/micromatch/node_modules/extend-shallow": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/normalize-path": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sane/node_modules/to-regex-range": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sax": {"version": "1.2.4", "dev": true, "license": "ISC"}, "node_modules/saxes": {"version": "5.0.1", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/scheduler": {"version": "0.20.2", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/send/node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/send/node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/serialize-error": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-value": {"version": "2.0.1", "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shaka-player": {"version": "2.5.23", "license": "Apache-2.0", "dependencies": {"eme-encryption-scheme-polyfill": "^2.0.1"}}, "node_modules/shallow-clone": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^5.0.0", "mixin-object": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shallow-clone/node_modules/kind-of": {"version": "5.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/shellwords": {"version": "0.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/simple-plist": {"version": "1.4.0", "license": "MIT", "dependencies": {"bplist-creator": "0.1.1", "bplist-parser": "0.3.2", "plist": "^3.0.5"}}, "node_modules/simple-swizzle": {"version": "0.2.4", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-swizzle/node_modules/is-arrayish": {"version": "0.3.4", "license": "MIT"}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/smart-buffer": {"version": "4.2.0", "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/snapdragon": {"version": "0.8.2", "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/snapdragon/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/snapdragon/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/socket.io-client": {"version": "2.5.0", "license": "MIT", "dependencies": {"backo2": "1.0.2", "component-bind": "1.0.0", "component-emitter": "~1.3.0", "debug": "~3.1.0", "engine.io-client": "~3.5.0", "has-binary2": "~1.0.2", "indexof": "0.0.1", "parseqs": "0.0.6", "parseuri": "0.0.6", "socket.io-parser": "~3.3.0", "to-array": "0.1.4"}}, "node_modules/socket.io-client/node_modules/debug": {"version": "3.1.0", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/socket.io-client/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/socket.io-parser": {"version": "3.3.4", "license": "MIT", "dependencies": {"component-emitter": "~1.3.0", "debug": "~3.1.0", "isarray": "2.0.1"}}, "node_modules/socket.io-parser/node_modules/debug": {"version": "3.1.0", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/socket.io-parser/node_modules/isarray": {"version": "2.0.1", "license": "MIT"}, "node_modules/socket.io-parser/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/socks": {"version": "2.8.7", "license": "MIT", "dependencies": {"ip-address": "^10.0.1", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "8.0.5", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "engines": {"node": ">= 14"}}, "node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "license": "MIT"}, "node_modules/spdx-correct": {"version": "3.2.0", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.22", "dev": true, "license": "CC0-1.0"}, "node_modules/split-on-first": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/split-string": {"version": "3.1.0", "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split-string/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/split-string/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/stable": {"version": "0.1.8", "dev": true, "license": "MIT"}, "node_modules/stack-generator": {"version": "2.0.10", "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/stack-utils": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/stackframe": {"version": "1.3.4", "license": "MIT"}, "node_modules/stacktrace-parser": {"version": "0.1.11", "license": "MIT", "dependencies": {"type-fest": "^0.7.1"}, "engines": {"node": ">=6"}}, "node_modules/stacktrace-parser/node_modules/type-fest": {"version": "0.7.1", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/static-extend": {"version": "0.1.2", "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stop-iteration-iterator": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/stream-buffers": {"version": "2.2.0", "license": "Unlicense", "engines": {"node": ">= 0.10.0"}}, "node_modules/strict-uri-encode": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/string-hash-64": {"version": "1.0.3", "license": "MIT"}, "node_modules/string-length": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/string.prototype.includes": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/string.prototype.matchall": {"version": "4.0.12", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "regexp.prototype.flags": "^1.5.3", "set-function-name": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.repeat": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/stringify-entities": {"version": "3.1.0", "license": "MIT", "dependencies": {"character-entities-html4": "^1.0.0", "character-entities-legacy": "^1.0.0", "xtend": "^4.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-eof": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/sudo-prompt": {"version": "9.2.1", "license": "MIT"}, "node_modules/superagent": {"version": "5.1.0", "license": "MIT", "dependencies": {"component-emitter": "^1.3.0", "cookiejar": "^2.1.2", "debug": "^4.1.1", "fast-safe-stringify": "^2.0.6", "form-data": "^2.3.3", "formidable": "^1.2.1", "methods": "^1.1.2", "mime": "^2.4.4", "qs": "^6.7.0", "readable-stream": "^3.4.0", "semver": "^6.1.1"}, "engines": {"node": ">= 6.4.0"}}, "node_modules/superagent/node_modules/form-data": {"version": "2.5.5", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.35", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/superstruct": {"version": "0.6.2", "license": "MIT", "dependencies": {"clone-deep": "^2.0.1", "kind-of": "^6.0.1"}}, "node_modules/superstruct/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-hyperlinks": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-parser": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/svgo": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.37", "csso": "^4.0.2", "js-yaml": "^3.13.1", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=4.0.0"}}, "node_modules/svgo/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/css-tree": {"version": "1.0.0-alpha.37", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/svgo/node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/svgo/node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/symbol-tree": {"version": "3.2.4", "dev": true, "license": "MIT"}, "node_modules/table": {"version": "6.9.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/table/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/table/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/temp": {"version": "0.8.4", "license": "MIT", "dependencies": {"rimraf": "~2.6.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/temp/node_modules/rimraf": {"version": "2.6.3", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/terminal-link": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/terser": {"version": "5.44.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.15.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser/node_modules/acorn": {"version": "8.15.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/throat": {"version": "5.0.0", "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "license": "MIT"}, "node_modules/through2": {"version": "2.0.5", "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/through2/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/through2/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/through2/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/tmp": {"version": "0.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-array": {"version": "0.1.4"}, "node_modules/to-object-path": {"version": "0.3.0", "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/to-regex-range/node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/to-regex/node_modules/define-property": {"version": "2.0.2", "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex/node_modules/extend-shallow": {"version": "3.0.2", "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex/node_modules/is-extendable": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/toggle-switch-react-native": {"version": "3.3.0", "license": "MIT", "dependencies": {"prop-types": "^15.5.7"}, "peerDependencies": {"react-native": ">=0.47.1"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tough-cookie": {"version": "4.1.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/tr46": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/traverse": {"version": "0.6.11", "license": "MIT", "dependencies": {"gopd": "^1.2.0", "typedarray.prototype.slice": "^1.0.5", "which-typed-array": "^1.1.18"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/tsconfig-paths/node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "dev": true, "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/typedarray.prototype.slice": {"version": "1.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "math-intrinsics": "^1.1.0", "typed-array-buffer": "^1.0.3", "typed-array-byte-offset": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ua-parser-js": {"version": "1.0.41", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/uglify-es": {"version": "3.3.10", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"commander": "~2.14.1", "source-map": "~0.6.1"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/uglify-es/node_modules/commander": {"version": "2.14.1", "license": "MIT"}, "node_modules/unbox-primitive": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "7.14.0", "license": "MIT"}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/union-value": {"version": "1.0.1", "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unquote": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/unset-value": {"version": "1.0.0", "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/update-browserslist-db": {"version": "1.1.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "license": "MIT"}, "node_modules/url-parse": {"version": "1.5.10", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/use": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/use-subscription": {"version": "1.5.1", "license": "MIT", "dependencies": {"object-assign": "^4.1.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/util.promisify": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.2", "has-symbols": "^1.0.1", "object.getownpropertydescriptors": "^2.1.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "dev": true, "license": "MIT", "optional": true, "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache": {"version": "2.4.0", "dev": true, "license": "MIT"}, "node_modules/v8-to-istanbul": {"version": "7.1.2", "dev": true, "license": "ISC", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0", "source-map": "^0.7.3"}, "engines": {"node": ">=10.10.0"}}, "node_modules/v8-to-istanbul/node_modules/convert-source-map": {"version": "1.9.0", "dev": true, "license": "MIT"}, "node_modules/v8-to-istanbul/node_modules/source-map": {"version": "0.7.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 12"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vlq": {"version": "1.0.1", "license": "MIT"}, "node_modules/w3c-hr-time": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"browser-process-hrtime": "^1.0.0"}}, "node_modules/w3c-xmlserializer": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/warning": {"version": "4.0.3", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webidl-conversions": {"version": "6.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/whatwg-encoding": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.4.24"}}, "node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.4.24", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/whatwg-fetch": {"version": "3.6.20", "license": "MIT"}, "node_modules/whatwg-mimetype": {"version": "2.3.0", "dev": true, "license": "MIT"}, "node_modules/whatwg-url": {"version": "8.7.0", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.7.0", "tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "engines": {"node": ">=10"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-module": {"version": "2.0.1", "license": "ISC"}, "node_modules/which-typed-array": {"version": "1.1.19", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/write-file-atomic": {"version": "3.0.3", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xcode": {"version": "3.0.1", "license": "Apache-2.0", "dependencies": {"simple-plist": "^1.1.0", "uuid": "^7.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/xcode/node_modules/uuid": {"version": "7.0.3", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/xml-name-validator": {"version": "3.0.0", "dev": true, "license": "Apache-2.0"}, "node_modules/xmlbuilder": {"version": "15.1.1", "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/xmlchars": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/xmldoc": {"version": "1.3.0", "license": "MIT", "dependencies": {"sax": "^1.2.4"}}, "node_modules/xmldoc/node_modules/sax": {"version": "1.4.1", "license": "ISC"}, "node_modules/xmlhttprequest-ssl": {"version": "1.6.3", "engines": {"node": ">=0.4.0"}}, "node_modules/xtend": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/yaml": {"version": "2.8.1", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yargs": {"version": "15.4.1", "license": "MIT", "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "engines": {"node": ">=8"}}, "node_modules/yargs-parser": {"version": "18.1.3", "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "engines": {"node": ">=6"}}, "node_modules/yargs/node_modules/y18n": {"version": "4.0.3", "license": "ISC"}, "node_modules/yazl": {"version": "2.5.1", "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3"}}, "node_modules/yeast": {"version": "0.1.2", "license": "MIT"}}}