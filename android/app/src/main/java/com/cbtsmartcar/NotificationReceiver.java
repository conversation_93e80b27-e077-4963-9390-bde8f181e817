package com.cbtsmartcar;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class NotificationReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null) {
            String action = intent.getAction();
            if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
                Log.d("NotificationReceiver", "Boot completed, starting notification service");
            } else if ("com.cbtsmartcar.SHOW_NOTIFICATION".equals(action)) {
                String title = intent.getStringExtra("title");
                if (title == null) title = "Alert";

                String message = intent.getStringExtra("message");
                if (message == null) message = "You have a new notification";

                Intent serviceIntent = new Intent(context, NotificationService.class);
                serviceIntent.putExtra("title", title);
                serviceIntent.putExtra("message", message);

                context.startService(serviceIntent);
            }
        }
    }
}
