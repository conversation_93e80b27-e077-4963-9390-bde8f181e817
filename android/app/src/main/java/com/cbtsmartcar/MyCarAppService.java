package com.cbtsmartcar;

import android.content.Intent;
import android.os.Handler;
import android.os.Looper;

import androidx.car.app.CarAppService;
import androidx.car.app.CarContext;
import androidx.car.app.Screen;
import androidx.car.app.Session;
import androidx.car.app.validation.HostValidator;
import androidx.car.app.model.ListTemplate;
import androidx.car.app.model.ItemList;
import androidx.car.app.model.Row;
import androidx.car.app.model.Template;
import androidx.car.app.model.MessageTemplate;
import androidx.car.app.model.Action;
import androidx.car.app.model.CarColor;

public class MyCarAppService extends CarAppService {

    @Override
    public HostValidator createHostValidator() {
        return HostValidator.ALLOW_ALL_HOSTS_VALIDATOR;
    }

    @Override
    public Session onCreateSession() {
        return new CarPlaySession();
    }

    private static class CarPlaySession extends Session {
        CarPlaySession() {}

        @Override
        public Screen onCreateScreen(Intent intent) {
            return new CarPlayScreen(getCarContext());
        }
    }

    private static class CarPlayScreen extends Screen implements BLEDataStore.BLEDataChangeListener {

        private final BLEDataStore store;
        private final Handler handler = new Handler(Looper.getMainLooper());
       private final Runnable resetActive;

        CarPlayScreen(CarContext carContext) {
            super(carContext);
            store = BLEDataStore.getInstance();
            if (store != null) store.addListener(this);
         resetActive = () -> {
            if (store != null) store.setActive(false);
          };
        }

        @Override
        public Template onGetTemplate() {
            if (store != null) {
                // App is visible
                store.setActive(true);

                // Reset previous timeout and start new
                handler.removeCallbacks(resetActive);
                handler.postDelayed(resetActive, 2000);
            }

            if (store == null) {
                ItemList.Builder errorList = new ItemList.Builder();
                errorList.addItem(new Row.Builder()
                        .setTitle("Error")
                        .addText("BLE Data not available")
                        .build());
                return new ListTemplate.Builder()
                        .setSingleList(errorList.build())
                        .setTitle("Error")
                        .build();
            }

            // Alert modal
            if (store.isAlertActive()) {
                return new MessageTemplate.Builder(store.getAlertMessage())
                        .setTitle(store.getAlertTitle())
                        .addAction(
                                new Action.Builder()
                                        .setTitle("OK")
                                        .setOnClickListener(() -> {
                                            store.dismissAlert();
                                            invalidate();
                                        })
                                        .setBackgroundColor(CarColor.createCustom(0xFF63D8FE, 0xFF63D8FE))
                                        .build()
                        )
                        .setHeaderAction(Action.BACK)
                        .build();
            }

            // Normal BLE data list
            ItemList.Builder itemListBuilder = new ItemList.Builder();
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Welcome")
                    .addText(store.getChildName() != null ? store.getChildName() : "Guest")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Standing Leg")
                    .addText(store.getS6() == 1 ? "✅ Standing leg" : "❌ Standing leg")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("ISO Fix")
                    .addText(store.getS7() == 0 && store.getS8() == 0 ? "✅ ISO fix" : "❌ ISO fix")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Harness Buckled")
                    .addText("✅ Harness buckled")
                    .build());
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Orientation")
                    .addText(store.getS10() == 1 ? "✅ Orientation" : "❌ Orientation")
                    .build());

            return new ListTemplate.Builder()
                    .setSingleList(itemListBuilder.build())
                    .setTitle("SMART 360 IQ" + (store.getChildName() != null ? " - " + store.getChildName() : ""))
                    .build();
        }

        @Override
        public void onDataChanged() {
            invalidate();
        }
    }
}
