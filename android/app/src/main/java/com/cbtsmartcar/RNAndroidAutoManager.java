package com.cbtsmartcar;

import android.util.Log;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;

import javax.annotation.Nonnull;

public class RNAndroidAutoManager extends ReactContextBaseJavaModule {

    private static final String TAG = "RNAndroidAutoManager";

    public RNAndroidAutoManager(@Nonnull ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Nonnull
    @Override
    public String getName() {
        return "RNAndroidAutoManager";
    }

    // Update BLE + alert data
    @ReactMethod
    public void updateBLEData(
            int s6, int s7, int s8, int s10, String childName,
            boolean alertActive, String alertTitle, String alertMessage
    ) {
        try {
            BLEDataStore store = BLEDataStore.getInstance();
            if (store != null) {
                 getReactApplicationContext().runOnUiQueueThread(() -> {
                    store.setData(s6, s7, s8, s10, childName, alertActive, alertTitle, alertMessage);
                    Log.d(TAG, "BLEData updated: alertActive=" + alertActive);
                });
            } else {
                Log.e(TAG, "BLEDataStore instance is null!");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating BLEData: ", e);
        }
    }

    // Get current Android Auto active state
    @ReactMethod
    public void isAppActive(Promise promise) {
        BLEDataStore store = BLEDataStore.getInstance();
        if (store != null) {
            promise.resolve(store.isActive());
        } else {
            promise.resolve(false);
        }
    }
}
