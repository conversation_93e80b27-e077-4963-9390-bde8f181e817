<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.cbtsmartcar"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" android:required="false" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:required="false" />
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true"/>
    <!-- Android Auto permissions (match Google car app library namespace) -->
    <uses-permission android:name="com.google.android.libraries.car.app.ACCESS_SURFACE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.google.android.car.permission.CAR_PASSENGER" />


    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/app_logo"
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/app_logo"
        tools:targetApi="28"
        android:theme="@style/AppTheme">

        <!-- Notifee receivers -->
        <receiver
            android:name="app.notifee.core.NotificationAlarmReceiver"
            android:enabled="true"
            android:exported="true"
            tools:node="merge"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="app.notifee.core.RebootBroadcastReceiver"
            android:enabled="true"
            android:exported="true"
            tools:node="merge"
            tools:replace="android:exported">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
            <!-- Firebase (possible if using FCM) -->
        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver>

        <!-- Firebase notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            tools:replace="android:value"
            android:value="default-channel-id" />
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- React Native Push Notifications -->
        <meta-data
            android:name="com.dieam.reactnativepushnotification.notification_foreground"
            android:value="false" />
        <meta-data
            android:name="com.dieam.reactnativepushnotification.notification_color"
            android:resource="@color/white" />

        <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions" />
        <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
        <receiver
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

         <!-- Android Auto Service (use library service for React integration) -->
        <service
            android:name=".MyCarAppService"
            android:exported="true">
            <intent-filter>
                <action android:name="androidx.car.app.CarAppService" />
                <category android:name="androidx.car.app.category.MESSAGING" />
            </intent-filter>
        </service>

        <!-- Meta-data for Android Auto (must be at application level) -->
        <meta-data
            android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc" />

        <!-- Required: Minimum Car App API Level (Google Car App namespace) -->
        <meta-data
            android:name="androidx.car.app.minCarApiLevel"
            android:value="1" />

        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
    </application>
</manifest>
