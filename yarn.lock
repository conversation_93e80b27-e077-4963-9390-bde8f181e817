# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.20.5", "@babel/compat-data@^7.27.2", "@babel/compat-data@^7.27.7", "@babel/compat-data@^7.28.0":
  version "7.28.4"

"@babel/core@^7.0.0", "@babel/core@^7.1.0", "@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.13.16", "@babel/core@^7.13.8", "@babel/core@^7.14.0", "@babel/core@^7.20.0", "@babel/core@^7.25.2", "@babel/core@^7.4.5", "@babel/core@^7.7.5":
  version "7.28.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.28.3"
    "@babel/helpers" "^7.28.4"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.4"
    "@babel/types" "^7.28.4"
    "@jridgewell/remapping" "^2.3.5"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.14.0", "@babel/generator@^7.20.0", "@babel/generator@^7.28.3":
  version "7.28.3"
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1", "@babel/helper-annotate-as-pure@^7.27.3":
  version "7.27.3"
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.27.1", "@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.27.1", "@babel/helper-create-class-features-plugin@^7.28.3":
  version "7.28.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.28.3"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.5":
  version "0.6.5"
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    debug "^4.4.1"
    lodash.debounce "^4.0.8"
    resolve "^1.22.10"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1", "@babel/helper-module-transforms@^7.28.3":
  version "7.28.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"

"@babel/helper-remap-async-to-generator@^7.18.9", "@babel/helper-remap-async-to-generator@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0", "@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"

"@babel/helper-validator-identifier@^7.25.9", "@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"

"@babel/helper-wrap-function@^7.27.1":
  version "7.28.3"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.3"
    "@babel/types" "^7.28.2"

"@babel/helpers@^7.28.4":
  version "7.28.4"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"

"@babel/highlight@^7.10.4":
  version "7.25.9"
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.13.16", "@babel/parser@^7.14.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.0", "@babel/parser@^7.20.7", "@babel/parser@^7.27.2", "@babel/parser@^7.28.3", "@babel/parser@^7.28.4", "@babel/parser@^7.7.0":
  version "7.28.4"
  dependencies:
    "@babel/types" "^7.28.4"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.28.3":
  version "7.28.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/plugin-proposal-async-generator-functions@^7.0.0":
  version "7.20.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-remap-async-to-generator" "^7.18.9"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.0.0", "@babel/plugin-proposal-class-properties@^7.13.0", "@babel/plugin-proposal-class-properties@^7.18.0":
  version "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.13.8", "@babel/plugin-proposal-nullish-coalescing-operator@^7.18.0":
  version "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.0.0":
  version "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.0.0", "@babel/plugin-proposal-object-rest-spread@^7.20.0":
  version "7.20.7"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  version "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.0.0", "@babel/plugin-proposal-optional-chaining@^7.13.12", "@babel/plugin-proposal-optional-chaining@^7.20.0":
  version "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0", "@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.0.0", "@babel/plugin-syntax-dynamic-import@^7.8.0":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.12.1", "@babel/plugin-syntax-flow@^7.18.0", "@babel/plugin-syntax-flow@^7.2.0", "@babel/plugin-syntax-flow@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-assertions@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.24.7", "@babel/plugin-syntax-import-attributes@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.0.0", "@babel/plugin-transform-arrow-functions@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-generator-functions@^7.28.0":
  version "7.28.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-async-to-generator@^7.0.0", "@babel/plugin-transform-async-to-generator@^7.20.0", "@babel/plugin-transform-async-to-generator@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"

"@babel/plugin-transform-block-scoped-functions@^7.0.0", "@babel/plugin-transform-block-scoped-functions@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.0.0", "@babel/plugin-transform-block-scoping@^7.28.0":
  version "7.28.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-properties@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-static-block@^7.28.3":
  version "7.28.3"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.28.3"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.0.0", "@babel/plugin-transform-classes@^7.28.3":
  version "7.28.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-globals" "^7.28.0"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.28.4"

"@babel/plugin-transform-computed-properties@^7.0.0", "@babel/plugin-transform-computed-properties@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.0.0", "@babel/plugin-transform-destructuring@^7.20.0", "@babel/plugin-transform-destructuring@^7.28.0":
  version "7.28.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-dotall-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dynamic-import@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-explicit-resource-management@^7.28.0":
  version "7.28.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"

"@babel/plugin-transform-exponentiation-operator@^7.0.0", "@babel/plugin-transform-exponentiation-operator@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-export-namespace-from@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-flow-strip-types@^7.0.0", "@babel/plugin-transform-flow-strip-types@^7.20.0", "@babel/plugin-transform-flow-strip-types@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-flow" "^7.27.1"

"@babel/plugin-transform-for-of@^7.0.0", "@babel/plugin-transform-for-of@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.0.0", "@babel/plugin-transform-function-name@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-json-strings@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-literals@^7.0.0", "@babel/plugin-transform-literals@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-logical-assignment-operators@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.0.0", "@babel/plugin-transform-member-expression-literals@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.13.8", "@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.0.0", "@babel/plugin-transform-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-nullish-coalescing-operator@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-numeric-separator@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-assign@^7.0.0", "@babel/plugin-transform-object-assign@^7.16.7":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-rest-spread@^7.28.0":
  version "7.28.4"
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/traverse" "^7.28.4"

"@babel/plugin-transform-object-super@^7.0.0", "@babel/plugin-transform-object-super@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-catch-binding@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.20.7", "@babel/plugin-transform-parameters@^7.27.7":
  version "7.27.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-methods@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-property-in-object@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.0.0", "@babel/plugin-transform-property-literals@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-display-name@^7.0.0", "@babel/plugin-transform-react-display-name@^7.27.1":
  version "7.28.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-development@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.0.0", "@babel/plugin-transform-react-jsx@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/plugin-transform-react-pure-annotations@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.0.0", "@babel/plugin-transform-regenerator@^7.28.3":
  version "7.28.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regexp-modifiers@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-runtime@^7.0.0":
  version "7.28.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.0.0", "@babel/plugin-transform-shorthand-properties@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.0.0", "@babel/plugin-transform-spread@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.0.0", "@babel/plugin-transform-sticky-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.0.0", "@babel/plugin-transform-template-literals@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typescript@^7.27.1", "@babel/plugin-transform-typescript@^7.5.0":
  version "7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-property-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.0.0", "@babel/plugin-transform-unicode-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-sets-regex@^7.27.1":
  version "7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/preset-env@^7.25.0":
  version "7.28.3"
  dependencies:
    "@babel/compat-data" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.28.3"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.27.1"
    "@babel/plugin-syntax-import-attributes" "^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.27.1"
    "@babel/plugin-transform-async-generator-functions" "^7.28.0"
    "@babel/plugin-transform-async-to-generator" "^7.27.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.27.1"
    "@babel/plugin-transform-block-scoping" "^7.28.0"
    "@babel/plugin-transform-class-properties" "^7.27.1"
    "@babel/plugin-transform-class-static-block" "^7.28.3"
    "@babel/plugin-transform-classes" "^7.28.3"
    "@babel/plugin-transform-computed-properties" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-dotall-regex" "^7.27.1"
    "@babel/plugin-transform-duplicate-keys" "^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-dynamic-import" "^7.27.1"
    "@babel/plugin-transform-explicit-resource-management" "^7.28.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.27.1"
    "@babel/plugin-transform-export-namespace-from" "^7.27.1"
    "@babel/plugin-transform-for-of" "^7.27.1"
    "@babel/plugin-transform-function-name" "^7.27.1"
    "@babel/plugin-transform-json-strings" "^7.27.1"
    "@babel/plugin-transform-literals" "^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.27.1"
    "@babel/plugin-transform-member-expression-literals" "^7.27.1"
    "@babel/plugin-transform-modules-amd" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-modules-systemjs" "^7.27.1"
    "@babel/plugin-transform-modules-umd" "^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-new-target" "^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.27.1"
    "@babel/plugin-transform-numeric-separator" "^7.27.1"
    "@babel/plugin-transform-object-rest-spread" "^7.28.0"
    "@babel/plugin-transform-object-super" "^7.27.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/plugin-transform-private-methods" "^7.27.1"
    "@babel/plugin-transform-private-property-in-object" "^7.27.1"
    "@babel/plugin-transform-property-literals" "^7.27.1"
    "@babel/plugin-transform-regenerator" "^7.28.3"
    "@babel/plugin-transform-regexp-modifiers" "^7.27.1"
    "@babel/plugin-transform-reserved-words" "^7.27.1"
    "@babel/plugin-transform-shorthand-properties" "^7.27.1"
    "@babel/plugin-transform-spread" "^7.27.1"
    "@babel/plugin-transform-sticky-regex" "^7.27.1"
    "@babel/plugin-transform-template-literals" "^7.27.1"
    "@babel/plugin-transform-typeof-symbol" "^7.27.1"
    "@babel/plugin-transform-unicode-escapes" "^7.27.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.27.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    core-js-compat "^3.43.0"
    semver "^6.3.1"

"@babel/preset-flow@^7.13.13":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-flow-strip-types" "^7.27.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.24.7":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-react-display-name" "^7.27.1"
    "@babel/plugin-transform-react-jsx" "^7.27.1"
    "@babel/plugin-transform-react-jsx-development" "^7.27.1"
    "@babel/plugin-transform-react-pure-annotations" "^7.27.1"

"@babel/preset-typescript@^7.13.0", "@babel/preset-typescript@^7.16.7":
  version "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-typescript" "^7.27.1"

"@babel/register@^7.13.16":
  version "7.28.3"
  dependencies:
    clone-deep "^4.0.1"
    find-cache-dir "^2.0.0"
    make-dir "^2.1.0"
    pirates "^4.0.6"
    source-map-support "^0.5.16"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.13.8", "@babel/runtime@^7.15.4", "@babel/runtime@^7.9.2":
  version "7.28.4"

"@babel/template@^7.0.0", "@babel/template@^7.27.1", "@babel/template@^7.27.2", "@babel/template@^7.3.3":
  version "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.14.0", "@babel/traverse@^7.20.0", "@babel/traverse@^7.27.1", "@babel/traverse@^7.28.0", "@babel/traverse@^7.28.3", "@babel/traverse@^7.28.4", "@babel/traverse@^7.7.0", "@babel/traverse@^7.7.4":
  version "7.28.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.0", "@babel/types@^7.20.7", "@babel/types@^7.24.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.28.2", "@babel/types@^7.28.4", "@babel/types@^7.3.3", "@babel/types@^7.4.4", "@babel/types@^7.7.0":
  version "7.28.4"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"

"@bugsnag/core@^7.25.0":
  version "7.25.0"
  dependencies:
    "@bugsnag/cuid" "^3.0.0"
    "@bugsnag/safe-json-stringify" "^6.0.0"
    error-stack-parser "^2.0.3"
    iserror "0.0.2"
    stack-generator "^2.0.3"

"@bugsnag/cuid@^3.0.0":
  version "3.2.1"

"@bugsnag/delivery-react-native@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-console-breadcrumbs@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-network-breadcrumbs@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-client-sync@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-event-sync@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-global-error-handler@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-hermes@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-session@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react-native-unhandled-rejection@^7.25.0":
  version "7.25.0"

"@bugsnag/plugin-react@^7.25.0":
  version "7.25.0"

"@bugsnag/react-native@^7.10.5":
  version "7.25.1"
  dependencies:
    "@bugsnag/core" "^7.25.0"
    "@bugsnag/delivery-react-native" "^7.25.0"
    "@bugsnag/plugin-console-breadcrumbs" "^7.25.0"
    "@bugsnag/plugin-network-breadcrumbs" "^7.25.0"
    "@bugsnag/plugin-react" "^7.25.0"
    "@bugsnag/plugin-react-native-client-sync" "^7.25.0"
    "@bugsnag/plugin-react-native-event-sync" "^7.25.0"
    "@bugsnag/plugin-react-native-global-error-handler" "^7.25.0"
    "@bugsnag/plugin-react-native-hermes" "^7.25.0"
    "@bugsnag/plugin-react-native-session" "^7.25.0"
    "@bugsnag/plugin-react-native-unhandled-rejection" "^7.25.0"
    iserror "^0.0.2"

"@bugsnag/safe-json-stringify@^6.0.0":
  version "6.1.0"

"@callstack/react-theme-provider@3.0.3":
  version "3.0.3"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.1"
    deepmerge "^3.2.0"
    hoist-non-react-statics "^3.3.0"

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@egjs/hammerjs@^2.0.17":
  version "2.0.17"
  dependencies:
    "@types/hammerjs" "^2.0.36"

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/hoek@^9.0.0", "@hapi/hoek@^9.3.0":
  version "9.3.0"

"@hapi/topo@^5.1.0":
  version "5.1.0"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"

"@inquirer/external-editor@^1.0.0":
  version "1.0.2"
  dependencies:
    chardet "^2.1.0"
    iconv-lite "^0.7.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"

"@jest/console@^26.6.2":
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^26.6.2"
    jest-util "^26.6.2"
    slash "^3.0.0"

"@jest/core@^26.6.3":
  version "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-changed-files "^26.6.2"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-resolve-dependencies "^26.6.3"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    jest-watcher "^26.6.2"
    micromatch "^4.0.2"
    p-each-series "^2.1.0"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/create-cache-key-function@^27.0.1":
  version "27.5.1"
  dependencies:
    "@jest/types" "^27.5.1"

"@jest/environment@^26.6.2":
  version "26.6.2"
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"

"@jest/fake-timers@^26.6.2":
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

"@jest/globals@^26.6.2":
  version "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    expect "^26.6.2"

"@jest/reporters@^26.6.2":
  version "26.6.2"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.4"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.3"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.2"
    jest-haste-map "^26.6.2"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^7.0.0"
  optionalDependencies:
    node-notifier "^8.0.0"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^26.6.2":
  version "26.6.2"
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.4"
    source-map "^0.6.0"

"@jest/test-result@^26.6.2":
  version "26.6.2"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  version "26.6.3"
  dependencies:
    "@jest/test-result" "^26.6.2"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"

"@jest/transform@^26.6.2":
  version "26.6.2"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    babel-plugin-istanbul "^6.0.0"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-util "^26.6.2"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^26.6.2":
  version "26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"

"@jest/types@^27.5.1":
  version "27.5.1"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"

"@jest/types@^29.6.3":
  version "29.6.3"
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.13"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/remapping@^2.3.5":
  version "2.3.5"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/source-map@^0.3.3":
  version "0.3.11"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.5"

"@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.31"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@noble/hashes@^1.1.5":
  version "1.8.0"

"@notifee/react-native@7.9.0":
  version "7.9.0"

"@paralleldrive/cuid2@^2.2.2":
  version "2.2.2"
  dependencies:
    "@noble/hashes" "^1.1.5"

"@react-native-async-storage/async-storage@^1.14.1":
  version "1.24.0"
  dependencies:
    merge-options "^3.0.4"

"@react-native-community/art@^1.1.2":
  version "1.1.2"
  dependencies:
    art "^0.10.3"
    invariant "^2.2.4"
    prop-types "^15.7.2"

"@react-native-community/async-storage@^1.6.1":
  version "1.12.1"
  dependencies:
    deep-assign "^3.0.0"

"@react-native-community/blur@^4.3.2":
  version "4.4.1"

"@react-native-community/cli-debugger-ui@^7.0.3":
  version "7.0.3"
  dependencies:
    serve-static "^1.13.1"

"@react-native-community/cli-hermes@^6.3.1":
  version "6.3.1"
  dependencies:
    "@react-native-community/cli-platform-android" "^6.3.1"
    "@react-native-community/cli-tools" "^6.2.1"
    chalk "^4.1.2"
    hermes-profile-transformer "^0.0.6"
    ip "^1.1.5"

"@react-native-community/cli-platform-android@^6.3.1":
  version "6.3.1"
  dependencies:
    "@react-native-community/cli-tools" "^6.2.1"
    chalk "^4.1.2"
    execa "^1.0.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    jetifier "^1.6.2"
    lodash "^4.17.15"
    logkitty "^0.7.1"
    slash "^3.0.0"
    xmldoc "^1.1.2"

"@react-native-community/cli-platform-android@^7.0.1":
  version "7.0.1"
  dependencies:
    "@react-native-community/cli-tools" "^7.0.1"
    chalk "^4.1.2"
    execa "^1.0.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    jetifier "^1.6.2"
    lodash "^4.17.15"
    logkitty "^0.7.1"
    slash "^3.0.0"
    xmldoc "^1.1.2"

"@react-native-community/cli-platform-ios@^7.0.1":
  version "7.0.1"
  dependencies:
    "@react-native-community/cli-tools" "^7.0.1"
    chalk "^4.1.2"
    execa "^1.0.0"
    glob "^7.1.3"
    js-yaml "^3.13.1"
    lodash "^4.17.15"
    ora "^5.4.1"
    plist "^3.0.2"
    xcode "^3.0.0"

"@react-native-community/cli-plugin-metro@^7.0.4":
  version "7.0.4"
  dependencies:
    "@react-native-community/cli-server-api" "^7.0.4"
    "@react-native-community/cli-tools" "^6.2.1"
    chalk "^4.1.2"
    metro "^0.67.0"
    metro-config "^0.67.0"
    metro-core "^0.67.0"
    metro-react-native-babel-transformer "^0.67.0"
    metro-resolver "^0.67.0"
    metro-runtime "^0.67.0"
    readline "^1.3.0"

"@react-native-community/cli-server-api@^7.0.4":
  version "7.0.4"
  dependencies:
    "@react-native-community/cli-debugger-ui" "^7.0.3"
    "@react-native-community/cli-tools" "^6.2.1"
    compression "^1.7.1"
    connect "^3.6.5"
    errorhandler "^1.5.0"
    nocache "^2.1.0"
    pretty-format "^26.6.2"
    serve-static "^1.13.1"
    ws "^7.5.1"

"@react-native-community/cli-tools@^6.2.1":
  version "6.2.1"
  dependencies:
    appdirsjs "^1.2.4"
    chalk "^4.1.2"
    lodash "^4.17.15"
    mime "^2.4.1"
    node-fetch "^2.6.0"
    open "^6.2.0"
    semver "^6.3.0"
    shell-quote "^1.7.3"

"@react-native-community/cli-tools@^7.0.1":
  version "7.0.1"
  dependencies:
    appdirsjs "^1.2.4"
    chalk "^4.1.2"
    lodash "^4.17.15"
    mime "^2.4.1"
    node-fetch "^2.6.0"
    open "^6.2.0"
    ora "^5.4.1"
    semver "^6.3.0"
    shell-quote "^1.7.3"

"@react-native-community/cli-types@^6.0.0":
  version "6.0.0"
  dependencies:
    ora "^3.4.0"

"@react-native-community/cli@^7.0.3":
  version "7.0.4"
  dependencies:
    "@react-native-community/cli-debugger-ui" "^7.0.3"
    "@react-native-community/cli-hermes" "^6.3.1"
    "@react-native-community/cli-plugin-metro" "^7.0.4"
    "@react-native-community/cli-server-api" "^7.0.4"
    "@react-native-community/cli-tools" "^6.2.1"
    "@react-native-community/cli-types" "^6.0.0"
    appdirsjs "^1.2.4"
    chalk "^4.1.2"
    command-exists "^1.2.8"
    commander "^2.19.0"
    cosmiconfig "^5.1.0"
    deepmerge "^3.2.0"
    envinfo "^7.7.2"
    execa "^1.0.0"
    find-up "^4.1.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    graceful-fs "^4.1.3"
    joi "^17.2.1"
    leven "^3.1.0"
    lodash "^4.17.15"
    minimist "^1.2.0"
    node-stream-zip "^1.9.1"
    ora "^3.4.0"
    pretty-format "^26.6.2"
    prompts "^2.4.0"
    semver "^6.3.0"
    serve-static "^1.13.1"
    strip-ansi "^5.2.0"
    sudo-prompt "^9.0.0"
    wcwidth "^1.0.1"

"@react-native-community/clipboard@^1.2.2", "@react-native-community/clipboard@^1.5.1":
  version "1.5.1"

"@react-native-community/datetimepicker@^3.4.1":
  version "3.5.2"
  dependencies:
    invariant "^2.2.4"

"@react-native-community/eslint-config@^2.0.0":
  version "2.0.0"
  dependencies:
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^3.1.0"
    "@typescript-eslint/parser" "^3.1.0"
    babel-eslint "^10.1.0"
    eslint-config-prettier "^6.10.1"
    eslint-plugin-eslint-comments "^3.1.2"
    eslint-plugin-flowtype "2.50.3"
    eslint-plugin-jest "22.4.1"
    eslint-plugin-prettier "3.1.2"
    eslint-plugin-react "^7.20.0"
    eslint-plugin-react-hooks "^4.0.4"
    eslint-plugin-react-native "^3.8.1"
    prettier "^2.0.2"

"@react-native-community/eslint-plugin@^1.1.0":
  version "1.3.0"

"@react-native-community/masked-view@^0.1.10":
  version "0.1.11"

"@react-native-community/netinfo@^6.0.0":
  version "6.2.1"

"@react-native-community/push-notification-ios@^1.8.0":
  version "1.11.0"
  dependencies:
    invariant "^2.2.4"

"@react-native-firebase/app@^18.1.0":
  version "18.9.0"
  dependencies:
    opencollective-postinstall "^2.0.3"
    superstruct "^0.6.2"

"@react-native-firebase/messaging@^18.1.0":
  version "18.9.0"

"@react-native-picker/picker@2.4.8":
  version "2.4.8"

"@react-native/assets@1.0.0":
  version "1.0.0"

"@react-native/normalize-color@*":
  version "2.1.0"

"@react-native/normalize-color@2.0.0":
  version "2.0.0"

"@react-native/polyfills@2.0.0":
  version "2.0.0"

"@react-navigation/bottom-tabs@^5.11.8":
  version "5.11.15"
  dependencies:
    color "^3.1.3"
    react-native-iphone-x-helper "^1.3.0"

"@react-navigation/core@^5.16.1":
  version "5.16.1"
  dependencies:
    "@react-navigation/routers" "^5.7.4"
    escape-string-regexp "^4.0.0"
    nanoid "^3.1.15"
    query-string "^6.13.6"
    react-is "^16.13.0"

"@react-navigation/drawer@^5.12.4":
  version "5.12.9"
  dependencies:
    color "^3.1.3"
    react-native-iphone-x-helper "^1.3.0"

"@react-navigation/native@^5.9.3":
  version "5.9.8"
  dependencies:
    "@react-navigation/core" "^5.16.1"
    escape-string-regexp "^4.0.0"
    nanoid "^3.1.15"

"@react-navigation/routers@^5.7.4":
  version "5.7.4"
  dependencies:
    nanoid "^3.1.15"

"@react-navigation/stack@^5.14.3":
  version "5.14.9"
  dependencies:
    color "^3.1.3"
    react-native-iphone-x-helper "^1.3.0"

"@rtsao/scc@^1.1.0":
  version "1.1.0"

"@sideway/address@^4.1.5":
  version "4.1.5"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  version "3.0.1"

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"

"@sinonjs/commons@^1.7.0":
  version "1.8.6"
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  version "6.0.1"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@svgr/babel-plugin-add-jsx-attribute@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-remove-jsx-attribute@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-remove-jsx-empty-expression@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-replace-jsx-attribute-value@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-svg-dynamic-title@^4.3.3":
  version "4.3.3"

"@svgr/babel-plugin-svg-em-dimensions@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-transform-react-native-svg@^4.2.0":
  version "4.2.0"

"@svgr/babel-plugin-transform-svg-component@^4.2.0":
  version "4.2.0"

"@svgr/babel-preset@^4.3.3":
  version "4.3.3"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^4.2.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^4.2.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^4.2.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^4.2.0"
    "@svgr/babel-plugin-svg-dynamic-title" "^4.3.3"
    "@svgr/babel-plugin-svg-em-dimensions" "^4.2.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^4.2.0"
    "@svgr/babel-plugin-transform-svg-component" "^4.2.0"

"@svgr/core@^4.3.3":
  version "4.3.3"
  dependencies:
    "@svgr/plugin-jsx" "^4.3.3"
    camelcase "^5.3.1"
    cosmiconfig "^5.2.1"

"@svgr/hast-util-to-babel-ast@^4.3.2":
  version "4.3.2"
  dependencies:
    "@babel/types" "^7.4.4"

"@svgr/plugin-jsx@^4.3.3":
  version "4.3.3"
  dependencies:
    "@babel/core" "^7.4.5"
    "@svgr/babel-preset" "^4.3.3"
    "@svgr/hast-util-to-babel-ast" "^4.3.2"
    svg-parser "^2.0.0"

"@svgr/plugin-svgo@^4.3.1":
  version "4.3.1"
  dependencies:
    cosmiconfig "^5.2.1"
    merge-deep "^3.0.2"
    svgo "^1.2.2"

"@tootallnate/once@1":
  version "1.1.2"

"@tootallnate/quickjs-emscripten@^0.23.0":
  version "0.23.0"

"@twotalltotems/react-native-otp-input@^1.3.11":
  version "1.3.11"
  dependencies:
    "@react-native-community/clipboard" "^1.2.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.14", "@types/babel__core@^7.1.7":
  version "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.28.0"
  dependencies:
    "@babel/types" "^7.28.2"

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"

"@types/graceful-fs@^4.1.2", "@types/graceful-fs@^4.1.3":
  version "4.1.9"
  dependencies:
    "@types/node" "*"

"@types/hammerjs@^2.0.36":
  version "2.0.46"

"@types/hoist-non-react-statics@^3.3.0", "@types/hoist-non-react-statics@^3.3.1":
  version "3.3.7"
  dependencies:
    hoist-non-react-statics "^3.3.0"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"

"@types/istanbul-lib-report@*":
  version "3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.3":
  version "7.0.15"

"@types/json5@^0.0.29":
  version "0.0.29"

"@types/node@*":
  version "24.7.0"
  dependencies:
    undici-types "~7.14.0"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"

"@types/prettier@^2.0.0":
  version "2.7.3"

"@types/q@^1.5.1":
  version "1.5.8"

"@types/react-redux@^7.1.20":
  version "7.1.34"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"
    redux "^4.0.0"

"@types/react@*":
  version "19.2.2"
  dependencies:
    csstype "^3.0.2"

"@types/stack-utils@^2.0.0":
  version "2.0.3"

"@types/yargs-parser@*":
  version "21.0.3"

"@types/yargs@^15.0.0":
  version "15.0.19"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^16.0.0":
  version "16.0.9"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^17.0.8":
  version "17.0.33"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^3.1.0":
  version "3.10.1"
  dependencies:
    "@typescript-eslint/experimental-utils" "3.10.1"
    debug "^4.1.1"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/experimental-utils@3.10.1":
  version "3.10.1"
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^3.1.0":
  version "3.10.1"
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "3.10.1"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/types@3.10.1":
  version "3.10.1"

"@typescript-eslint/typescript-estree@3.10.1":
  version "3.10.1"
  dependencies:
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/visitor-keys" "3.10.1"
    debug "^4.1.1"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@typescript-eslint/visitor-keys@3.10.1":
  version "3.10.1"
  dependencies:
    eslint-visitor-keys "^1.1.0"

"@xmldom/xmldom@^0.8.8":
  version "0.8.11"

"@yarnpkg/lockfile@^1.1.0":
  version "1.1.0"

abab@^2.0.3, abab@^2.0.5:
  version "2.0.6"

abort-controller@^3.0.0:
  version "3.0.0"
  dependencies:
    event-target-shim "^5.0.0"

absolute-path@^0.0.0:
  version "0.0.0"

accepts@^1.3.7, accepts@~1.3.7:
  version "1.3.8"
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-globals@^6.0.0:
  version "6.0.0"
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.3.1:
  version "5.3.2"

acorn-walk@^7.1.1:
  version "7.2.0"

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"

acorn@^8.15.0:
  version "8.15.0"

acorn@^8.2.4:
  version "8.15.0"

after@0.8.2:
  version "0.8.2"

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.4"

agent-base@6:
  version "6.0.2"
  dependencies:
    debug "4"

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.17.1"
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

anser@^1.4.9:
  version "1.4.10"

ansi-colors@^4.1.1:
  version "4.1.3"

ansi-escapes@^4.2.1:
  version "4.3.2"
  dependencies:
    type-fest "^0.21.3"

ansi-fragments@^0.2.1:
  version "0.2.1"
  dependencies:
    colorette "^1.0.7"
    slice-ansi "^2.0.0"
    strip-ansi "^5.0.0"

ansi-regex@^4.1.0:
  version "4.1.1"

ansi-regex@^5.0.0, ansi-regex@^5.0.1:
  version "5.0.1"

ansi-styles@^3.2.0:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^3.2.1:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"

anymatch@^2.0.0:
  version "2.0.0"
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.3:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

appcenter-file-upload-client@0.1.0:
  version "0.1.0"
  dependencies:
    detect-node "^2.0.4"
    superagent "5.1.0"
    url-parse "^1.4.7"

appdirsjs@^1.2.4:
  version "1.2.7"

argparse@^1.0.7:
  version "1.0.10"
  dependencies:
    sprintf-js "~1.0.2"

aria-query@^5.3.2:
  version "5.3.2"

arr-diff@^4.0.0:
  version "4.0.0"

arr-flatten@^1.1.0:
  version "1.1.0"

arr-union@^3.1.0:
  version "3.1.0"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8, array-includes@^3.1.9:
  version "3.1.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array-unique@^0.3.2:
  version "0.3.2"

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.6:
  version "1.2.6"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-shim-unscopables "^1.1.0"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.3:
  version "1.3.3"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.2, array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.reduce@^1.0.6:
  version "1.0.8"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-array-method-boxes-properly "^1.0.0"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    is-string "^1.1.1"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

arraybuffer.slice@~0.0.7:
  version "0.0.7"

art@^0.10.3:
  version "0.10.3"

asap@^2.0.0, asap@~2.0.3, asap@~2.0.6:
  version "2.0.6"

assign-symbols@^1.0.0:
  version "1.0.0"

ast-types-flow@^0.0.8:
  version "0.0.8"

ast-types@^0.13.4:
  version "0.13.4"
  dependencies:
    tslib "^2.0.1"

ast-types@0.14.2:
  version "0.14.2"
  dependencies:
    tslib "^2.0.1"

astral-regex@^1.0.0:
  version "1.0.0"

astral-regex@^2.0.0:
  version "2.0.0"

async-function@^1.0.0:
  version "1.0.0"

async-generator-function@^1.0.0:
  version "1.0.0"

async-limiter@~1.0.0:
  version "1.0.1"

async@^2.4.0:
  version "2.6.4"
  dependencies:
    lodash "^4.17.14"

async@^3.2.2:
  version "3.2.6"

asynckit@^0.4.0:
  version "0.4.0"

atob@^2.1.2:
  version "2.1.2"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  dependencies:
    possible-typed-array-names "^1.0.0"

axe-core@^4.10.0:
  version "4.10.3"

axobject-query@^4.1.0:
  version "4.1.0"

babel-core@^7.0.0-bridge.0:
  version "7.0.0-bridge.0"

babel-eslint@^10.1.0:
  version "10.1.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-jest@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    slash "^3.0.0"

babel-jest@^29.7.0:
  version "29.7.0"
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.0.0, babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^26.6.2:
  version "26.6.2"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-polyfill-corejs2@^0.4.14:
  version "0.4.14"
  dependencies:
    "@babel/compat-data" "^7.27.7"
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.13.0:
  version "0.13.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    core-js-compat "^3.43.0"

babel-plugin-polyfill-regenerator@^0.6.5:
  version "0.6.5"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"

babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0:
  version "7.0.0-beta.0"

babel-plugin-transform-flow-enums@^0.0.2:
  version "0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow" "^7.12.1"

babel-preset-current-node-syntax@^1.0.0:
  version "1.2.0"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-fbjs@^3.4.0:
  version "3.4.0"
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    babel-plugin-syntax-trailing-function-commas "^7.0.0-beta.0"

babel-preset-jest@^26.6.2:
  version "26.6.2"
  dependencies:
    babel-plugin-jest-hoist "^26.6.2"
    babel-preset-current-node-syntax "^1.0.0"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

backo2@1.0.2:
  version "1.0.2"

balanced-match@^1.0.0:
  version "1.0.2"

base-64@0.1.0:
  version "0.1.0"

base@^0.11.1:
  version "0.11.2"
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

base64-arraybuffer@0.1.4:
  version "0.1.4"

base64-js@^1.1.2, base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"

baseline-browser-mapping@^2.8.9:
  version "2.8.13"

basic-ftp@^5.0.2:
  version "5.0.5"

big-integer@1.6.x:
  version "1.6.52"

bl@^4.1.0:
  version "4.1.0"
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blob@0.0.5:
  version "0.0.5"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"

bplist-creator@0.1.1:
  version "0.1.1"
  dependencies:
    stream-buffers "2.2.x"

bplist-parser@0.3.2:
  version "0.3.2"
  dependencies:
    big-integer "1.6.x"

brace-expansion@^1.1.7:
  version "1.1.12"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.3:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"

browserslist@^4.24.0, browserslist@^4.25.3:
  version "4.26.3"
  dependencies:
    baseline-browser-mapping "^2.8.9"
    caniuse-lite "^1.0.30001746"
    electron-to-chromium "^1.5.227"
    node-releases "^2.0.21"
    update-browserslist-db "^1.1.3"

bser@2.1.1:
  version "2.1.1"
  dependencies:
    node-int64 "^0.4.0"

buffer-crc32@~0.2.3:
  version "0.2.13"

buffer-from@^1.0.0:
  version "1.1.2"

buffer@^5.5.0:
  version "5.7.1"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bytes@3.1.2:
  version "3.1.2"

cache-base@^1.0.1:
  version "1.0.1"
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

caller-callsite@^2.0.0:
  version "2.0.0"
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"

callsites@^3.0.0:
  version "3.1.0"

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"

camelcase@^6.0.0:
  version "6.3.0"

camelcase@^6.2.0:
  version "6.3.0"

caniuse-lite@^1.0.30001746:
  version "1.0.30001749"

capture-exit@^2.0.0:
  version "2.0.0"
  dependencies:
    rsvp "^4.8.4"

chalk@^2.0.1, chalk@^2.4.2:
  version "2.4.2"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.4.1:
  version "2.4.2"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"

character-entities-html4@^1.0.0:
  version "1.1.4"

character-entities-legacy@^1.0.0:
  version "1.1.4"

chardet@^2.1.0:
  version "2.1.0"

ci-info@^2.0.0:
  version "2.0.0"

ci-info@^3.2.0:
  version "3.9.0"

ci-info@^3.7.0:
  version "3.9.0"

cjs-module-lexer@^0.6.0:
  version "0.6.0"

class-utils@^0.3.5:
  version "0.3.6"
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.0.0, cli-spinners@^2.5.0:
  version "2.9.2"

cli-width@^3.0.0:
  version "3.0.0"

cliui@^6.0.0:
  version "6.0.0"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-deep@^0.2.4:
  version "0.2.4"
  dependencies:
    for-own "^0.1.3"
    is-plain-object "^2.0.1"
    kind-of "^3.0.2"
    lazy-cache "^1.0.3"
    shallow-clone "^0.1.2"

clone-deep@^2.0.1:
  version "2.0.2"
  dependencies:
    for-own "^1.0.0"
    is-plain-object "^2.0.4"
    kind-of "^6.0.0"
    shallow-clone "^1.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"

co@^4.6.0:
  version "4.6.0"

coa@^2.0.2:
  version "2.0.2"
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

code-push@^4.1.0:
  version "4.2.3"
  dependencies:
    appcenter-file-upload-client "0.1.0"
    proxy-agent "^6.3.0"
    recursive-fs "^2.1.0"
    slash "^3.0.0"
    superagent "^8.0.0"
    yazl "^2.5.1"

collect-v8-coverage@^1.0.0:
  version "1.0.2"

collection-visit@^1.0.0:
  version "1.0.0"
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"

color-name@1.1.3:
  version "1.1.3"

color-string@^1.6.0:
  version "1.9.1"
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.1.3:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colorette@^1.0.7:
  version "1.4.0"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

command-exists@^1.2.8:
  version "1.2.9"

commander@^2.19.0, commander@^2.20.0:
  version "2.20.3"

commander@~2.14.1:
  version "2.14.1"

commondir@^1.0.1:
  version "1.0.1"

component-bind@1.0.0:
  version "1.0.0"

component-emitter@^1.2.1, component-emitter@^1.3.0, component-emitter@~1.3.0:
  version "1.3.1"

component-inherit@0.0.3:
  version "0.0.3"

compressible@~2.0.18:
  version "2.0.18"
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.1:
  version "1.8.1"
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.1.0"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"

confusing-browser-globals@^1.0.10:
  version "1.0.11"

connect@^3.6.5:
  version "3.7.0"
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

convert-source-map@^1.4.0:
  version "1.9.0"

convert-source-map@^1.6.0:
  version "1.9.0"

convert-source-map@^2.0.0:
  version "2.0.0"

convert-string@^0.1.0:
  version "0.1.0"

cookiejar@^2.1.2, cookiejar@^2.1.4:
  version "2.1.4"

copy-descriptor@^0.1.0:
  version "0.1.1"

core-js-compat@^3.43.0:
  version "3.45.1"
  dependencies:
    browserslist "^4.25.3"

core-js@^1.0.0:
  version "1.2.7"

core-util-is@~1.0.0:
  version "1.0.3"

cosmiconfig@^5.0.5, cosmiconfig@^5.1.0, cosmiconfig@^5.2.1:
  version "5.2.1"
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cross-fetch@^3.1.5:
  version "3.2.0"
  dependencies:
    node-fetch "^2.7.0"

cross-spawn@^6.0.0:
  version "6.0.6"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.6"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-select-base-adapter@^0.1.1:
  version "0.1.1"

css-select@^2.0.0:
  version "2.1.0"
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^5.1.0:
  version "5.2.2"
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"

css-what@^6.1.0:
  version "6.2.2"

csso@^4.0.2:
  version "4.2.0"
  dependencies:
    css-tree "^1.1.2"

cssom@^0.4.4:
  version "0.4.4"

cssom@~0.3.6:
  version "0.3.8"

cssstyle@^2.3.0:
  version "2.3.0"
  dependencies:
    cssom "~0.3.6"

csstype@^3.0.2:
  version "3.1.3"

damerau-levenshtein@^1.0.8:
  version "1.0.8"

data-uri-to-buffer@^6.0.2:
  version "6.0.2"

data-urls@^2.0.0:
  version "2.0.0"
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

dayjs@^1.8.15:
  version "1.11.18"

debug@^2.2.0:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

debug@^2.3.3:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.4, debug@^4.4.1, debug@4:
  version "4.4.3"
  dependencies:
    ms "^2.1.3"

debug@~3.1.0:
  version "3.1.0"
  dependencies:
    ms "2.0.0"

debug@2.6.9:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

decamelize@^1.2.0:
  version "1.2.0"

decimal.js@^10.2.1:
  version "10.6.0"

decode-uri-component@^0.2.0:
  version "0.2.2"

deep-assign@^3.0.0:
  version "3.0.0"
  dependencies:
    is-obj "^1.0.0"

deep-is@^0.1.3:
  version "0.1.4"

deepmerge@^3.2.0:
  version "3.3.0"

deepmerge@^4.2.2:
  version "4.3.1"

defaults@^1.0.3:
  version "1.0.4"
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

define-property@^0.2.5:
  version "0.2.5"
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

degenerator@^5.0.0:
  version "5.0.1"
  dependencies:
    ast-types "^0.13.4"
    escodegen "^2.1.0"
    esprima "^4.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"

denodeify@^1.2.1:
  version "1.2.1"

depd@2.0.0:
  version "2.0.0"

deprecated-prop-type@^1.0.0:
  version "1.0.0"
  dependencies:
    warning "4.0.1"

deprecated-react-native-prop-types@^2.2.0, deprecated-react-native-prop-types@^2.3.0:
  version "2.3.0"
  dependencies:
    "@react-native/normalize-color" "*"
    invariant "*"
    prop-types "*"

destroy@1.2.0:
  version "1.2.0"

detect-newline@^3.0.0:
  version "3.1.0"

detect-node@^2.0.4:
  version "2.1.0"

dezalgo@^1.0.4:
  version "1.0.4"
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-sequences@^26.6.2:
  version "26.6.2"

doctrine@^2.1.0:
  version "2.1.0"
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  dependencies:
    esutils "^2.0.2"

dom-serializer@^1.0.1:
  version "1.4.1"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

dom-serializer@^2.0.0:
  version "2.0.0"
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom-serializer@0:
  version "0.2.2"
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0, domelementtype@^2.3.0:
  version "2.3.0"

domelementtype@1:
  version "1.3.1"

domexception@^2.0.1:
  version "2.0.1"
  dependencies:
    webidl-conversions "^5.0.0"

domhandler@^3.3.0:
  version "3.3.0"
  dependencies:
    domelementtype "^2.0.1"

domhandler@^4.2.0:
  version "4.3.1"
  dependencies:
    domelementtype "^2.2.0"

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  dependencies:
    domelementtype "^2.3.0"

domutils@^1.7.0:
  version "1.7.0"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.4.2:
  version "2.8.0"
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

domutils@^3.0.1:
  version "3.2.2"
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

ee-first@1.1.1:
  version "1.1.1"

electron-to-chromium@^1.5.227:
  version "1.5.233"

eme-encryption-scheme-polyfill@^2.0.1:
  version "2.2.3"

emittery@^0.7.1:
  version "0.7.2"

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

encodeurl@~1.0.2:
  version "1.0.2"

encodeurl@~2.0.0:
  version "2.0.0"

encoding@^0.1.11:
  version "0.1.13"
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.1.0:
  version "1.4.5"
  dependencies:
    once "^1.4.0"

engine.io-client@~3.5.0:
  version "3.5.4"
  dependencies:
    component-emitter "~1.3.0"
    component-inherit "0.0.3"
    debug "~3.1.0"
    engine.io-parser "~2.2.0"
    has-cors "1.1.0"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    ws "~7.5.10"
    xmlhttprequest-ssl "~1.6.2"
    yeast "0.1.2"

engine.io-parser@~2.2.0:
  version "2.2.1"
  dependencies:
    after "0.8.2"
    arraybuffer.slice "~0.0.7"
    base64-arraybuffer "0.1.4"
    blob "0.0.5"
    has-binary2 "~1.0.2"

enquirer@^2.3.5:
  version "2.4.1"
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^2.0.0:
  version "2.2.0"

entities@^4.2.0:
  version "4.5.0"

envinfo@^7.7.2:
  version "7.17.0"

error-ex@^1.3.1:
  version "1.3.4"
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.3, error-stack-parser@^2.0.6:
  version "2.1.4"
  dependencies:
    stackframe "^1.3.4"

errorhandler@^1.5.0:
  version "1.5.1"
  dependencies:
    accepts "~1.3.7"
    escape-html "~1.0.3"

es-abstract@^1.17.2, es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"

es-errors@^1.3.0:
  version "1.3.0"

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2, es-shim-unscopables@^1.1.0:
  version "1.1.0"
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"

escape-html@~1.0.3:
  version "1.0.3"

escape-string-regexp@^1.0.5:
  version "1.0.5"

escape-string-regexp@^2.0.0:
  version "2.0.0"

escape-string-regexp@^4.0.0:
  version "4.0.0"

escape-string-regexp@2.0.0:
  version "2.0.0"

escodegen@^2.0.0, escodegen@^2.1.0:
  version "2.1.0"
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-airbnb-base@^14.2.1:
  version "14.2.1"
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-airbnb@^18.2.0:
  version "18.2.1"
  dependencies:
    eslint-config-airbnb-base "^14.2.1"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-prettier@^6.10.1:
  version "6.15.0"
  dependencies:
    get-stdin "^6.0.0"

eslint-config-prettier@^8.1.0:
  version "8.10.2"

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.12.1:
  version "2.12.1"
  dependencies:
    debug "^3.2.7"

eslint-plugin-eslint-comments@^3.1.2:
  version "3.2.0"
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-flowtype@2.50.3:
  version "2.50.3"
  dependencies:
    lodash "^4.17.10"

eslint-plugin-import@^2.22.0:
  version "2.32.0"
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.9"
    array.prototype.findlastindex "^1.2.6"
    array.prototype.flat "^1.3.3"
    array.prototype.flatmap "^1.3.3"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.1"
    hasown "^2.0.2"
    is-core-module "^2.16.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.1"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.9"
    tsconfig-paths "^3.15.0"

eslint-plugin-jest@22.4.1:
  version "22.4.1"

eslint-plugin-jsx-a11y@^6.3.1:
  version "6.10.2"
  dependencies:
    aria-query "^5.3.2"
    array-includes "^3.1.8"
    array.prototype.flatmap "^1.3.2"
    ast-types-flow "^0.0.8"
    axe-core "^4.10.0"
    axobject-query "^4.1.0"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    hasown "^2.0.2"
    jsx-ast-utils "^3.3.5"
    language-tags "^1.0.9"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    safe-regex-test "^1.0.3"
    string.prototype.includes "^2.0.1"

eslint-plugin-prettier@3.1.2:
  version "3.1.2"
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.0.4:
  version "4.6.2"

eslint-plugin-react-native-globals@^0.1.1:
  version "0.1.2"

eslint-plugin-react-native@^3.8.1:
  version "3.11.0"
  dependencies:
    "@babel/traverse" "^7.7.4"
    eslint-plugin-react-native-globals "^0.1.1"

eslint-plugin-react@^7.20.0, eslint-plugin-react@^7.20.5:
  version "7.37.5"
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^5.0.0, eslint-scope@^5.1.1:
  version "5.1.1"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.0.0, eslint-utils@^2.1.0:
  version "2.1.0"
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"

eslint@^7.21.0:
  version "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0, esprima@^4.0.1, esprima@~4.0.0:
  version "4.0.1"

esquery@^1.4.0:
  version "1.6.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"

esutils@^2.0.2:
  version "2.0.3"

etag@~1.8.1:
  version "1.8.1"

event-target-shim@^5.0.0, event-target-shim@^5.0.1:
  version "5.0.1"

exec-sh@^0.3.2:
  version "0.3.6"

execa@^1.0.0:
  version "1.0.0"
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.0.0:
  version "4.1.0"
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"

expand-brackets@^2.1.4:
  version "2.1.4"
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-styles "^4.0.0"
    jest-get-type "^26.3.0"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0:
  version "3.0.2"
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend-shallow@^3.0.2:
  version "3.0.2"
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extglob@^2.0.4:
  version "2.0.4"
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"

fast-diff@^1.1.2:
  version "1.3.0"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"

fast-levenshtein@^2.0.6:
  version "2.0.6"

fast-safe-stringify@^2.0.6, fast-safe-stringify@^2.1.1:
  version "2.1.1"

fast-uri@^3.0.1:
  version "3.1.0"

fb-watchman@^2.0.0:
  version "2.0.2"
  dependencies:
    bser "2.1.1"

fbjs-css-vars@^1.0.0:
  version "1.0.2"

fbjs@^0.8.4, fbjs@^0.8.9:
  version "0.8.18"
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.30"

fbjs@^3.0.0:
  version "3.0.5"
  dependencies:
    cross-fetch "^3.1.5"
    fbjs-css-vars "^1.0.0"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^1.0.35"

figures@^3.0.0:
  version "3.2.0"
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  dependencies:
    flat-cache "^3.0.4"

fill-range@^4.0.0:
  version "4.0.0"
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"

finalhandler@1.1.2:
  version "1.1.2"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^2.0.0:
  version "2.1.0"
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^3.0.0:
  version "3.0.0"
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-yarn-workspace-root@^2.0.0:
  version "2.0.0"
  dependencies:
    micromatch "^4.0.2"

flat-cache@^3.0.4:
  version "3.2.0"
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"

flow-parser@^0.121.0:
  version "0.121.0"

flow-parser@0.*:
  version "0.287.0"

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  dependencies:
    is-callable "^1.2.7"

for-in@^0.1.3:
  version "0.1.8"

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"

for-own@^0.1.3:
  version "0.1.5"
  dependencies:
    for-in "^1.0.1"

for-own@^1.0.0:
  version "1.0.0"
  dependencies:
    for-in "^1.0.1"

form-data@^2.3.3:
  version "2.5.5"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.35"
    safe-buffer "^5.2.1"

form-data@^3.0.0:
  version "3.0.4"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.35"

form-data@^4.0.0:
  version "4.0.4"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

formidable@^1.2.1:
  version "1.2.6"

formidable@^2.1.2:
  version "2.1.5"
  dependencies:
    "@paralleldrive/cuid2" "^2.2.2"
    dezalgo "^1.0.4"
    once "^1.4.0"
    qs "^6.11.0"

fragment-cache@^0.2.1:
  version "0.2.1"
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"

fs-extra@^1.0.0:
  version "1.0.0"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^10.0.0:
  version "10.1.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"

fsevents@^2.1.2, fsevents@^2.3.2:
  version "2.3.3"

function-bind@^1.1.2:
  version "1.1.2"

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functional-red-black-tree@^1.0.1:
  version "1.0.1"

functions-have-names@^1.2.3:
  version "1.2.3"

fuse.js@3.4.5:
  version "3.4.5"

generator-function@^2.0.0:
  version "2.0.1"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.1"
  dependencies:
    async-function "^1.0.0"
    async-generator-function "^1.0.0"
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    generator-function "^2.0.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-package-type@^0.1.0:
  version "0.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"

get-stream@^4.0.0:
  version "4.1.0"
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-uri@^6.0.1:
  version "6.0.5"
  dependencies:
    basic-ftp "^5.0.2"
    data-uri-to-buffer "^6.0.2"
    debug "^4.3.4"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"

glob-parent@^5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6, glob@^7.1.7:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.0.6:
  version "7.0.6"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.6.0, globals@^13.9.0:
  version "13.24.0"
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.11"

growly@^1.3.0:
  version "1.3.0"

has-bigints@^1.0.2:
  version "1.1.0"

has-binary2@~1.0.2:
  version "1.0.3"
  dependencies:
    isarray "2.0.1"

has-cors@1.1.0:
  version "1.1.0"

has-flag@^3.0.0:
  version "3.0.0"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.1, has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"

has-tostringtag@^1.0.2:
  version "1.0.2"
  dependencies:
    has-symbols "^1.0.3"

has-value@^0.3.1:
  version "0.3.1"
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"

has-values@^1.0.0:
  version "1.0.0"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

hermes-engine@~0.11.0:
  version "0.11.0"

hermes-estree@0.12.0:
  version "0.12.0"

hermes-estree@0.5.0:
  version "0.5.0"

hermes-parser@0.12.0:
  version "0.12.0"
  dependencies:
    hermes-estree "0.12.0"

hermes-parser@0.5.0:
  version "0.5.0"
  dependencies:
    hermes-estree "0.5.0"

hermes-profile-transformer@^0.0.6:
  version "0.0.6"
  dependencies:
    source-map "^0.7.3"

hoist-non-react-statics@^3.0.1, hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  dependencies:
    react-is "^16.7.0"

hosted-git-info@^2.1.4:
  version "2.8.9"

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"

htmlparser2@5.0.1:
  version "5.0.1"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^3.3.0"
    domutils "^2.4.2"
    entities "^2.0.0"

http-errors@2.0.0:
  version "2.0.0"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^4.0.1:
  version "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

http-proxy-agent@^7.0.0, http-proxy-agent@^7.0.1:
  version "7.0.2"
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  dependencies:
    agent-base "6"
    debug "4"

https-proxy-agent@^7.0.6:
  version "7.0.6"
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"

i18n-js@^3.8.0:
  version "3.9.2"

iconv-lite@^0.6.2:
  version "0.6.3"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@^0.7.0:
  version "0.7.0"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@0.4.24:
  version "0.4.24"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13:
  version "1.2.1"

ignore@^4.0.6:
  version "4.0.6"

ignore@^5.0.5:
  version "5.3.2"

image-size@^0.6.0:
  version "0.6.3"

image-size@^1.0.2:
  version "1.2.1"
  dependencies:
    queue "6.0.2"

import-fresh@^2.0.0:
  version "2.0.0"
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.1"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.2.0"
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"

indexof@0.0.1:
  version "0.0.1"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"

inquirer@^8.1.5:
  version "8.2.7"
  dependencies:
    "@inquirer/external-editor" "^1.0.0"
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

internal-slot@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

invariant@*, invariant@^2.2.4, invariant@2.2.4:
  version "2.2.4"
  dependencies:
    loose-envify "^1.0.0"

ip-address@^10.0.1:
  version "10.0.1"

ip@^1.1.5:
  version "1.1.9"

is-accessor-descriptor@^1.0.1:
  version "1.0.1"
  dependencies:
    hasown "^2.0.0"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"

is-arrayish@^0.3.1:
  version "0.3.4"

is-async-function@^2.0.0:
  version "2.1.1"
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  dependencies:
    has-bigints "^1.0.2"

is-boolean-object@^1.2.1:
  version "1.2.2"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-buffer@^1.0.2, is-buffer@^1.1.5:
  version "1.1.6"

is-callable@^1.2.7:
  version "1.2.7"

is-ci@^2.0.0:
  version "2.0.0"
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.13.0, is-core-module@^2.16.0, is-core-module@^2.16.1:
  version "2.16.1"
  dependencies:
    hasown "^2.0.2"

is-data-descriptor@^1.0.1:
  version "1.0.1"
  dependencies:
    hasown "^2.0.0"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-descriptor@^0.1.0:
  version "0.1.7"
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.3"
  dependencies:
    is-accessor-descriptor "^1.0.1"
    is-data-descriptor "^1.0.1"

is-directory@^0.3.1:
  version "0.3.1"

is-docker@^2.0.0:
  version "2.2.1"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"

is-extendable@^1.0.1:
  version "1.0.1"
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-generator-fn@^2.0.0:
  version "2.1.0"

is-generator-function@^1.0.10:
  version "1.1.2"
  dependencies:
    call-bound "^1.0.4"
    generator-function "^2.0.0"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"

is-map@^2.0.3:
  version "2.0.3"

is-negative-zero@^2.0.3:
  version "2.0.3"

is-number-object@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^3.0.0:
  version "3.0.0"
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"

is-obj@^1.0.0:
  version "1.0.1"

is-plain-obj@^2.1.0:
  version "2.1.0"

is-plain-object@^2.0.1, is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"

is-regex@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  dependencies:
    call-bound "^1.0.3"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"

is-stream@^2.0.0:
  version "2.0.1"

is-string@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  dependencies:
    which-typed-array "^1.1.16"

is-typedarray@^1.0.0:
  version "1.0.0"

is-unicode-supported@^0.1.0:
  version "0.1.0"

is-weakmap@^2.0.2:
  version "2.0.2"

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-windows@^1.0.2:
  version "1.0.2"

is-wsl@^1.1.0:
  version "1.1.0"

is-wsl@^2.1.1, is-wsl@^2.2.0:
  version "2.2.0"
  dependencies:
    is-docker "^2.0.0"

isarray@^2.0.5:
  version "2.0.5"

isarray@~1.0.0:
  version "1.0.0"

isarray@1.0.0:
  version "1.0.0"

isarray@2.0.1:
  version "2.0.1"

iserror@^0.0.2, iserror@0.0.2:
  version "0.0.2"

isexe@^2.0.0:
  version "2.0.0"

isobject@^2.0.0:
  version "2.1.0"
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"

istanbul-lib-instrument@^4.0.3:
  version "4.0.3"
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.2:
  version "3.2.0"
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jest-changed-files@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    execa "^4.0.0"
    throat "^5.0.0"

jest-cli@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    import-local "^3.0.2"
    is-ci "^2.0.0"
    jest-config "^26.6.3"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    prompts "^2.0.1"
    yargs "^15.4.1"

jest-config@^26.6.3:
  version "26.6.3"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    babel-jest "^26.6.3"
    chalk "^4.0.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.4"
    jest-environment-jsdom "^26.6.2"
    jest-environment-node "^26.6.2"
    jest-get-type "^26.3.0"
    jest-jasmine2 "^26.6.3"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"

jest-diff@^26.6.2:
  version "26.6.2"
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-docblock@^26.0.0:
  version "26.0.0"
  dependencies:
    detect-newline "^3.0.0"

jest-each@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"

jest-environment-jsdom@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"
    jsdom "^16.4.0"

jest-environment-node@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

jest-get-type@^26.3.0:
  version "26.3.0"

jest-get-type@^29.6.3:
  version "29.6.3"

jest-haste-map@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^26.0.0"
    jest-serializer "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-haste-map@^27.3.1:
  version "27.5.1"
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^27.5.1"
    jest-serializer "^27.5.1"
    jest-util "^27.5.1"
    jest-worker "^27.5.1"
    micromatch "^4.0.4"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.3.2"

jest-haste-map@^29.7.0:
  version "29.7.0"
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-jasmine2@^26.6.3:
  version "26.6.3"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^26.6.2"
    is-generator-fn "^2.0.0"
    jest-each "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"
    throat "^5.0.0"

jest-leak-detector@^26.6.2:
  version "26.6.2"
  dependencies:
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-matcher-utils@^26.6.2:
  version "26.6.2"
  dependencies:
    chalk "^4.0.0"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-message-util@^26.6.2:
  version "26.6.2"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"
    slash "^3.0.0"
    stack-utils "^2.0.2"

jest-mock@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"

jest-regex-util@^26.0.0:
  version "26.0.0"

jest-regex-util@^27.0.6:
  version "27.5.1"

jest-regex-util@^27.5.1:
  version "27.5.1"

jest-regex-util@^29.6.3:
  version "29.6.3"

jest-resolve-dependencies@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/types" "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-snapshot "^26.6.2"

jest-resolve@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    jest-pnp-resolver "^1.2.2"
    jest-util "^26.6.2"
    read-pkg-up "^7.0.1"
    resolve "^1.18.1"
    slash "^3.0.0"

jest-runner@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.7.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-docblock "^26.0.0"
    jest-haste-map "^26.6.2"
    jest-leak-detector "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    jest-runtime "^26.6.3"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    source-map-support "^0.5.6"
    throat "^5.0.0"

jest-runtime@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"
    cjs-module-lexer "^0.6.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^15.4.1"

jest-serializer@^26.6.2:
  version "26.6.2"
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-serializer@^27.5.1:
  version "27.5.1"
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.9"

jest-snapshot@^26.6.2:
  version "26.6.2"
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    chalk "^4.0.0"
    expect "^26.6.2"
    graceful-fs "^4.2.4"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    jest-haste-map "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    natural-compare "^1.4.0"
    pretty-format "^26.6.2"
    semver "^7.3.2"

jest-util@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    is-ci "^2.0.0"
    micromatch "^4.0.2"

jest-util@^27.2.0:
  version "27.5.1"
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-util@^27.5.1:
  version "27.5.1"
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-util@^29.7.0:
  version "29.7.0"
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^26.5.2, jest-validate@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    camelcase "^6.0.0"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    leven "^3.1.0"
    pretty-format "^26.6.2"

jest-validate@^29.2.1:
  version "29.7.0"
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^26.6.2"
    string-length "^4.0.1"

jest-worker@^26.0.0, jest-worker@^26.6.2:
  version "26.6.2"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.2.0:
  version "27.5.1"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^27.5.1:
  version "27.5.1"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.7.0:
  version "29.7.0"
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^26.6.3:
  version "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    import-local "^3.0.2"
    jest-cli "^26.6.3"

jetifier@^1.6.2:
  version "1.6.8"

joi@^17.2.1:
  version "17.13.3"
  dependencies:
    "@hapi/hoek" "^9.3.0"
    "@hapi/topo" "^5.1.0"
    "@sideway/address" "^4.1.5"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"

js-yaml@^3.13.1:
  version "3.14.1"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsc-android@^250230.2.1:
  version "250230.2.1"

jsc-safe-url@^0.2.2:
  version "0.2.4"

jscodeshift@^0.13.1:
  version "0.13.1"
  dependencies:
    "@babel/core" "^7.13.16"
    "@babel/parser" "^7.13.16"
    "@babel/plugin-proposal-class-properties" "^7.13.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.13.8"
    "@babel/plugin-proposal-optional-chaining" "^7.13.12"
    "@babel/plugin-transform-modules-commonjs" "^7.13.8"
    "@babel/preset-flow" "^7.13.13"
    "@babel/preset-typescript" "^7.13.0"
    "@babel/register" "^7.13.16"
    babel-core "^7.0.0-bridge.0"
    chalk "^4.1.2"
    flow-parser "0.*"
    graceful-fs "^4.2.4"
    micromatch "^3.1.10"
    neo-async "^2.5.0"
    node-dir "^0.1.17"
    recast "^0.20.4"
    temp "^0.8.4"
    write-file-atomic "^2.3.0"

jsdom@^16.4.0:
  version "16.7.0"
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^3.0.2, jsesc@~3.1.0:
  version "3.1.0"

json-buffer@3.0.1:
  version "3.0.1"

json-parse-better-errors@^1.0.1:
  version "1.0.2"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"

json-schema-traverse@^0.4.1:
  version "0.4.1"

json-schema-traverse@^1.0.0:
  version "1.0.0"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"

json-stable-stringify@^1.0.2:
  version "1.3.0"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    isarray "^2.0.5"
    jsonify "^0.0.1"
    object-keys "^1.1.1"

json5@^1.0.2:
  version "1.0.2"
  dependencies:
    minimist "^1.2.0"

json5@^2.2.3:
  version "2.2.3"

jsonfile@^2.1.0:
  version "2.4.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.2.0"
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@^0.0.1:
  version "0.0.1"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.5:
  version "3.3.5"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keymirror@^0.1.1:
  version "0.1.1"

keyv@^4.5.3:
  version "4.5.4"
  dependencies:
    json-buffer "3.0.1"

kind-of@^2.0.1:
  version "2.0.1"
  dependencies:
    is-buffer "^1.0.2"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"

kind-of@^6.0.0:
  version "6.0.3"

kind-of@^6.0.1:
  version "6.0.3"

kind-of@^6.0.2:
  version "6.0.3"

klaw-sync@^6.0.0:
  version "6.0.0"
  dependencies:
    graceful-fs "^4.1.11"

klaw@^1.0.0:
  version "1.3.1"
  optionalDependencies:
    graceful-fs "^4.1.9"

kleur@^3.0.3:
  version "3.0.3"

language-subtag-registry@^0.3.20:
  version "0.3.23"

language-tags@^1.0.9:
  version "1.0.9"
  dependencies:
    language-subtag-registry "^0.3.20"

lazy-cache@^0.2.3:
  version "0.2.7"

lazy-cache@^1.0.3:
  version "1.0.4"

leven@^3.1.0:
  version "3.1.0"

levn@^0.4.1:
  version "0.4.1"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"

locate-path@^3.0.0:
  version "3.0.0"
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  dependencies:
    p-locate "^4.1.0"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"

lodash.frompairs@^4.0.1:
  version "4.0.1"

lodash.isequal@^4.5.0:
  version "4.5.0"

lodash.isstring@^4.0.1:
  version "4.0.1"

lodash.merge@^4.6.2:
  version "4.6.2"

lodash.omit@^4.5.0:
  version "4.5.0"

lodash.pick@^4.4.0:
  version "4.4.0"

lodash.template@^4.5.0:
  version "4.5.0"
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.templatesettings "^4.0.0"

lodash.templatesettings@^4.0.0:
  version "4.2.0"
  dependencies:
    lodash._reinterpolate "^3.0.0"

lodash.throttle@^4.1.1:
  version "4.1.1"

lodash.toarray@^4.4.0:
  version "4.4.0"

lodash.truncate@^4.4.2:
  version "4.4.2"

lodash@^4.17.10, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.7.0:
  version "4.17.21"

log-symbols@^2.2.0:
  version "2.2.0"
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.1.0:
  version "4.1.0"
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

logkitty@^0.7.1:
  version "0.7.1"
  dependencies:
    ansi-fragments "^0.2.1"
    dayjs "^1.8.15"
    yargs "^15.1.0"

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^5.1.1:
  version "5.1.1"
  dependencies:
    yallist "^3.0.2"

lru-cache@^7.14.1:
  version "7.18.3"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^4.0.0:
  version "4.0.0"
  dependencies:
    semver "^7.5.3"

makeerror@1.0.12:
  version "1.0.12"
  dependencies:
    tmpl "1.0.5"

map-cache@^0.2.2:
  version "0.2.2"

map-visit@^1.0.0:
  version "1.0.0"
  dependencies:
    object-visit "^1.0.0"

math-intrinsics@^1.1.0:
  version "1.1.0"

mdn-data@2.0.14:
  version "2.0.14"

mdn-data@2.0.4:
  version "2.0.4"

merge-deep@^3.0.2:
  version "3.0.3"
  dependencies:
    arr-union "^3.1.0"
    clone-deep "^0.2.4"
    kind-of "^3.0.2"

merge-options@^3.0.4:
  version "3.0.4"
  dependencies:
    is-plain-obj "^2.1.0"

merge-stream@^2.0.0:
  version "2.0.0"

methods@^1.1.2:
  version "1.1.2"

metro-babel-transformer@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/core" "^7.14.0"
    hermes-parser "0.5.0"
    metro-source-map "0.67.0"
    nullthrows "^1.1.1"

metro-babel-transformer@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/core" "^7.20.0"
    hermes-parser "0.12.0"
    nullthrows "^1.1.1"

metro-cache-key@0.67.0:
  version "0.67.0"

metro-cache-key@0.76.9:
  version "0.76.9"

metro-cache@0.67.0:
  version "0.67.0"
  dependencies:
    metro-core "0.67.0"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"

metro-cache@0.76.9:
  version "0.76.9"
  dependencies:
    metro-core "0.76.9"
    rimraf "^3.0.2"

metro-config@^0.67.0:
  version "0.67.0"
  dependencies:
    cosmiconfig "^5.0.5"
    jest-validate "^26.5.2"
    metro "0.67.0"
    metro-cache "0.67.0"
    metro-core "0.67.0"
    metro-runtime "0.67.0"

metro-config@^0.76.7, metro-config@0.76.9:
  version "0.76.9"
  dependencies:
    connect "^3.6.5"
    cosmiconfig "^5.0.5"
    jest-validate "^29.2.1"
    metro "0.76.9"
    metro-cache "0.76.9"
    metro-core "0.76.9"
    metro-runtime "0.76.9"

metro-config@0.67.0:
  version "0.67.0"
  dependencies:
    cosmiconfig "^5.0.5"
    jest-validate "^26.5.2"
    metro "0.67.0"
    metro-cache "0.67.0"
    metro-core "0.67.0"
    metro-runtime "0.67.0"

metro-core@^0.67.0, metro-core@0.67.0:
  version "0.67.0"
  dependencies:
    jest-haste-map "^27.3.1"
    lodash.throttle "^4.1.1"
    metro-resolver "0.67.0"

metro-core@0.76.9:
  version "0.76.9"
  dependencies:
    lodash.throttle "^4.1.1"
    metro-resolver "0.76.9"

metro-file-map@0.76.9:
  version "0.76.9"
  dependencies:
    anymatch "^3.0.3"
    debug "^2.2.0"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    invariant "^2.2.4"
    jest-regex-util "^27.0.6"
    jest-util "^27.2.0"
    jest-worker "^27.2.0"
    micromatch "^4.0.4"
    node-abort-controller "^3.1.1"
    nullthrows "^1.1.1"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.3.2"

metro-hermes-compiler@0.67.0:
  version "0.67.0"

metro-inspector-proxy@0.67.0:
  version "0.67.0"
  dependencies:
    connect "^3.6.5"
    debug "^2.2.0"
    ws "^7.5.1"
    yargs "^15.3.1"

metro-inspector-proxy@0.76.9:
  version "0.76.9"
  dependencies:
    connect "^3.6.5"
    debug "^2.2.0"
    node-fetch "^2.2.0"
    ws "^7.5.1"
    yargs "^17.6.2"

metro-minify-terser@0.76.9:
  version "0.76.9"
  dependencies:
    terser "^5.15.0"

metro-minify-uglify@0.67.0:
  version "0.67.0"
  dependencies:
    uglify-es "^3.1.9"

metro-minify-uglify@0.76.9:
  version "0.76.9"
  dependencies:
    uglify-es "^3.1.9"

metro-react-native-babel-preset@^0.65.1:
  version "0.65.2"
  dependencies:
    "@babel/core" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    react-refresh "^0.4.0"

metro-react-native-babel-preset@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    react-refresh "^0.4.0"

metro-react-native-babel-preset@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/core" "^7.20.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.18.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.18.0"
    "@babel/plugin-proposal-numeric-separator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.20.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.20.0"
    "@babel/plugin-syntax-dynamic-import" "^7.8.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.18.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.20.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.20.0"
    "@babel/plugin-transform-flow-strip-types" "^7.20.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    babel-plugin-transform-flow-enums "^0.0.2"
    react-refresh "^0.4.0"

metro-react-native-babel-transformer@^0.67.0, metro-react-native-babel-transformer@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/core" "^7.14.0"
    babel-preset-fbjs "^3.4.0"
    hermes-parser "0.5.0"
    metro-babel-transformer "0.67.0"
    metro-react-native-babel-preset "0.67.0"
    metro-source-map "0.67.0"
    nullthrows "^1.1.1"

metro-resolver@^0.67.0, metro-resolver@0.67.0:
  version "0.67.0"
  dependencies:
    absolute-path "^0.0.0"

metro-resolver@0.76.9:
  version "0.76.9"

metro-runtime@^0.67.0, metro-runtime@0.67.0:
  version "0.67.0"

metro-runtime@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/runtime" "^7.0.0"
    react-refresh "^0.4.0"

metro-source-map@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    invariant "^2.2.4"
    metro-symbolicate "0.67.0"
    nullthrows "^1.1.1"
    ob1 "0.67.0"
    source-map "^0.5.6"
    vlq "^1.0.0"

metro-source-map@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/traverse" "^7.20.0"
    "@babel/types" "^7.20.0"
    invariant "^2.2.4"
    metro-symbolicate "0.76.9"
    nullthrows "^1.1.1"
    ob1 "0.76.9"
    source-map "^0.5.6"
    vlq "^1.0.0"

metro-symbolicate@0.67.0:
  version "0.67.0"
  dependencies:
    invariant "^2.2.4"
    metro-source-map "0.67.0"
    nullthrows "^1.1.1"
    source-map "^0.5.6"
    through2 "^2.0.1"
    vlq "^1.0.0"

metro-symbolicate@0.76.9:
  version "0.76.9"
  dependencies:
    invariant "^2.2.4"
    metro-source-map "0.76.9"
    nullthrows "^1.1.1"
    source-map "^0.5.6"
    through2 "^2.0.1"
    vlq "^1.0.0"

metro-transform-plugins@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    nullthrows "^1.1.1"

metro-transform-plugins@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/core" "^7.20.0"
    "@babel/generator" "^7.20.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.20.0"
    nullthrows "^1.1.1"

metro-transform-worker@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/types" "^7.0.0"
    babel-preset-fbjs "^3.4.0"
    metro "0.67.0"
    metro-babel-transformer "0.67.0"
    metro-cache "0.67.0"
    metro-cache-key "0.67.0"
    metro-hermes-compiler "0.67.0"
    metro-source-map "0.67.0"
    metro-transform-plugins "0.67.0"
    nullthrows "^1.1.1"

metro-transform-worker@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/core" "^7.20.0"
    "@babel/generator" "^7.20.0"
    "@babel/parser" "^7.20.0"
    "@babel/types" "^7.20.0"
    babel-preset-fbjs "^3.4.0"
    metro "0.76.9"
    metro-babel-transformer "0.76.9"
    metro-cache "0.76.9"
    metro-cache-key "0.76.9"
    metro-minify-terser "0.76.9"
    metro-source-map "0.76.9"
    metro-transform-plugins "0.76.9"
    nullthrows "^1.1.1"

metro@^0.67.0, metro@0.67.0:
  version "0.67.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    absolute-path "^0.0.0"
    accepts "^1.3.7"
    async "^2.4.0"
    chalk "^4.0.0"
    ci-info "^2.0.0"
    connect "^3.6.5"
    debug "^2.2.0"
    denodeify "^1.2.1"
    error-stack-parser "^2.0.6"
    fs-extra "^1.0.0"
    graceful-fs "^4.1.3"
    hermes-parser "0.5.0"
    image-size "^0.6.0"
    invariant "^2.2.4"
    jest-haste-map "^27.3.1"
    jest-worker "^26.0.0"
    lodash.throttle "^4.1.1"
    metro-babel-transformer "0.67.0"
    metro-cache "0.67.0"
    metro-cache-key "0.67.0"
    metro-config "0.67.0"
    metro-core "0.67.0"
    metro-hermes-compiler "0.67.0"
    metro-inspector-proxy "0.67.0"
    metro-minify-uglify "0.67.0"
    metro-react-native-babel-preset "0.67.0"
    metro-resolver "0.67.0"
    metro-runtime "0.67.0"
    metro-source-map "0.67.0"
    metro-symbolicate "0.67.0"
    metro-transform-plugins "0.67.0"
    metro-transform-worker "0.67.0"
    mime-types "^2.1.27"
    mkdirp "^0.5.1"
    node-fetch "^2.2.0"
    nullthrows "^1.1.1"
    rimraf "^2.5.4"
    serialize-error "^2.1.0"
    source-map "^0.5.6"
    strip-ansi "^6.0.0"
    temp "0.8.3"
    throat "^5.0.0"
    ws "^7.5.1"
    yargs "^15.3.1"

metro@0.76.9:
  version "0.76.9"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.20.0"
    "@babel/generator" "^7.20.0"
    "@babel/parser" "^7.20.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.20.0"
    "@babel/types" "^7.20.0"
    accepts "^1.3.7"
    async "^3.2.2"
    chalk "^4.0.0"
    ci-info "^2.0.0"
    connect "^3.6.5"
    debug "^2.2.0"
    denodeify "^1.2.1"
    error-stack-parser "^2.0.6"
    graceful-fs "^4.2.4"
    hermes-parser "0.12.0"
    image-size "^1.0.2"
    invariant "^2.2.4"
    jest-worker "^27.2.0"
    jsc-safe-url "^0.2.2"
    lodash.throttle "^4.1.1"
    metro-babel-transformer "0.76.9"
    metro-cache "0.76.9"
    metro-cache-key "0.76.9"
    metro-config "0.76.9"
    metro-core "0.76.9"
    metro-file-map "0.76.9"
    metro-inspector-proxy "0.76.9"
    metro-minify-uglify "0.76.9"
    metro-react-native-babel-preset "0.76.9"
    metro-resolver "0.76.9"
    metro-runtime "0.76.9"
    metro-source-map "0.76.9"
    metro-symbolicate "0.76.9"
    metro-transform-plugins "0.76.9"
    metro-transform-worker "0.76.9"
    mime-types "^2.1.27"
    node-fetch "^2.2.0"
    nullthrows "^1.1.1"
    rimraf "^3.0.2"
    serialize-error "^2.1.0"
    source-map "^0.5.6"
    strip-ansi "^6.0.0"
    throat "^5.0.0"
    ws "^7.5.1"
    yargs "^17.6.2"

micromatch@^3.1.10:
  version "3.1.10"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^3.1.4:
  version "3.1.10"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.8"
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

"mime-db@>= 1.43.0 < 2":
  version "1.54.0"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@^2.1.35, mime-types@~2.1.34:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mime@^2.4.1, mime@^2.4.4, mime@2.6.0:
  version "2.6.0"

mime@1.6.0:
  version "1.6.0"

mimic-fn@^1.0.0:
  version "1.2.0"

mimic-fn@^2.1.0:
  version "2.1.0"

minimatch@^3.0.2, minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"

mixin-deep@^1.2.0:
  version "1.3.2"
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mixin-object@^2.0.1:
  version "2.0.1"
  dependencies:
    for-in "^0.1.3"
    is-extendable "^0.1.1"

mkdirp@^0.5.1, mkdirp@~0.5.1:
  version "0.5.6"
  dependencies:
    minimist "^1.2.6"

modal-react-native-web@0.2.0:
  version "0.2.0"
  dependencies:
    warning "^4.0.1"

moment@^2.22.0, moment@^2.29.4:
  version "2.30.1"

ms@^2.1.1, ms@^2.1.3, ms@2.1.3:
  version "2.1.3"

ms@2.0.0:
  version "2.0.0"

mute-stream@0.0.8:
  version "0.0.8"

nanoid@^3.1.15:
  version "3.3.11"

nanomatch@^1.2.9:
  version "1.2.13"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"

negotiator@~0.6.4:
  version "0.6.4"

negotiator@0.6.3:
  version "0.6.3"

neo-async@^2.5.0:
  version "2.6.2"

netmask@^2.0.2:
  version "2.0.2"

nice-try@^1.0.4:
  version "1.0.5"

nocache@^2.1.0:
  version "2.1.0"

node-abort-controller@^3.1.1:
  version "3.1.1"

node-dir@^0.1.17:
  version "0.1.17"
  dependencies:
    minimatch "^3.0.2"

node-emoji@1.10.0:
  version "1.10.0"
  dependencies:
    lodash.toarray "^4.4.0"

node-fetch@^1.0.1:
  version "1.7.3"
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

node-fetch@^2.2.0, node-fetch@^2.6.0, node-fetch@^2.7.0:
  version "2.7.0"
  dependencies:
    whatwg-url "^5.0.0"

node-int64@^0.4.0:
  version "0.4.0"

node-notifier@^8.0.0:
  version "8.0.2"
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.2.0"
    semver "^7.3.2"
    shellwords "^0.1.1"
    uuid "^8.3.0"
    which "^2.0.2"

node-releases@^2.0.21:
  version "2.0.23"

node-stream-zip@^1.9.1:
  version "1.15.0"

normalize-package-data@^2.5.0:
  version "2.5.0"
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0:
  version "3.0.0"

npm-run-path@^2.0.0:
  version "2.0.2"
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  dependencies:
    boolbase "^1.0.0"

nullthrows@^1.1.1:
  version "1.1.1"

nwsapi@^2.2.0:
  version "2.2.22"

ob1@0.67.0:
  version "0.67.0"

ob1@0.76.9:
  version "0.76.9"

object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"

object-copy@^0.1.0:
  version "0.1.0"
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"

object-keys@^1.1.1:
  version "1.1.1"

object-visit@^1.0.0:
  version "1.0.1"
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.2, object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.2, object.entries@^1.1.9:
  version "1.1.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.getownpropertydescriptors@^2.1.0:
  version "2.1.8"
  dependencies:
    array.prototype.reduce "^1.0.6"
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    gopd "^1.0.1"
    safe-array-concat "^1.1.2"

object.groupby@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.pick@^1.3.0:
  version "1.3.0"
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0, object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

on-finished@~2.3.0:
  version "2.3.0"
  dependencies:
    ee-first "1.1.1"

on-finished@2.4.1:
  version "2.4.1"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.1.0:
  version "1.1.0"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  dependencies:
    mimic-fn "^2.1.0"

open@^6.2.0:
  version "6.4.0"
  dependencies:
    is-wsl "^1.1.0"

open@^7.4.2:
  version "7.4.2"
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

opencollective-postinstall@^2.0.3:
  version "2.0.3"

optionator@^0.9.1:
  version "0.9.4"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^3.4.0:
  version "3.4.0"
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

ora@^5.4.1:
  version "5.4.1"
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@^1.0.0:
  version "1.0.2"

own-keys@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-each-series@^2.1.0:
  version "2.2.0"

p-finally@^1.0.0:
  version "1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  dependencies:
    p-limit "^2.2.0"

p-try@^2.0.0:
  version "2.2.0"

pac-proxy-agent@^7.1.0:
  version "7.2.0"
  dependencies:
    "@tootallnate/quickjs-emscripten" "^0.23.0"
    agent-base "^7.1.2"
    debug "^4.3.4"
    get-uri "^6.0.1"
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.6"
    pac-resolver "^7.0.1"
    socks-proxy-agent "^8.0.5"

pac-resolver@^7.0.1:
  version "7.0.1"
  dependencies:
    degenerator "^5.0.0"
    netmask "^2.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@6.0.1:
  version "6.0.1"

parseqs@0.0.6:
  version "0.0.6"

parseuri@0.0.6:
  version "0.0.6"

parseurl@~1.3.3:
  version "1.3.3"

pascalcase@^0.1.1:
  version "0.1.1"

patch-package@^8.0.1:
  version "8.0.1"
  dependencies:
    "@yarnpkg/lockfile" "^1.1.0"
    chalk "^4.1.2"
    ci-info "^3.7.0"
    cross-spawn "^7.0.3"
    find-yarn-workspace-root "^2.0.0"
    fs-extra "^10.0.0"
    json-stable-stringify "^1.0.2"
    klaw-sync "^6.0.0"
    minimist "^1.2.6"
    open "^7.4.2"
    semver "^7.5.3"
    slash "^2.0.0"
    tmp "^0.2.4"
    yaml "^2.2.2"

path-dirname@^1.0.2:
  version "1.0.2"

path-exists@^3.0.0:
  version "3.0.0"

path-exists@^4.0.0:
  version "4.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^2.0.0:
  version "2.0.1"

path-key@^2.0.1:
  version "2.0.1"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"

path-parse@^1.0.7:
  version "1.0.7"

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"

picomatch@^2.0.4:
  version "2.3.1"

picomatch@^2.2.3:
  version "2.3.1"

picomatch@^2.3.1:
  version "2.3.1"

pify@^4.0.1:
  version "4.0.1"

pirates@^4.0.1, pirates@^4.0.4, pirates@^4.0.6:
  version "4.0.7"

pkg-dir@^3.0.0:
  version "3.0.0"
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  dependencies:
    find-up "^4.0.0"

plist@^3.0.2, plist@^3.0.4, plist@^3.0.5:
  version "3.1.0"
  dependencies:
    "@xmldom/xmldom" "^0.8.8"
    base64-js "^1.5.1"
    xmlbuilder "^15.1.1"

posix-character-classes@^0.1.0:
  version "0.1.1"

possible-typed-array-names@^1.0.0:
  version "1.1.0"

postinstall-postinstall@^2.1.0:
  version "2.1.0"

prelude-ls@^1.2.1:
  version "1.2.1"

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.0.2:
  version "2.8.8"

pretty-format@^26.5.2, pretty-format@^26.6.2:
  version "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^17.0.1"

pretty-format@^29.7.0:
  version "29.7.0"
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"

progress@^2.0.0:
  version "2.0.3"

promise@^7.1.1:
  version "7.3.1"
  dependencies:
    asap "~2.0.3"

promise@^8.2.0:
  version "8.3.0"
  dependencies:
    asap "~2.0.6"

prompts@^2.0.1, prompts@^2.4.0:
  version "2.4.2"
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@*, prop-types@^15.5.10, prop-types@^15.5.7, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

prop-types@15.5.10:
  version "15.5.10"
  dependencies:
    fbjs "^0.8.9"
    loose-envify "^1.3.1"

prop-types@15.7.2:
  version "15.7.2"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

proxy-agent@^6.3.0:
  version "6.5.0"
  dependencies:
    agent-base "^7.1.2"
    debug "^4.3.4"
    http-proxy-agent "^7.0.1"
    https-proxy-agent "^7.0.6"
    lru-cache "^7.14.1"
    pac-proxy-agent "^7.1.0"
    proxy-from-env "^1.1.0"
    socks-proxy-agent "^8.0.5"

proxy-from-env@^1.1.0:
  version "1.1.0"

psl@^1.1.33:
  version "1.15.0"
  dependencies:
    punycode "^2.3.1"

pump@^3.0.0:
  version "3.0.3"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"

q@^1.1.2:
  version "1.5.1"

qs@^6.11.0, qs@^6.7.0:
  version "6.14.0"
  dependencies:
    side-channel "^1.1.0"

query-string@^6.13.6:
  version "6.14.1"
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystringify@^2.1.1:
  version "2.2.0"

queue@6.0.2:
  version "6.0.2"
  dependencies:
    inherits "~2.0.3"

range-parser@~1.2.1:
  version "1.2.1"

react-addons-shallow-compare@15.6.2:
  version "15.6.2"
  dependencies:
    fbjs "^0.8.4"
    object-assign "^4.1.0"

react-async-hook@3.6.1:
  version "3.6.1"

react-devtools-core@^4.23.0:
  version "4.28.5"
  dependencies:
    shell-quote "^1.6.1"
    ws "^7"

"react-is@^16.12.0 || ^17.0.0":
  version "17.0.2"

react-is@^16.13.0, react-is@^16.13.1, react-is@^16.7.0, react-is@^16.8.1, react-is@^16.8.6:
  version "16.13.1"

react-is@^17.0.1:
  version "17.0.2"

react-is@^17.0.2:
  version "17.0.2"

react-is@^18.0.0:
  version "18.3.1"

react-lifecycles-compat@^3.0.4:
  version "3.0.4"

react-native-action-button@^2.8.5:
  version "2.8.5"
  dependencies:
    prop-types "^15.5.10"

react-native-animatable@^1.3.3:
  version "1.4.0"
  dependencies:
    prop-types "^15.8.1"

react-native-app-intro-slider@^4.0.4:
  version "4.0.4"

react-native-background-timer@^2.4.1:
  version "2.4.1"

react-native-ble-manager@10.1.5:
  version "10.1.5"

react-native-bluetooth-state-manager@1.3.5:
  version "1.3.5"

react-native-camera@3.40.0:
  version "3.40.0"
  dependencies:
    prop-types "^15.6.2"

react-native-card-stack-swiper@^1.2.5:
  version "1.2.5"
  dependencies:
    prop-types "15.5.10"
    react-lifecycles-compat "^3.0.4"

react-native-carplay@^2.3.0:
  version "2.3.0"
  dependencies:
    traverse "^0.6.6"

react-native-code-push@^7.0.1:
  version "7.1.0"
  dependencies:
    code-push "^4.1.0"
    glob "^7.1.7"
    hoist-non-react-statics "^3.3.2"
    inquirer "^8.1.5"
    plist "^3.0.4"
    semver "^7.3.5"
    xcode "3.0.1"

react-native-codegen@^0.0.18:
  version "0.0.18"
  dependencies:
    "@babel/parser" "^7.14.0"
    flow-parser "^0.121.0"
    jscodeshift "^0.13.1"
    nullthrows "^1.1.1"

react-native-config@^1.4.2:
  version "1.5.9"
  dependencies:
    "@babel/core" "^7.25.2"
    "@babel/preset-env" "^7.25.0"
    "@babel/preset-react" "^7.24.7"
    babel-jest "^29.7.0"

react-native-country-picker-modal@^2.0.0:
  version "2.0.0"
  dependencies:
    "@callstack/react-theme-provider" "3.0.3"
    fuse.js "3.4.5"
    modal-react-native-web "0.2.0"
    node-emoji "1.10.0"
    prop-types "15.7.2"
    react-async-hook "3.6.1"

react-native-datepicker@^1.7.2:
  version "1.7.2"
  dependencies:
    moment "^2.22.0"

react-native-deck-swiper@^2.0.5:
  version "2.0.19"
  dependencies:
    lodash "^4.17.21"
    prop-types "15.5.10"

react-native-device-info@^8.1.3:
  version "8.7.1"

react-native-event-listeners@^1.0.7:
  version "1.0.7"
  dependencies:
    type-detect "^4.0.8"

react-native-gesture-handler@^1.10.3:
  version "1.10.3"
  dependencies:
    "@egjs/hammerjs" "^2.0.17"
    fbjs "^3.0.0"
    hoist-non-react-statics "^3.3.0"
    invariant "^2.2.4"
    prop-types "^15.7.2"

react-native-get-location@^2.1.0:
  version "2.2.1"

react-native-gradle-plugin@^0.0.6:
  version "0.0.6"

react-native-image-picker@5.4.0:
  version "5.4.0"

react-native-image-slider-box@^1.0.12:
  version "1.1.24"
  dependencies:
    react-native-snap-carousel latest

react-native-in-app-notification@^3.1.0:
  version "3.2.0"
  dependencies:
    hoist-non-react-statics "^3.0.1"
    react-native-iphone-x-helper "^1.2.0"
    react-native-swipe-gestures "^1.0.2"

react-native-inappbrowser-reborn@^3.5.1:
  version "3.7.0"
  dependencies:
    invariant "^2.2.4"
    opencollective-postinstall "^2.0.3"

react-native-iphone-x-helper@^1.2.0, react-native-iphone-x-helper@^1.3.0, react-native-iphone-x-helper@^1.3.1:
  version "1.3.1"

react-native-linear-gradient@^2.5.6:
  version "2.8.3"

react-native-permissions@^2.0.2:
  version "2.2.2"

react-native-permissions@3.6.1:
  version "3.6.1"

react-native-progress@^4.1.2:
  version "4.1.2"
  dependencies:
    "@react-native-community/art" "^1.1.2"
    prop-types "^15.7.2"

react-native-push-notification@^7.3.1:
  version "7.4.0"

react-native-qrcode-scanner@^1.5.3:
  version "1.6.0"
  dependencies:
    "@react-native-community/async-storage" "^1.6.1"
    prop-types "^15.5.10"
    react-native-permissions "^2.0.2"

react-native-reanimated@^2.17.0:
  version "2.17.0"
  resolved "https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-2.17.0.tgz"
  integrity sha512-bVy+FUEaHXq4i+aPPqzGeor1rG4scgVNBbBz21ohvC7iMpB9IIgvGsmy1FAoodZhZ5sa3EPF67Rcec76F1PXlQ==
  dependencies:
    "@babel/plugin-transform-object-assign" "^7.16.7"
    "@babel/preset-typescript" "^7.16.7"
    invariant "^2.2.4"
    lodash.isequal "^4.5.0"
    setimmediate "^1.0.5"
    string-hash-64 "^1.0.3"

react-native-render-html@^5.1.0:
  version "5.1.1"
  dependencies:
    deprecated-prop-type "^1.0.0"
    htmlparser2 "5.0.1"
    prop-types "^15.7.2"
    stringify-entities "^3.1.0"

react-native-restart@^0.0.22:
  version "0.0.22"

react-native-safe-area-context@^3.1.9:
  version "3.4.1"

react-native-screens@^2.18.0:
  version "2.18.1"

react-native-share@^7.0.0:
  version "7.9.1"

react-native-simple-toast@^1.1.3:
  version "1.1.4"

react-native-snap-carousel@latest:
  version "3.9.1"
  resolved "https://registry.npmjs.org/react-native-snap-carousel/-/react-native-snap-carousel-3.9.1.tgz"
  integrity sha512-xWEGusacIgK1YaDXLi7Gao2+ISLoGPVEBR8fcMf4tOOJQufutlNwkoLu0l6B8Qgsrre0nTxoVZikRgGRDWlLaQ==
  dependencies:
    prop-types "^15.6.1"
    react-addons-shallow-compare "15.6.2"

react-native-splash-screen@^3.2.0:
  version "3.3.0"

react-native-svg-transformer@^0.14.3:
  version "0.14.3"
  dependencies:
    "@svgr/core" "^4.3.3"
    "@svgr/plugin-svgo" "^4.3.1"
    path-dirname "^1.0.2"
    semver "^5.6.0"

react-native-svg@^12.3.0:
  version "12.5.1"
  dependencies:
    css-select "^5.1.0"
    css-tree "^1.1.3"

react-native-swipe-gestures@^1.0.2:
  version "1.0.5"

react-native-switch@^2.0.0:
  version "2.0.0"
  dependencies:
    prop-types "^15.6.0"

react-native-vector-icons@^8.1.0:
  version "8.1.0"
  dependencies:
    lodash.frompairs "^4.0.1"
    lodash.isequal "^4.5.0"
    lodash.isstring "^4.0.1"
    lodash.omit "^4.5.0"
    lodash.pick "^4.4.0"
    lodash.template "^4.5.0"
    prop-types "^15.7.2"
    yargs "^16.1.1"

react-native-video@^5.2.1:
  version "5.2.2"
  dependencies:
    deprecated-react-native-prop-types "^2.2.0"
    keymirror "^0.1.1"
    prop-types "^15.7.2"
    shaka-player "^2.5.9"

react-native-webview@^11.4.0:
  version "11.26.1"
  dependencies:
    escape-string-regexp "2.0.0"
    invariant "2.2.4"

react-native@0.68.5:
  version "0.68.5"
  dependencies:
    "@jest/create-cache-key-function" "^27.0.1"
    "@react-native-community/cli" "^7.0.3"
    "@react-native-community/cli-platform-android" "^7.0.1"
    "@react-native-community/cli-platform-ios" "^7.0.1"
    "@react-native/assets" "1.0.0"
    "@react-native/normalize-color" "2.0.0"
    "@react-native/polyfills" "2.0.0"
    abort-controller "^3.0.0"
    anser "^1.4.9"
    base64-js "^1.1.2"
    deprecated-react-native-prop-types "^2.3.0"
    event-target-shim "^5.0.1"
    hermes-engine "~0.11.0"
    invariant "^2.2.4"
    jsc-android "^250230.2.1"
    metro-react-native-babel-transformer "0.67.0"
    metro-runtime "0.67.0"
    metro-source-map "0.67.0"
    nullthrows "^1.1.1"
    pretty-format "^26.5.2"
    promise "^8.2.0"
    react-devtools-core "^4.23.0"
    react-native-codegen "^0.0.18"
    react-native-gradle-plugin "^0.0.6"
    react-refresh "^0.4.0"
    react-shallow-renderer "16.14.1"
    regenerator-runtime "^0.13.2"
    scheduler "^0.20.2"
    stacktrace-parser "^0.1.3"
    use-subscription ">=1.0.0 <1.6.0"
    whatwg-fetch "^3.0.0"
    ws "^6.1.4"

react-reconciler@^0.33.0:
  version "0.33.0"
  dependencies:
    scheduler "^0.27.0"

react-redux@^7.2.2:
  version "7.2.9"
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/react-redux" "^7.1.20"
    hoist-non-react-statics "^3.3.2"
    loose-envify "^1.4.0"
    prop-types "^15.7.2"
    react-is "^17.0.2"

react-refresh@^0.4.0:
  version "0.4.3"

react-shallow-renderer@16.14.1:
  version "16.14.1"
  dependencies:
    object-assign "^4.1.1"
    react-is "^16.12.0 || ^17.0.0"

react-test-renderer@16.13.1:
  version "16.13.1"
  dependencies:
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    react-is "^16.8.6"
    scheduler "^0.19.1"

react@16.13.1:
  version "16.13.1"
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"

read-pkg-up@^7.0.1:
  version "7.0.1"
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^3.4.0:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readline@^1.3.0:
  version "1.3.0"

recast@^0.20.4:
  version "0.20.5"
  dependencies:
    ast-types "0.14.2"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tslib "^2.0.1"

recursive-fs@^2.1.0:
  version "2.1.0"

redux-persist@^6.0.0:
  version "6.0.0"

redux-thunk@^2.3.0:
  version "2.4.2"

redux@^4.0.0, redux@^4.0.5:
  version "4.2.1"
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.2:
  version "10.2.2"
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"

regenerator-runtime@^0.13.2:
  version "0.13.11"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpp@^3.0.0, regexpp@^3.1.0:
  version "3.2.0"

regexpu-core@^6.2.0:
  version "6.4.0"
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.2"
    regjsgen "^0.8.0"
    regjsparser "^0.13.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.2.1"

regjsgen@^0.8.0:
  version "0.8.0"

regjsparser@^0.13.0:
  version "0.13.0"
  dependencies:
    jsesc "~3.1.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"

repeat-element@^1.1.2:
  version "1.1.4"

repeat-string@^1.6.1:
  version "1.6.1"

require-directory@^2.1.1:
  version "2.1.1"

require-from-string@^2.0.2:
  version "2.0.2"

require-main-filename@^2.0.0:
  version "2.0.0"

requires-port@^1.0.0:
  version "1.0.0"

resolve-cwd@^3.0.0:
  version "3.0.0"
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"

resolve-from@^4.0.0:
  version "4.0.0"

resolve-from@^5.0.0:
  version "5.0.0"

resolve-url@^0.2.1:
  version "0.2.1"

resolve@^1.10.0, resolve@^1.12.0, resolve@^1.18.1, resolve@^1.22.10, resolve@^1.22.4:
  version "1.22.10"
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"

rimraf@^2.5.4:
  version "2.7.1"
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  dependencies:
    glob "^7.1.3"

rimraf@~2.2.6:
  version "2.2.8"

rimraf@~2.6.2:
  version "2.6.3"
  dependencies:
    glob "^7.1.3"

rn-fetch-blob@^0.12.0:
  version "0.12.0"
  dependencies:
    base-64 "0.1.0"
    glob "7.0.6"

rsvp@^4.8.4:
  version "4.8.5"

run-async@^2.4.0:
  version "2.4.1"

rxjs@^7.5.5:
  version "7.8.2"
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.2, safe-array-concat@^1.1.3:
  version "1.1.3"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.2.1, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"

safe-buffer@~5.1.0:
  version "5.1.2"

safe-buffer@~5.1.1:
  version "5.1.2"

safe-push-apply@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.0.3, safe-regex-test@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-regex@^1.1.0:
  version "1.1.0"
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"

sails.io.js@^1.2.1:
  version "1.2.1"

sane@^4.0.3:
  version "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sax@^1.2.4:
  version "1.4.1"

sax@~1.2.4:
  version "1.2.4"

saxes@^5.0.1:
  version "5.0.1"
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.19.1:
  version "0.19.1"
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.20.2:
  version "0.20.2"
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.27.0:
  version "0.27.0"

semver@^5.5.0:
  version "5.7.2"

semver@^5.6.0:
  version "5.7.2"

semver@^6.1.1, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"

semver@^7.2.1:
  version "7.7.3"

semver@^7.3.2:
  version "7.7.3"

semver@^7.3.5:
  version "7.7.3"

semver@^7.3.8:
  version "7.7.3"

semver@^7.5.3:
  version "7.7.3"

"semver@2 || 3 || 4 || 5":
  version "5.7.2"

send@0.19.0:
  version "0.19.0"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-error@^2.1.0:
  version "2.1.0"

serve-static@^1.13.1:
  version "1.16.2"
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-blocking@^2.0.0:
  version "2.0.0"

set-function-length@^1.2.2:
  version "1.2.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.5:
  version "1.0.5"

setprototypeof@1.2.0:
  version "1.2.0"

shaka-player@^2.5.9:
  version "2.5.23"
  dependencies:
    eme-encryption-scheme-polyfill "^2.0.1"

shallow-clone@^0.1.2:
  version "0.1.2"
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^2.0.1"
    lazy-cache "^0.2.3"
    mixin-object "^2.0.1"

shallow-clone@^1.0.0:
  version "1.0.0"
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^5.0.0"
    mixin-object "^2.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

shell-quote@^1.6.1, shell-quote@^1.7.3:
  version "1.8.3"

shellwords@^0.1.1:
  version "0.1.1"

side-channel-list@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"

simple-plist@^1.1.0:
  version "1.4.0"
  dependencies:
    bplist-creator "0.1.1"
    bplist-parser "0.3.2"
    plist "^3.0.5"

simple-swizzle@^0.2.2:
  version "0.2.4"
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"

slash@^2.0.0:
  version "2.0.0"

slash@^3.0.0:
  version "3.0.0"

slice-ansi@^2.0.0:
  version "2.1.0"
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

smart-buffer@^4.2.0:
  version "4.2.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socket.io-client@^2.0.3:
  version "2.5.0"
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "~1.3.0"
    debug "~3.1.0"
    engine.io-client "~3.5.0"
    has-binary2 "~1.0.2"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    socket.io-parser "~3.3.0"
    to-array "0.1.4"

socket.io-parser@~3.3.0:
  version "3.3.4"
  dependencies:
    component-emitter "~1.3.0"
    debug "~3.1.0"
    isarray "2.0.1"

socks-proxy-agent@^8.0.5:
  version "8.0.5"
  dependencies:
    agent-base "^7.1.2"
    debug "^4.3.4"
    socks "^2.8.3"

socks@^2.8.3:
  version "2.8.7"
  dependencies:
    ip-address "^10.0.1"
    smart-buffer "^4.2.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.16, source-map-support@^0.5.6, source-map-support@~0.5.20:
  version "0.5.21"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"

source-map@^0.5.6:
  version "0.5.7"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"

source-map@^0.7.3:
  version "0.7.6"

spdx-correct@^3.0.0:
  version "3.2.0"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.22"

split-on-first@^1.0.0:
  version "1.1.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"

stable@^0.1.8:
  version "0.1.8"

stack-generator@^2.0.3:
  version "2.0.10"
  dependencies:
    stackframe "^1.3.4"

stack-utils@^2.0.2:
  version "2.0.6"
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.3.4:
  version "1.3.4"

stacktrace-parser@^0.1.3:
  version "0.1.11"
  dependencies:
    type-fest "^0.7.1"

static-extend@^0.1.1:
  version "0.1.2"
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

statuses@~1.5.0:
  version "1.5.0"

statuses@2.0.1:
  version "2.0.1"

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

stream-buffers@2.2.x:
  version "2.2.0"

strict-uri-encode@^2.0.0:
  version "2.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  dependencies:
    safe-buffer "~5.1.0"

string-hash-64@^1.0.3:
  version "1.0.3"

string-length@^4.0.1:
  version "4.0.2"
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.includes@^2.0.1:
  version "2.0.1"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

stringify-entities@^3.1.0:
  version "3.1.0"
  dependencies:
    character-entities-html4 "^1.0.0"
    character-entities-legacy "^1.0.0"
    xtend "^4.0.0"

strip-ansi@^5.0.0:
  version "5.2.0"
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^5.2.0:
  version "5.2.0"
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"

strip-bom@^4.0.0:
  version "4.0.0"

strip-eof@^1.0.0:
  version "1.0.0"

strip-final-newline@^2.0.0:
  version "2.0.0"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"

sudo-prompt@^9.0.0:
  version "9.2.1"

superagent@^8.0.0:
  version "8.1.2"
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.4"
    debug "^4.3.4"
    fast-safe-stringify "^2.1.1"
    form-data "^4.0.0"
    formidable "^2.1.2"
    methods "^1.1.2"
    mime "2.6.0"
    qs "^6.11.0"
    semver "^7.3.8"

superagent@5.1.0:
  version "5.1.0"
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.2"
    debug "^4.1.1"
    fast-safe-stringify "^2.0.6"
    form-data "^2.3.3"
    formidable "^1.2.1"
    methods "^1.1.2"
    mime "^2.4.4"
    qs "^6.7.0"
    readable-stream "^3.4.0"
    semver "^6.1.1"

superstruct@^0.6.2:
  version "0.6.2"
  dependencies:
    clone-deep "^2.0.1"
    kind-of "^6.0.1"

supports-color@^5.3.0:
  version "5.5.0"
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.3.0"
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"

svg-parser@^2.0.0:
  version "2.0.4"

svgo@^1.2.2:
  version "1.3.2"
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

symbol-tree@^3.2.4:
  version "3.2.4"

table@^6.0.9:
  version "6.9.0"
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

temp@^0.8.4:
  version "0.8.4"
  dependencies:
    rimraf "~2.6.2"

temp@0.8.3:
  version "0.8.3"
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

terminal-link@^2.0.0:
  version "2.1.1"
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser@^5.15.0:
  version "5.44.0"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.15.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"

throat@^5.0.0:
  version "5.0.0"

through@^2.3.6:
  version "2.3.8"

through2@^2.0.1:
  version "2.0.5"
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

tmp@^0.2.4:
  version "0.2.5"

tmpl@1.0.5:
  version "1.0.5"

to-array@0.1.4:
  version "0.1.4"

to-object-path@^0.3.0:
  version "0.3.0"
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toggle-switch-react-native@^3.2.0:
  version "3.3.0"
  dependencies:
    prop-types "^15.5.7"

toidentifier@1.0.1:
  version "1.0.1"

tough-cookie@^4.0.0:
  version "4.1.4"
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^2.1.0:
  version "2.1.0"
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"

traverse@^0.6.6:
  version "0.6.11"
  dependencies:
    gopd "^1.2.0"
    typedarray.prototype.slice "^1.0.5"
    which-typed-array "^1.1.18"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"

tslib@^2.0.1, tslib@^2.1.0:
  version "2.8.1"

tsutils@^3.17.1:
  version "3.21.0"
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  dependencies:
    prelude-ls "^1.2.1"

type-detect@^4.0.8:
  version "4.1.0"

type-detect@4.0.8:
  version "4.0.8"

type-fest@^0.20.2:
  version "0.20.2"

type-fest@^0.21.3:
  version "0.21.3"

type-fest@^0.6.0:
  version "0.6.0"

type-fest@^0.7.1:
  version "0.7.1"

type-fest@^0.8.1:
  version "0.8.1"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  dependencies:
    is-typedarray "^1.0.0"

typedarray.prototype.slice@^1.0.5:
  version "1.0.5"
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    math-intrinsics "^1.1.0"
    typed-array-buffer "^1.0.3"
    typed-array-byte-offset "^1.0.4"

ua-parser-js@^0.7.30:
  version "0.7.41"

ua-parser-js@^1.0.35:
  version "1.0.41"

uglify-es@^3.1.9:
  version "3.3.10"
  dependencies:
    commander "~2.14.1"
    source-map "~0.6.1"

unbox-primitive@^1.1.0:
  version "1.1.0"
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~7.14.0:
  version "7.14.0"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.2.1:
  version "2.2.1"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.2.0"

union-value@^1.0.0:
  version "1.0.1"
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.0:
  version "0.1.2"

universalify@^0.2.0:
  version "0.2.0"

universalify@^2.0.0:
  version "2.0.1"

unpipe@~1.0.0:
  version "1.0.0"

unquote@~1.1.1:
  version "1.1.1"

unset-value@^1.0.0:
  version "1.0.0"
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"

url-parse@^1.4.7, url-parse@^1.5.3:
  version "1.5.10"
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

"use-subscription@>=1.0.0 <1.6.0":
  version "1.5.1"
  dependencies:
    object-assign "^4.1.1"

use@^3.1.0:
  version "3.1.1"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"

util.promisify@~1.0.0:
  version "1.0.1"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utils-merge@1.0.1:
  version "1.0.1"

uuid@^7.0.3:
  version "7.0.3"

uuid@^8.3.0:
  version "8.3.2"

v8-compile-cache@^2.0.3:
  version "2.4.0"

v8-to-istanbul@^7.0.0:
  version "7.1.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"

vlq@^1.0.0:
  version "1.0.1"

w3c-hr-time@^1.0.2:
  version "1.0.2"
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@^1.0.8, walker@~1.0.5:
  version "1.0.8"
  dependencies:
    makeerror "1.0.12"

warning@^4.0.1:
  version "4.0.3"
  dependencies:
    loose-envify "^1.0.0"

warning@4.0.1:
  version "4.0.1"
  dependencies:
    loose-envify "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"

webidl-conversions@^5.0.0:
  version "5.0.0"

webidl-conversions@^6.1.0:
  version "6.1.0"

whatwg-encoding@^1.0.5:
  version "1.0.5"
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@^3.0.0, whatwg-fetch@>=0.10.0:
  version "3.6.20"

whatwg-mimetype@^2.3.0:
  version "2.3.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-module@^2.0.0:
  version "2.0.1"

which-typed-array@^1.1.16, which-typed-array@^1.1.18, which-typed-array@^1.1.19:
  version "1.1.19"
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^1.2.9:
  version "1.3.1"
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"

wrap-ansi@^6.0.1:
  version "6.2.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"

write-file-atomic@^2.3.0:
  version "2.4.3"
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^3.0.0:
  version "3.0.3"
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write-file-atomic@^4.0.2:
  version "4.0.2"
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@^6.1.4:
  version "6.2.3"
  dependencies:
    async-limiter "~1.0.0"

ws@^7, ws@^7.4.6, ws@^7.5.1, ws@~7.5.10:
  version "7.5.10"

xcode@^3.0.0, xcode@3.0.1:
  version "3.0.1"
  dependencies:
    simple-plist "^1.1.0"
    uuid "^7.0.3"

xml-name-validator@^3.0.0:
  version "3.0.0"

xmlbuilder@^15.1.1:
  version "15.1.1"

xmlchars@^2.2.0:
  version "2.2.0"

xmldoc@^1.1.2:
  version "1.3.0"
  dependencies:
    sax "^1.2.4"

xmlhttprequest-ssl@~1.6.2:
  version "1.6.3"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"

y18n@^4.0.0:
  version "4.0.3"

y18n@^5.0.5:
  version "5.0.8"

yallist@^3.0.2:
  version "3.1.1"

yaml@^2.2.2:
  version "2.8.1"

yargs-parser@^18.1.2:
  version "18.1.3"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2:
  version "20.2.9"

yargs-parser@^21.1.1:
  version "21.1.1"

yargs@^15.1.0, yargs@^15.3.1, yargs@^15.4.1:
  version "15.4.1"
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.1.1:
  version "16.2.0"
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.6.2:
  version "17.7.2"
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yazl@^2.5.1:
  version "2.5.1"
  dependencies:
    buffer-crc32 "~0.2.3"

yeast@0.1.2:
  version "0.1.2"
